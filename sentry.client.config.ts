import * as Sentry from '@sentry/nuxt';
const config = useAppConfig();

Sentry.init({
	dsn: 'https://<EMAIL>/4506546590711808',
	environment: process.env.NODE_ENV == 'development' ? 'local' : 'production',
	release: process.env.NODE_ENV == 'development' ? `core ${config.version}` : `core ${config.version}, build ${import.meta.env.VITE_BUILD_VERSION}`,
	integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
	ignoreErrors: [
		'fbq is not defined',
		"Cannot read properties of undefined (reading 'product_id')", // flix error
		'Cannot call methods on fl1xcarousel prior to initialization; attempted to call method "reload"', // flix error
		"undefined is not an object (evaluating 'opts.product_id')", // flix error
		'Cannot call methods on fl1xcarousel prior to initialization; attempted to call method "items"', // flix error
		'window.flixJsCallbacks.flixCartClick is not a function', // flix error
		"undefined is not an object (evaluating 'FlixServices.inpagedata[FlixServices.modular_match_data[i].product_meta.product_id]')", // flix error
		"null is not an object (evaluating 'FlixServices.modular_match_data[0]')", // flix error
		'opts2 is not defined', // flix error
		'FlixServices.modular_match_data.forEach is not a function', // flix error
		'flixtracking is undefined', // flix error
		'FlixjQ.fn.inPage is undefined', // flix error
		"Can't find variable: fbq",
		'$ is not defined',
		"Cannot read properties of null (reading 'off')", // @vueform/slider
		"null is not an object (evaluating 'k.value.off')", // @vueform/slider
		"expected expression, got '='", // 3d viewer
		'$ is not a function',
		"Can't find variable: $",
		'Could not load "onion".',
		'r is null',
		"Cannot read properties of null (reading 'image_upload_path')",
		'i is undefined',
		"Cannot read properties of null (reading 'parentNode')",
		'Load failed',
		'Event `CustomEvent` (type=unhandledrejection) captured as promise rejection',
		'Importing a module script failed.',
		"Cannot destructure property 'bum' of 'w' as it is null.", // vue error
		'Could not load "map".', // Google maps
		'Could not load "infowindow".', // Google maps
		'The Google Maps JavaScript API could not load.', // Google maps
		'Could not load "stats".', // Google maps
		'Unable to preload CSS',
		'Load failed',
		'Could not load "marker".',
		'Could not load "util".',
		'/_nuxt/builds/meta/',
		'/node_modules/@sentry-internal/',
		'signal is aborted without reason', // sentry
		"null is not an object (evaluating 'J.value.off')", // @vueform/slider
	],

	// Error monitoring
	sampleRate: process.env.NODE_ENV == 'development' ? 0 : 0.2,
	replaysOnErrorSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0.1, // Screen capture

	// Performance monitoring
	tracesSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0,
	replaysSessionSampleRate: 0, // Screen capture
});
