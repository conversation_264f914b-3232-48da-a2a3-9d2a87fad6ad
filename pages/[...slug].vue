<template>
	<div class="nuxt-page-container">
		<component :is="component" />
	</div>
</template>

<script setup>
	const nuxtApp = useNuxtApp();

	// Import project template files in order to load custom (non-base) templates
	const templateFiles = import.meta.glob('@/components/views/**/*.vue');
	const templates = new Map(
		Object.entries(templateFiles).map(([path, loader]) => [
			path
				.replace('/components/views/', '')
				.replace('.vue', '')
				.split('/')
				.map(el => el.charAt(0).toUpperCase() + el.slice(1))
				.filter(el => el !== 'Index')
				.join(''),
			loader,
		])
	);

	const component = computed(() => {
		const template = nuxtApp.$appGlobalData.template;

		// 404 Error template
		if (template === 'Error404') return resolveComponent('ViewsError404');

		// Newsletter
		if (__NEWSLETTER__ && template === 'NewsletterSubscribeConfirm') return resolveComponent('ViewsNewsletterSubscribeConfirm');
		if (__NEWSLETTER__ && template === 'NewsletterUnsubscribeConfirm') return resolveComponent('ViewsUnsubscribeConfirm');

		// CMS
		if (template === 'ErrorLoading') return resolveComponent('ViewsErrorLoading');
		if (template === 'CmsDefault') return resolveComponent('ViewsCmsDefault');
		if (template === 'CmsHomepage') return resolveComponent('ViewsCmsHomepage');

		// Catalog
		if (template === 'CatalogDetail') return resolveComponent('ViewsCatalogDetail');
		if (__CATALOG_CATEGORY_LANDING__ && template === 'CatalogCategoryLanding') return resolveComponent('ViewsCatalogCategoryLanding');

		// Tags
		if (_TAGS__ && template === 'Tag') return resolveComponent('ViewsTag');

		// Publish
		if (__PUBLISH__) {
			if (template == 'Publish') return resolveComponent('ViewsPublish');
			if (template == 'PublishDetail') return resolveComponent('ViewsPublishDetail');
		}
		if (__PUBLISH_AUTHORS__) {
			if (template === 'PublishAuthors') return resolveComponent('ViewsPublishAuthors');
			if (template === 'PublishAuthorDetail') return resolveComponent('ViewsPublishAuthorDetail');
		}

		// Auth
		if (__AUTH__) {
			if (template === 'AuthLogin') return resolveComponent('ViewsAuthLogin');
			if (template === 'AuthSignup') return resolveComponent('ViewsAuthSignup');
			if (template === 'AuthEdit') return resolveComponent('ViewsAuthEdit');
			if (template === 'AuthChangePassword') return resolveComponent('ViewsAuthChangePassword');
			if (template === 'AuthForgottenPassword') return resolveComponent('ViewsAuthForgottenPassword');
			if (template === 'AuthNewPassword') return resolveComponent('ViewsAuthNewPassword');
			if (template === 'AuthLogout') return resolveComponent('ViewsAuthLogout');
		}

		// Events
		if (__EVENTS__) {
			if (template === 'Event') return resolveComponent('ViewsEvent');
			if (template === 'EventDetail') return resolveComponent('ViewsEventDetail');
		}

		// Webshop
		if (template === 'WebshopCanceled') return resolveComponent('ViewsWebshopCanceled');
		if (template === 'WebshopCustomer') return resolveComponent('ViewsWebshopCustomer');
		if (template === 'WebshopFailedOrder') return resolveComponent('ViewsWebshopFailedOrder');
		if (template === 'WebshopFailedPayment') return resolveComponent('ViewsWebshopFailedPayment');
		if (template === 'WebshopLogin') return resolveComponent('ViewsWebshopLogin');
		if (template === 'WebshopPayment') return resolveComponent('ViewsWebshopPayment');
		if (template === 'WebshopPaymentCheck') return resolveComponent('ViewsWebshopPaymentCheck');
		if (template === 'WebshopPaymentFormAutosubmit') return resolveComponent('ViewsWebshopPaymentFormAutosubmit');
		if (template === 'WebshopPaymentCreate') return resolveComponent('ViewsWebshopPaymentCreate');
		if (template === 'WebshopPaymentCanceled') return resolveComponent('ViewsWebshopPaymentCanceled');
		if (template === 'WebshopPaymentNew') return resolveComponent('ViewsWebshopPaymentNew');
		if (template === 'WebshopPaymentOnHold') return resolveComponent('ViewsWebshopPaymentOnHold');
		if (template === 'WebshopReviewOrder') return resolveComponent('ViewsWebshopReviewOrder');
		if (template === 'WebshopShipping') return resolveComponent('ViewsWebshopShipping');
		if (template === 'WebshopShoppingCart') return resolveComponent('ViewsWebshopShoppingCart');
		if (template === 'WebshopThankYou') return resolveComponent('ViewsWebshopThankYou');
		if (template === 'WebshopCreateOrder') return resolveComponent('ViewsWebshopCreateOrder');
		if (__WEBSHOP_VIEW_ORDER__ && template === 'WebshopViewOrder') return resolveComponent('ViewsWebshopViewOrder');

		// Custom (non-base) templates
		if (templates && templates.has(template)) {
			const tpl = templates.get(template);
			return defineAsyncComponent(tpl);
		}

		return resolveComponent('ViewsCmsDefault');
	});

	provide('page', {templates});

	// set body css class
	useHead({
		bodyAttrs: {
			class: nuxtApp?.$appGlobalData?.bodyClass ? nuxtApp.$appGlobalData.bodyClass : '',
		},
		script: nuxtApp?.$appGlobalData?.structuredData ? [{type: 'application/ld+json', innerHTML: nuxtApp.$appGlobalData.structuredData}] : [],
	});
</script>
