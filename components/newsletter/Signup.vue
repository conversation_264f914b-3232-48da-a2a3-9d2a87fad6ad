<template>
	<div class="nw">
		<BaseCmsLabel code='newsletter_widget_title' tag="div" class="nw-title" />
		<BaseCmsLabel code='newsletter_widget_desc' tag="div" class="nw-desc" />

		<BaseNewsletterSignupForm v-slot="{fields, gdprFields, content, status, loading}">
			<template v-if="!status?.success">
				<div class="nw-form" :class="{'submitting': loading}">
					<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
						<BaseFormInput class="nw-input" :placeholder="labels.get('enter_email')" :id="'newsletter-'+item.name" @click="activeGdpr = 1" />
						<span v-if="item.type != 'hidden'" class="nw-error error" v-show="errorMessage" v-html="errorMessage" />
					</BaseFormField>
					<button class="nw-button" type="submit" @click="onSubmit"><BaseCmsLabel code="newsletter_signup" /></button>
					<div class="nw-gdpr" :class="{'active': activeGdpr}" v-if="gdprFields">
						<div class="nw-gdpr-note" v-if="content?.header" v-html="content.header" />
						<div class="nw-gdpr-checkbox">
							<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
								<BaseFormInput v-if="field.type == 'hidden'" />
								<p class="field" v-else>
									<BaseFormInput :id="'widget-'+field.name" :value="gdprCheckbox?.[field.name]" @click="onToggleGdpr" />
									<label :for="'widget-'+field.name" v-html="(field.name == 'gdpr_template_api_all') ? labels.get('gdpr_select_all') : field.title" />
									<span v-if="field.name == 'gdpr_template_api_all'" class="nw-desc active">
										<span class="nw-desc-cnt" v-html="labels.get('gdpr_select_all_tips')" />
									</span>
									<NewsletterWidgetGdprDescription v-else :content="field.description" />
								</p>
							</BaseFormField>
						</div>
						<div class="nw-gdpr-note special" v-if="content?.footer" v-html="content.footer" />
					</div>
				</div>
			</template>
			<!--success_subscribed_to_newsletter_email_sent-->
			<div v-show="status?.success" class="nw-success"><BaseCmsLabel code='success_subscribe' /></div>
		</BaseNewsletterSignupForm>
	</div>
</template>

<script setup>
	const labels = useLabels();

	const activeGdpr = ref(false); // open gdpr container
	const gdprCheckbox = ref({});

	onMounted(() => {
		setTimeout(() => {
			const fields = document.querySelectorAll('.nw-gdpr-checkbox [name^="gdpr_template_api"]');
			if(fields) fields.forEach(el => gdprCheckbox.value[el.name] = el.checked);
		}, 2000)
	});

	// toggle "all gdpr checkbox" if all gdpr options are checked or unchecked
	function onToggleGdpr(e) {
		if(e.target.name == 'gdpr_template_api_all') {
			Object.keys(gdprCheckbox.value).forEach(el => {
				gdprCheckbox.value[el] = e.target.checked;
			});
		} else {
			gdprCheckbox.value[e.target.name] = e.target.checked;
			const allTrue = Object.keys(gdprCheckbox.value).every(el => {
				return el != 'gdpr_template_api_all' ? gdprCheckbox.value[el] : true;
			});
			gdprCheckbox.value['gdpr_template_api_all'] = allTrue;
		}
	}
</script>

<style lang="less" scoped>
	.nw{
		flex-grow: 1; margin-bottom: 42px;
	}
	.nw-title{
		margin-bottom: 12px; font-size: 28px; line-height: 1.2; font-weight: 600; letter-spacing: -0.28px; color: var(--white);

		@media (max-width: @l){font-size: 24px; letter-spacing: -0.24px;}
	}
	.nw-desc{
		font-size: 18px; color: var(--white);

		@media (max-width: @l){font-size: 16px;}
	}

	.nw-form{
		flex-grow: 1; margin-top: 24px; position: relative;

		@media (max-width: @m){margin-top: 20px;}
	}
	:deep(.nw-input){
		height: 48px; padding: 0 170px 0 20px; border: none; border-radius: 24px; font-size: 16px; .placeholder(var(--gray5), var(--placeholderColor));

		@media (max-width: @l){padding-right: 130px;}
		@media (max-width: @m){padding: 0 20px; border-radius: 58px; font-size: 14px;}
	}
	.nw-error{color: var(--white);}
	.nw-button{
		display: flex; align-items: center; justify-content: center; min-width: 142px; height: 48px; padding: 0 20px; background: var(--turquoise); border-radius: 0 24px 24px 0; font-size: 16px; font-weight: 600; color: var(--blueDark); position: absolute; right: 0; top: 0;

		@media (max-width: @l){min-width: 108px;}
		@media (max-width: @m){width: 100%; margin-top: 16px; border-radius: 35px; position: relative; top: unset; right: unset;}
	}

	.nw-gdpr{
		max-height: 0; color: var(--white); overflow: hidden; visibility: hidden; .transition(max-height);
		&.active{ max-height: 2500px; visibility: visible;}
	}
	.nw-gdpr-note{
		padding: 24px 0 12px; font-size: 15px; line-height: 1.35;
		&.special{padding: 0;}
		:deep(a) {
			color: #fff; text-decoration: underline;
			&:hover{text-decoration: none;}
		}
	}
	:deep(.nw-gdpr-checkbox) {
		p{padding-bottom: 12px;}
		input[type='checkbox'] + label{font-size: 15px;}
		input[type=checkbox] + label:before{background: var(--blueDark); border: 2px solid var(--white); color: var(--blueDark);}
		input[type=checkbox]:checked + label:before{background: var(--white); border-color: var(--white); color: var(--blueDark);}
		.nw-gdpr-btn-more{display: none;}
	}

	:deep(.nw-desc-link){
		display: block; margin-left: 32px; font-size: 15px; color: var(--white); text-decoration: underline; cursor: pointer;
		.btn-active{display: none;}
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}
	:deep(.nw-desc){
		display: block; position: relative;
		:deep(.btn-inactive){display: block;}
		&.active{
			&:after{opacity: 1;}
			.nw-desc-cnt{max-height: 1500px; padding-top: 12px;}
			.btn-inactive{display: none;}
			.btn-active{display: block;}
		}
	}
	:deep(.nw-desc-cnt) {
		display: block; width: auto; padding: 0 0 0 32px; font-size: 12px; font-weight: normal; color: var(--white); overflow: hidden; max-height: 0; transition: max-height 0.3s, padding 0.3s;
		a{color: #fff; text-decoration: underline;}
	}
</style>