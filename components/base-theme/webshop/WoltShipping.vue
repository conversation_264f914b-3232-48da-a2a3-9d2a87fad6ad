<template>
	<BaseWebshopWoltShipping v-slot="{shippingData, onSelect, dates, hours, minutes, dropoff, selectedDate, selectedHours, selectedMinutes, cash, selectedCash, loading}">
		<div class="base-wolt-shipping" :class="{'loading': loading}">
			<div class="base-wolt-estimated-delivery" v-if="shippingData?.shipment_promise?.promised_delivery_eta">
				<BaseCmsLabel :code="props.labels?.estimated_delivery_time || 'estimated_delivery_time'" default="Predviđeno vrijeme dostave" />: <strong>{{ shippingData?.shipment_promise?.promised_delivery_eta }}</strong>
			</div>

			<div class="base-wolt-dropoff">
				<input type="checkbox" name="dropoff" id="scheduled-dropoff" :checked="dropoff" @change="onSelect" />
				<label for="scheduled-dropoff"><BaseCmsLabel :code="props.labels?.desired_delivery_time || 'desired_delivery_time'" default="Želim drugačije vrijeme isporuke" /></label>
				<div class="base-wolt-fields base-wolt-fields-dropoff" v-if="dropoff">
					<div class="base-wolt-field base-wolt-field-date">
						<div class="base-wolt-label"><BaseCmsLabel :code="props.labels?.select_date || 'select_date'" default="Datum" /></div>
						<div class="base-wolt-input">
							<select name="date" @change="onSelect">
								<option value="">-</option>
								<option v-for="option in dates" :key="option[0]" :value="option[0]" :selected="option[0] == selectedDate">{{ option[1].alternative_format }}</option>
							</select>
						</div>
					</div>
					<div class="base-wolt-field base-wolt-field-time">
						<div class="base-wolt-input">
							<div class="base-wolt-label"><BaseCmsLabel :code="props.labels?.select_hours || 'select_hours'" default="Sat" /></div>
							<select name="hours" @change="onSelect">
								<option value="">-</option>
								<option v-for="hour in hours" :key="hour" :value="hour" :selected="hour == selectedHours">{{ hour }}</option>
							</select>
						</div>
					</div>
					<div class="base-wolt-field base-wolt-field-minutes">
						<div class="base-wolt-label"><BaseCmsLabel :code="props.labels?.select_minutes || 'select_minutes'" default="Minute" /></div>
						<div class="base-wolt-input">
							<select name="minutes" @change="onSelect">
								<option value="">-</option>
								<option v-for="minute in minutes" :key="minute" :value="minute" :selected="minute == selectedMinutes">{{ minute }}</option>
							</select>
						</div>
					</div>
				</div>
			</div>

			<div class="base-wolt-cash">
				<input type="checkbox" name="cash" id="cash" :checked="cash" @change="onSelect" />
				<label for="cash"><BaseCmsLabel :code="props.labels?.prepared_cash_amount || 'prepared_cash_amount'" default="Nemam pripremljen točan iznos za naplatu" /></label>
				<div class="base-wolt-fields" v-if="cash">
					<div class="base-wolt-field">
						<div class="base-wolt-label" v-if="labels.get(props.labels?.prepared_cash || 'prepared_cash')" v-html="labels.get(props.labels?.prepared_cash || 'prepared_cash')"></div>
						<div class="base-wolt-cash-input">
							<input name="cashAmount" type="number" :value="selectedCash" @input="onSelect" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</BaseWebshopWoltShipping>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps({
		labels: Object,
	});
</script>

<style scoped lang="less">
	.base-wolt-shipping {
		display: flex;
		flex-direction: column;
		gap: 8px;
	}
	.base-wolt-fields {
		display: flex;
		gap: 10px;
		padding: 10px 0;
	}
	.base-wolt-label {
		padding: 0 0 5px;
	}
</style>
