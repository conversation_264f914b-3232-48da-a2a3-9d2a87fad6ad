<template>
	<slot :items="filteredItems" :alphabet="alphabet" :loading="loading" :onSearch="onSearch" :onFilter="onFilter" :activeLetter="activeLetter" />
</template>

<script setup>
	const catalog = useCatalog();
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {generateThumbs} = useImages();
	const {bus} = useEventBus();
	const {normalizeString} = useText();
	const props = defineProps({
		fetch: Object,
		sort: {
			type: Boolean,
			default: true,
		},
		thumbPreset: String,
	});

	const loading = ref(1);
	let fetchOptions = {};

	// set fetch options
	_setFetchOptions();

	// fetch sellers
	const items = ref([]);
	const data = await catalog.fetchSellers(fetchOptions);
	items.value = data?.data || [];

	// generate thumbs
	if (items.value?.length && props.thumbPreset) {
		await generateThumbs({
			data: items.value,
			preset: props.thumbPreset,
		});
	}	
	
	// Generate alphabet from seller titles
	const alphabet = ref([]);
	if (items.value?.length) {
		alphabet.value = items.value.reduce((acc, item) => {
			let firstChar = item.title.charAt(0).toUpperCase(); // Convert to uppercase
			
			// Group numbers under '#'
			if (/^\d/.test(firstChar)) {
				firstChar = '#';
			}

			if (!acc.includes(firstChar)) {
				acc.push(firstChar);
			}

			return acc;
		}, []).sort(); // Sort alphabetically
	}

	const searchTerm = ref('');
	function onSearch(event) {
		searchTerm.value = event.target.value;
	}

	// Filter items by letter. If letter is clicked again, reset activeLetter
	const activeLetter = ref('');
	function onFilter(letter) {
		if (letter === activeLetter.value) {
			activeLetter.value = '';
		} else {
			activeLetter.value = letter.trim().toUpperCase();
		}
	}

	// Filter items if search term is provided
	const filteredItems = computed(() => {
		let data = items.value;
		
		// Filter items by search term. If search term is empty, return all items
		if (!searchTerm.value.trim()) {
			data = items.value;
		} else {
			const normalizedSearch = normalizeString(searchTerm.value.toLowerCase());
			data = items.value.filter(item => normalizeString(item.title.toLowerCase()).includes(normalizedSearch));
		}

		// Filter items by letter
		if (activeLetter.value) {
			data = data.filter(item => {
				if (activeLetter.value === '#') {
					return /^\d/.test(item.title.charAt(0));
				}
				return item.title.charAt(0).toUpperCase() === activeLetter.value;
			});
		}

		return data;
	});

	// finish loading
	loading.value = 0;

	// watch for filter / sort updates and fetch sellers
	if(props.sort) {
		watch(
			() => bus.value,
			async () => {
				// do not fetch/filter if event is not "catalogSellersUpdate"
				if (bus.value.event != 'catalogSellersUpdate') return false;

				// start loading
				loading.value = 1;

				setTimeout(async () => {
					// set fetch options
					_setFetchOptions();

					const data = await catalog.fetchSellers(fetchOptions);
					items.value = data?.data;

					// generate thumbs
					if (items.value?.length && props.thumbPreset) {
						await generateThumbs({
							data: items.value,
							preset: props.thumbPreset,
						});
					}

					// finish loading
					loading.value = 0;
				}, 100);
			}
		);
	}

	// fetch options
	function _setFetchOptions() {
		// set initial fetch options
		fetchOptions = {
			...props.fetch,
		};

		// restructure filter query params
		if (route.query) {
			Object.entries(route.query).forEach(el => {
				const value = typeof el[1] != 'string' ? el[1].join(',') : el[1];
				if (el[0] != 'to_page') fetchOptions[el[0]] = value;
			});
		}
	}
</script>
