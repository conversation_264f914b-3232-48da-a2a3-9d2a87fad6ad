<template>
	<div v-if="item?.xxx && !isAdult" class="cp-age-verification" @click="openAgeModal(item)">
		<Body :class="{'xxx': item?.xxx && !isAdult}" />
		<div class="cp-age-verification-btn">
			<span><BaseCmsLabel code="xxx_img_btn" /></span>
		</div>
		<!-- FIXME provjeriti da li se overlay ispravno prikazuje kada je uključeno keširanje stranice -->
	</div>
</template>

<script setup>
	const {openAgeModal, isAdult} = useAgeVerification();
	const props = defineProps({
		item: Object,
	});
</script>

<style scoped lang="less">
	.cp-age-verification{
		position: absolute; top: 0; right: 0; left: 0; bottom: 0; z-index: 100; cursor: pointer; display: flex; justify-content: center;
		&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; background: rgba(255,255,255,.4); backdrop-filter: blur(5px); border-radius: var(--borderRadius);}
	}
	.cp-age-verification-btn{
		margin-top: 45px; font-size: 15px; font-weight: 600; color: var(--black); position: relative; z-index: 100; text-align: center;
		@media (max-width: @m){font-size: 14px;}
		&:before{
			.icon-age(); display: block; width: 100%; font: 102px/1 var(--fonti); color: var(--black); margin-bottom: 20px;
			@media (max-width: @m){font-size: 70px; margin-bottom: 15px;}
		}
		span{text-decoration: underline;}
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}
	.cp-list-age-verification{
		align-items: center; justify-content: flex-start;
		.cp-age-verification-btn{
			width: 540px; margin: 0;
			@media (max-width: @l){width: 30%;}
			@media (max-width: @m){width: 100%;}
		}
	}
	.cp-detail-age-verification{
		align-items: center;
		&:after, .cp-age-verification-btn{display: none;}
	}
	.cp-detail-image-age-verification{align-items: center;}
	.cp-menu-age-verification{
		align-items: center;
		.cp-age-verification-btn{
			margin: 0; font-size: 10px;
			&:before{font-size: 40px; margin: 5px;}
		}
	}
</style>