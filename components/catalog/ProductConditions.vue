<template>
	<CatalogProductContainer v-if="item.product_condition">
		<template #title>
			<span><BaseCmsLabel code="item_condition" />: <strong><BaseCmsLabel :code="'condition_' + item.product_condition" /></strong></span>
		</template>
		<template #content>
			<div class="conditions">
				<NuxtLink to="/" class="condition active">
					<div class="header"><BaseCmsLabel :code="'condition_' + item.product_condition" /></div>
					<div class="cnt">
						<div class="price">
							<div class="current">1.000,34 €</div>
							<div class="old">1.100,34 €</div>
						</div>
						<div class="status">Raspoloživo</div>
					</div>
				</NuxtLink>
				<NuxtLink to="/" class="condition">
					<div class="header">Novo</div>
					<div class="cnt">
						<div class="price">
							<div class="current">1.000,34 €</div>
							<div class="old">1.100,34 €</div>
						</div>
						<div class="status">Raspoloživo</div>
					</div>
				</NuxtLink>
				<NuxtLink to="/" class="condition">
					<div class="header">Novo</div>
					<div class="cnt">
						<div class="price">
							<div class="current">1.000,34 €</div>
							<div class="old">1.100,34 €</div>
						</div>
						<div class="status">Raspoloživo</div>
					</div>
				</NuxtLink>
			</div>
		</template>
	</CatalogProductContainer>	
</template>

<script setup>
	const item = useState('product');
</script>

<style scoped lang="less">
	:deep(.cd-container-title){font-weight: normal;}
	.conditions{
		display: grid; gap: 16px; grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
		@media (max-width: @m){gap: 12px;}
	}
	.condition{
		border: 2px solid var(--gray2); border-radius: 8px; overflow: hidden; font-size: 15px; line-height: 1.3; text-decoration: none; color: var(--textColor);
		@media (max-width: @m){font-size: 14px;}
		&.active{
			border-color: var(--blueDark);
			.header{background: #e9e9f1; border-color: transparent;}
		}
	}
	.header{
		padding: 5px 10px; font-weight: bold; border-bottom: 1px solid var(--gray2);
		@media (max-width: @m){font-size: 12px;}
	}
	.cnt{padding: 7px 10px; line-height: 1.4;}
	.current{font-weight: bold;}
	.old{
		font-size: 12px; text-decoration: line-through;
		@media (max-width: @m){font-size: 11px;}
	}
	.status{
		font-size: 11px; color: var(--green);
		@media (max-width: @m){font-size: 10px;}
	}
</style>