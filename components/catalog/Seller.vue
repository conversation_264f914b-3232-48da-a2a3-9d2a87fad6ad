<template>
	<BaseMetaSeo :data="item" v-if="seo" />
	<slot :item="item" :courierName="courierName" :costsCovered="costsCovered" />
</template>

<script setup>
	const {_route} = useNuxtApp();
	const catalog = useCatalog();
	const {getUrlSegments} = useUrl();
	const {generateThumbs} = useImages();
	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		seo: {
			type: Boolean,
			default: true,
		},
	});

	let fetchOptions = {
		mode: 'full',
		...props.fetch,
	};

	// fetch seller by slug if fetch options are not defined
	if (!props.fetch) {
		fetchOptions.slug = getUrlSegments(_route.path, {limit: 1, offset: 1});
	}

	// fetch seller
	const data = await catalog.fetchSellers(fetchOptions);
	if(data?.data[0]?.status == 'Terminated') {
		await navigateTo('/404/');
	}

	const item = computed(() => {
		if (!data?.data?.length) return null;
		return data.data[0];
	});

	await generateImages();
	watch(item, async () => await generateImages());

	// generate thumbs
	async function generateImages() {
		if (item.value && props.thumbPreset) {
			await generateThumbs({
				data: item.value,
				preset: props.thumbPreset,
			});
		}
	}

	//courier data
	const courierName = computed(() => {
		if (item.value?.return_courier_service) {
			// Split the return_courier_service value into an array of words
			const name = item.value.return_courier_service.split(', ')?.map(word => word.toLowerCase());

			// Filter out any empty words
			const filteredName = name.filter(word => word.trim() !== '');

			return filteredName;
		} else {
			return false;
		}
	});

	//costs data
	const costsCovered = computed(() => {
		if (item.value?.return_costs_covered) {
			// Split the return_courier_service value into an array of words
			const name = item.value.return_costs_covered.split(', ')?.map(word => word.toLowerCase());

			// Filter out any empty words
			const filteredName = name.filter(word => word.trim() !== '');

			return filteredName;
		} else {
			return false;
		}
	});
</script>
