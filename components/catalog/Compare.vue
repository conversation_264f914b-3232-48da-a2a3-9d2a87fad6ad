<template>
	<BaseCatalogCompare v-slot="{items, counter, attributes, onRemove}" @remove="onRemoveItems">
		<div class="compare" :class="{'active': !minimized, 'active-details': details}" v-if="items?.length">
			
			<!-- Compact minimized view -->
			<div class="compare-body" v-show="!details">
				<div class="compare-toggle" @click="minimized = !minimized" />
				<template v-if="minimized">
					<div class="compare-empty"><BaseCmsLabel code="compare_products_title" /></div>
				</template>
				<template v-else>
					<div class="wrapper compare-wrapper">
						<div class="col col1">
							<div class="compare-header">
								<div class="compare-header-title"><BaseCmsLabel code="compare_products_title" /></div>
								<div class="compare-header-remove"><span @click="onRemove()"><BaseCmsLabel code="remove_all" /></span></div>
							</div>
							<div class="compare-items">
								<div class="compare-item" v-for="item in items" :key="item.id">
									<div class="compare-item-remove" @click="onRemove(item)"></div>
									<div class="compare-item-image">
										<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width240-height240']" default="/images/no-image.jpg" />
									</div>
									<div class="compare-item-title">
										{{item.title}}
									</div>
								</div>
								<div class="compare-item" v-for="n in 4 - items.length" :key="`empty-${n}`"></div>
							</div>
						</div>
						<div class="col2">
							<div class="compare-add-more" v-if="counter < 2"><BaseCmsLabel code="compare_add_more_products" /> <span>({{counter}} od 4)</span></div>
							<button v-else class="btn btn-compare" @click="details = true"><BaseCmsLabel code="compare_product" /> ({{counter}} od 4)</button>
						</div>
					</div>
				</template>
			</div>

			<!-- Details view with full product information -->
			<div class="compare-body" v-show="details">
				<div class="compare-table-header-bg" :class="{'active': y > 100}" :style="{height: headerHeight + 20 + 'px'}"/>
				<div class="wrapper compare-table-wrapper" :class="{'fixed-compare-header': y > 100}" ref="compareTable">
					<div class="compare-table-fixed">
						<div class="compare-table-header">
							<div class="compare-table-header-title"><BaseCmsLabel code="compare_product" /> ({{counter}} od 4)</div>
							<div class="compare-table-header-remove" @click="onRemove(), details = false, minimized = true"><BaseCmsLabel code="remove_all" /></div>
							<span class="toggle-icon compare-table-header-toggle" @click="details = false, minimized = true" />
						</div>
						<div class="compare-table-products">
							<div class="compare-table-item" v-for="item in items" :key="item.id">
								<div class="compare-item-remove" @click="onRemove(item)"></div>
								<div class="compare-item-image">
									<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width240-height240']" default="/images/no-image.jpg" />
								</div>
								<div class="compare-item-title">
									{{item.title}}
								</div>
								<CatalogProductPrice :item="item" class="compare-item-price" />
							</div>
							<div v-if="items.length < 4" class="compare-table-item compare-table-item-empty">
								<div class="add" @click="details = false, minimized = true"><BaseCmsLabel code="add_more" /></div>
							</div>
						</div>
					</div>

					<div class="compare-table">
						<BaseUiAccordion>
							<BaseUiAccordionPanel :active="true" id="feedback" v-slot="{onToggle, active}">
								<div class="accordion-item" :class="{'active': active}">
									<div class="attribute-row-title" @click="onToggle"><BaseCmsLabel code="rates" /><span class="toggle-icon" :class="{'active': active}"></span></div>
									<div class="attribute-row-items">
										<div v-for="item in items" :key="item.id" class="attribute-row-item">
											<CatalogRates :item="item" class="compare-item-rates" v-if="+item.feedback_rate_widget?.rates_votes > 0" />
											<template v-else>-</template>
										</div>
										<template v-if="items.length < 4">
											<div v-for="index in 4 - items.length" :key="index" class="attribute-row-item">-</div>
										</template>
									</div>
								</div>
							</BaseUiAccordionPanel>
							<BaseUiAccordionPanel v-for="(attribute, index) in attributes" :key="attribute.id" :active="index < 4" :id="attribute.id" v-slot="{onToggle, active}">
								<div class="accordion-item" :class="{'active': active}">
									<div class="attribute-row-title" @click="onToggle">{{ attribute.attribute_title }}<span class="toggle-icon" :class="{'active': active}"></span></div>
									<div class="attribute-row-items">
										<div v-for="item in items" :key="item.id" class="attribute-row-item">
											{{item?.attributes.length && item.attributes.find((obj) => obj.id == attribute.id).title ? item.attributes.find((obj) => obj.id == attribute.id).title : '-'}}
										</div>
										<template v-if="items.length < 4">
											<div v-for="index in 4 - items.length" :key="index" class="attribute-row-item">-</div>
										</template>
									</div>
								</div>
							</BaseUiAccordionPanel>
						</BaseUiAccordion>
					</div>
				</div>
			</div>
		</div>
	</BaseCatalogCompare>
</template>

<script setup>
	const {onScroll} = useDom();
	const minimized = ref(true);
	const details = ref(false);
	const compareTable = ref(null);
	const headerHeight = ref(0);
	const header = ref(null);

	// Watch scroll position of compareTable and set fixed header accordingly
	const {init, destroy, y} = onScroll({
		element: compareTable,
		manual: true,
		callback: () => {
			headerHeight.value = header.value.offsetHeight;
		}
	});

	// Watch for changes to the details ref and initialize/destroy the scroll observer accordingly
	watch(details, (newValue) => {
		if (newValue) {
			nextTick(() => {
				init();
				header.value = compareTable.value.querySelector('.compare-table-fixed');
			});
		} else {
			destroy();
		}
	});

	onBeforeUnmount(() => {
		destroy();
	});

	// Reset everything when all items are removed
	function onRemoveItems(data) {
		if(!data.items.length) {
			details.value = false;
			minimized.value = true;
			destroy();
		}
	}
</script>

<style lang="less" scoped>
	.compare{
		position: fixed; bottom: 0; left: 0; right: 0; z-index: 501; filter: drop-shadow(0px 4px 18px #00000033); max-height: 80dvh;
		&:before{.pseudo(100px,12px); position: relative; margin: auto; background: url(assets/images/compare-handle.svg) no-repeat center top;}
		@media (max-width: @t){
			&.active{
				.compare-body{padding-bottom: 65px;}
			}
		}
		&.active-details{
			overflow: hidden;
			&:before{display: none;}
			@media (max-width: @t){
				.compare-body{padding: 0;}
			}
		}
	}
	.compare-body{
		background: #fff; padding: 18px 0;
		@media (max-width: @t){padding: 23px 15px 20px;}
	}
	.compare-toggle{
		position: absolute; left: 50%; top: 0; transform: translateX(-50%); width: 100px; height: 25px; display: flex; align-items: flex-start; justify-content: center; cursor: pointer;
		&:before{.icon-arrow-down(); font: 7px/1 var(--fonti); color: var(--textColor); .scaleY(-1); display: block; margin-top: 14px;}
	}
	.active .compare-toggle:before{.scaleY(1);}
	.compare-empty{text-align: center; font-size: 18px; color: var(--textColor); font-weight: 600; position: relative; z-index: 1;}
	.compare-add-more span{
		display: none;
		@media (max-width: @t){display: inline;}
	}
	.compare-header{
		display: flex; justify-content: space-between; align-items: center; font-size: 18px; color: var(--textColor); font-weight: 600; padding-bottom: 15px;
		@media (max-width: @t){justify-content: center; padding-bottom: 20px;}
	}
	.compare-header-remove{
		cursor: pointer; font-size: 15px; color: var(--blueDark); text-decoration: underline; font-weight: normal;
		@media (max-width: @t){position: absolute; bottom: 25px; left: 0; right: 0; text-align: center;}
	}
	.compare-items{
		display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;
		@media (max-width: @t){display: none;}
	}
	.compare-item{font-size: 14px; border-radius: var(--borderRadius); border: 1px solid var(--gray6); padding: 15px; position: relative;}
	.compare-item-title{
		height: 40px; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;
		@media (max-width: @t){height: 35px;}
	}
	.compare-item-remove{
		position: absolute; top: 10px; right: 10px; width: 22px; height: 22px; display: flex; align-items: center; justify-content: center; cursor: pointer; z-index: 1; background: var(--gray2); border-radius: 100px;
		&:before{.icon-x(); font: 9px/1 var(--fonti); color: var(--textColor); font-weight: bold;}
	}
	:deep(.compare-item-rates){padding-bottom: 0;}
	.compare-item-image{
		width: 100%; height: 100px; margin-bottom: 10px; display: flex; align-items: center; justify-content: center;
		@media (max-width: @t){height: 90px;}
		:deep(img){max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: cover; display: block; margin: auto;}
	}
	.compare-wrapper{
		display: flex; gap: 20px;
		@media (max-width: @t){flex-direction: column; gap: 0;}
	}
	.col2{
		flex: 0 0 25%; padding: 40px 0 0 3%; display: flex; align-items: center; justify-content: flex-end; font-size: 16px; text-align: right; text-wrap: balance;
		@media (max-width: @t){max-width: 300px; text-align: center; display: block; margin: auto; padding: 0;}
	}
	.btn{
		width: 100%; padding: 0 10px;
		@media (max-width: @t){min-width: 270px; margin: auto;}
	}

	.accordion-item{
		padding-bottom: 20px;
		@media (max-width: @t){padding-bottom: 10px;}
		&.active{
			padding-bottom: 12px;
			.attribute-row-items{display: grid;}
		}
	}
	.attribute-row-title{
		font-size: 18px; background: var(--gray3); border-radius: 8px; padding: 12px 25px; display: flex; justify-content: space-between; align-items: center;
		@media (max-width: @t){font-size: 16px; padding: 10px 15px;}
		.toggle-icon:before{color: var(--textColor);}
	}
	.attribute-row-items{
		display: none; grid-template-columns: repeat(4, 1fr); gap: 20px; padding: 12px 0;
		@media (max-width: @t){grid-template-columns: repeat(2, 1fr); gap: 10px;}
	}
	.attribute-row-item{
		padding: 0 25px; font-size: 14px;
		@media (max-width: @t){padding: 0 15px;}
	}
	
	.compare-table-wrapper{
		overflow: auto; max-height: 71dvh; scrollbar-width: none; -ms-overflow-style: none;
		&::-webkit-scrollbar{width: 5px; display: none;}
		&::-webkit-scrollbar-thumb{background: transparent; border-radius: 100px;}
		&::-webkit-scrollbar-track{background: transparent; border-radius: 100px;}
	}
	.compare-table-products{
		display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;
		@media (max-width: @t){grid-template-columns: repeat(2, 1fr); gap: 15px;}
	}
	.compare-table-item{
		padding: 25px; font-size: 14px; position: relative;
		@media (max-width: @t){padding: 10px; font-size: 12px;}
	}
	.compare-table-item-empty{display: flex; align-items: center; justify-content: center;}
	.add{
		font-size: 14px; cursor: pointer;
		&:before{.pseudo(42px,42px); .icon-plus(); font: 18px/1 var(--fonti); color: #fff; display: flex; align-items: center; justify-content: center; border-radius: 100px; background: var(--blueDark); position: relative; margin: 0 auto 13px;}
	}
	:deep(.compare-item-price){
		padding-top: 12px;
		@media (max-width: @t){padding-top: 8px;}
	}
	.compare-table-header{
		display: flex; gap: 35px; font-size: 24px; font-weight: bold; align-items: center; border-bottom: 1px solid var(--gray2); padding-left: 25px; margin-bottom: 15px;
		@media (max-width: @t){font-size: 17px; padding-left: 10px; gap: 15px;}
	}
	.compare-table-header-remove{
		font-size: 15px; color: var(--blueDark); text-decoration: underline; font-weight: normal; cursor: pointer; text-underline-offset: 2px; text-decoration-thickness: 1px;
		@media (max-width: @t){font-size: 14px;}
	}
	.compare-table-header-toggle{
		height: 60px; width: 60px; cursor: pointer; margin-left: auto;
		@media (max-width: @t){width: 50px;}
		&:before{color: var(--textColor);}
	}

	.compare-table-fixed{position: sticky; top: 0; z-index: 500; background: #fff;}
	.fixed-compare-header{
		.compare-table-item{
			padding: 0 0 0 120px; min-height: 90px;
			@media (max-width: @t){padding: 0 10px; min-height: 0;}
		}
		.compare-item-remove{display: none;}
		.compare-item-image{
			position: absolute; width: 80px; height: 80px; top: 0; left: 25px; right: 0; bottom: 0; z-index: 1; margin: 0;
			@media (max-width: @t){opacity: 0; left: -1000px;}
		}
		.compare-table-item-empty{padding: 0;
			@media (max-width: @t){justify-content: flex-start;}
		}
		.add{
			margin-top: -8px;
			@media (max-width: @t){margin-top: 5px; font-size: 12px;}
			@media (max-width: @m){}
			&:before{
				margin: 0 auto 7px;
				@media (max-width: @t){margin: 0 auto 3px; width: 35px; height: 35px;}
			}
		}
	}
	.compare-table-header-bg{
		display: none;
		&.active{display: block; position: absolute; top: 0; left: 0; right: 0; z-index: 499; background: #fff;box-shadow: 0 0 10px #00000033;}
	}
</style>
