<template>
	<div class="cp">
		<NuxtLink :to="item.url_without_domain">
			<div class="title" v-if="item.title">{{ item.title }}</div>
			<BaseUiImage loading="lazy" :data="item.image_thumbs?.['width275-height490-crop1']" default="/images/no-image.jpg" />
		</NuxtLink>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: Object,
	})
</script>

<style scoped lang="less">
	.cp{position: relative;}
	a{display: block; height: 100%;}
	:deep(img){display: block; width: 100%; height: 100%; object-fit: cover; border-radius: var(--borderRadius);}
	.title{position: absolute; top: 10%; left: 10%; right: 10%; text-align: center; font-size: 24px; font-weight: bold; color: #fff;}
</style>