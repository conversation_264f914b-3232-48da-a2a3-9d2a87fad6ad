<template>
	<div class="cd-container">
		<slot>
			<div class="cd-container-title"><slot name="title" /></div>
			<div class="cd-container-content">
				<slot name="content" />
			</div>
		</slot>
	</div>
</template>

<style scoped lang="less">
	.cd-container{
		padding: 18px 16px; background: var(--white); border-radius: 12px; position: relative;
		&.flyout{
			padding-right: 50px; cursor: pointer;
			&:before{.icon-arrow-down(); font: 12px/1 var(--fonti); color: var(--black); position: absolute; right: 18px; top: 50%; transform: translateY(-50%) rotate(-90deg);}
		}

		@media (max-width: @m){
		padding: 18px 15px; border-radius: 0;
		}
	}
	.icon .cd-container-title{
		padding-left: 30px;
		&:before{font: 15px/1 var(--fonti); color: var(--black); position: absolute; left: 0;}
	}
	.energy .cd-container-title:before{.icon-energy(); font-size: 19px; left: 2px;}
	.shipping .cd-container-title:before{.icon-shipping(); font-size: 17px;}
	.stores .cd-container-title:before{.icon-pin(); font-size: 19px; left: 3px;}
	.store .cd-container-title:before{.icon-store(); font-size: 21px; left: 3px;}
	.seller .cd-container-title:before{.icon-store(); font-size: 19px; left: 2px;}

	.cd-container-title{
		display: flex; align-items: center; font-size: 18px; font-weight: 600; position: relative;

		@media (max-width: @m){
			font-size: 16px;
			&.icon{
				padding-left: 28px;
				&:before{font-size: 14px;}
			}
			&.shipping:before{font-size: 15px;}
			&.stores:before{font-size: 17px; left: 2px;}
			&.seller:before{font-size: 17px; left: 1px;}
			&.energy:before{font-size: 17px; left: 1px;}
		}
	}
	.cd-container-content{
		margin-top: 16px; font-size: 15px;

		@media (max-width: @m){margin-top: 12px; font-size: 12px; line-height: 1.3;}
	}
</style>