<template>
	<CatalogProductContainer v-if="item.seller_id && firstOtherOffer && (!firstMainOffer || item.offers_other_total != 1)" class="icon store flyout" @click="modal.open('flyout', {mode: 'seller', header: true, headerIcon: 'seller', footer: true, content: item.offers})">
	<!--<CatalogProductContainer v-if="item.seller_id && firstOtherOffer && (!firstMainOffer || item.offers_other_total != 1)" class="icon store flyout" @click="flyoutOpen('seller', {'offers': item.offers, 'id': item.offer_id})">-->
		<template #title>
			<span><BaseCmsLabel code="tab_seller_items" /></span>
		</template>
		<template #content>
			<div class="cd-seller-desc cd-flyout-btn" v-html="offersDescl" @click="modal.open('flyout', {mode: 'seller', header: true, headerIcon: 'seller', content: item.offers})"></div>
			<div v-if="labels.get('shop_extra_info_pdp') != 'shop_extra_info_pdp'" class="sellers-extra-info">
				<span v-html="labels.get('shop_extra_info_pdp')"></span>
			</div>
		</template>
	</CatalogProductContainer>	
</template>

<script setup>
	const item = useState('product');
	const labels = useLabels();
	const modal = useModal();

	const firstOtherOffer = computed(() => {
		return item?.value?.first_other_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_other_offer_id.toString()) : null;
	});
	const firstMainOffer = computed(() => {
		return item?.value?.first_main_seller_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_main_seller_offer_id.toString()) : null;
	});

	const offersDescl = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_others')
			.replace('%offers_total%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text)
			.replace('%price%', firstOtherOffer.value.price_custom ?? 0);
	});	
</script>

<style scoped lang="less">
	.sellers-extra-info{
		margin-top: 15px; font-size: 14px; padding-left: 20px; position: relative; text-decoration: underline; cursor: pointer; text-underline-offset: 3px; text-decoration-thickness: 1px; position: relative;
		@media (max-width: @m){font-size: 12px;}
		&:before{
			.icon-info(); font: 13px/1 var(--fonti); position: absolute; left: 0; top: 3px;
			@media (max-width: @m){top: 2px;}
		}
	}
</style>