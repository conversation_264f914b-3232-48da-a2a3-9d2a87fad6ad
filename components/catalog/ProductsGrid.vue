<template>
	<div v-if="props.items?.length" class="cw-double">
		<div class="wrapper" v-if="list.title || list.url_without_domain || list.external_url">
			<CmsSectionHeader>
				<div v-if="list.title" v-html="list.title"></div>
				<template #btn>
					<NuxtLink :to="list.external_url ? list.external_url : list.url_without_domain" class="widget-btn-show-all" v-if="list.url_without_domain || list.external_url">
						<span><BaseCmsLabel code="show_all" /></span>
					</NuxtLink>
				</template>
			</CmsSectionHeader>
		</div>
		<UiSwiper
			:name="list.code"
			:options="{
				slidesPerView: 1.7,
				spaceBetween: 12,
				slidesPerGroup: 1,
				breakpoints: {
					980: {
						slidesPerView: 5,
						slidesPerGroup: 5,
						spaceBetween: 24,
					},
					600: {
						slidesPerView: 2.5,
						slidesPerGroup: 2,
						spaceBetween: 12,
					}
				}
			}">
			<BaseUiSwiperSlide v-if="list.main_image_3" class="slide special">
				<NuxtLink :to="list.external_url ? list.external_url : list.url_without_domain">
					<BaseUiImage loading="lazy" :data="list.main_image_3_thumb" default="/images/no-image.jpg" />
					<!--<BaseUiImage loading="lazy" :data="list.main_image_3" default="/images/no-image.jpg" />-->
					<!-- FIXME dodatno polje za naslov u banneru liste -->
					<!--<span class="title">Idealni božićni pokloni!</span>-->
					<span class="button"><BaseCmsLabel code="read_more" /></span>
				</NuxtLink>
			</BaseUiSwiperSlide>
			<template v-for="(slide, index) in props.items" :key="index">
				<BaseUiSwiperSlide class="slide">
					<template v-for="item in slide" :key="item.id">
						<NuxtLink :to="list.external_url ? list.external_url : list.url_without_domain" v-if="item.id == 'readmore'" class="promo-read-more"><BaseCmsLabel code="read_more" /></NuxtLink>
						<CatalogIndexEntry :item="item" v-else />
					</template>
				</BaseUiSwiperSlide>
			</template>	
		</UiSwiper>
	</div>
</template>

<script setup>
	const props = defineProps({
		list: Object,
		items: Array
	})
</script>

<style scoped lang="less">
	.cw-double{margin-bottom: var(--widgetSpacing); position: relative;}
	:deep(.swiper){overflow: initial;}
	.slide{
		display: flex; flex-direction: column; gap: var(--elementGap);
		&.special{
			&>a{display: flex; align-items: center; justify-content: center; position: relative;}
			.title{font-size: 32px; line-height: 1.25; font-weight: 700; text-align: center; color: var(--white); position: absolute; top: 20%; z-index: 1;}
			.button{
				display: flex; align-items: center; padding-right: 36px; font-size: 12px; line-height: 1.2; font-weight: 600; color: var(--white); position: absolute; right: 12px; bottom: 24px;
				&:before{.pseudo(25px,25px); display: flex; align-items: center; justify-content: center; background: var(--white); border-radius: 100%; .icon-arrow-right(); font: 11px/1 var(--fonti); color: var(--black); position: absolute; right: 0;}
			}
			:deep(img){width: 100%;}
		}
		@media (max-width: @m){
			&.special{
				.title{font-size: 20px; font-weight: 800; top: 15%;}
				.button{bottom: 16px;}
			}
		}
	}
	:deep(.cp){
		min-height: 225px;
		.cp-rate, .cp-product-info, .cp-badges, .cp-badges-special, .cp-price-installment{display: none;}
		.cp-image-container{padding: 10px 10px 5px;}
		.cp-image a{height: 130px;}
		.cp-title{font-size: 12px;}
		.cp-current-price{font-size: 20px;}
		.cp-price{margin: 0; padding: 0;}
	}
</style>