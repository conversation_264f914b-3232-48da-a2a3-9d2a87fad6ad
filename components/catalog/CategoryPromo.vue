<template>
	<BaseCmsRotator
		:fetch="{code: 'catalog_category_promo', catalogcategory_id: category.id, response_fields: ['id','link','title','link_target_blank','url_without_domain','image','image_thumbs']}"
		v-slot="{items}">
		<div v-if="items?.length" class="c-promo-rotator">
			<BaseUiSwiper name="cList" :options="{
				slidesPerView: 1,
				loop: true,
				autoplay: {
					delay: 5000,
					disableOnInteraction: true
				},
				effect: 'fade'
			}">
				<BaseUiSwiperSlide v-for="item in items" :key="item.id" class="c-promo-slide">
					<NuxtLink :target="item?.link_target_blank == 1 ? '_blank' : null" :to="item?.url_without_domain">
						<BaseUiImage
							loading="lazy"
							:data="item?.image_thumbs?.['width1920-height200-crop1']"
							default="/images/no-image.jpg"
							:picture="[{maxWidth: '760px', src: item?.image_thumbs?.['width760-height390-crop1']?.thumb ? item?.image_thumbs['width760-height390-crop1']?.thumb : item?.image_thumbs?.['width1920-height200-crop1']?.thumb, default: '/images/no-image.jpg'}]" />
					</NuxtLink>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const props = defineProps({
		category: Object,
	})
</script>

<style scoped lang="less">
	.c-promo-rotator{max-width: 1920px; margin: 0 auto; position: relative; background: #fff;}
	:deep(.swiper-container){display: flex;}
	:deep(img){display: block; width: 100%; height: auto;}
	.c-promo-slide{display: flex; align-items: center; justify-content: center; background: #fff;}
</style>