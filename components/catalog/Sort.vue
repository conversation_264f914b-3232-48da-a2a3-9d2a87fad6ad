<template>
	<div class="c-toolbar-extra">
		<div class="c-toolbar-info" :class="{'has-flyout': hasSortFlyout}" @click="hasSortFlyout ? modal.open('flyout', {header: true, title: labels.get('shop_extra_info_flyout_title'), content: labels.get('shop_extra_info_flyout_content')}) : null">
			<BaseCmsLabel code='shop_extra_info' />
		</div>
		<FormSelect class="c-toolbar-select layout" :label="labels.get('layout_option_label')" :html="true">
			<BaseFormSelectOption :selected="layout == 'grid'" value="grid" @click="changeItemsLayout('grid')"><span class="grid"><BaseCmsLabel code='grid' /></span></BaseFormSelectOption>
			<BaseFormSelectOption :selected="layout == 'list'" value="list"><span class="list" @click="changeItemsLayout('list')"><BaseCmsLabel code='list' /></span></BaseFormSelectOption>
		</FormSelect>

		<BaseCatalogSort :sort-options="['priority', 'fastest_first', 'rates', 'new', 'discount_priority', 'expensive', 'cheaper']" v-slot="{items, onSort, selected}">
			<FormSelect class="c-toolbar-select sort" :label="labels.get('sort')">
				<BaseFormSelectOption v-for="sort in items" :key="sort" :value="sort" :selected="sort == selected" @click="onSort(sort)"><BaseCmsLabel :code="'sort_'+sort" /></BaseFormSelectOption>
			</FormSelect>
		</BaseCatalogSort>
	</div>
<BaseCatalogSort />
</template>

<script setup>
	const labels = useLabels();
	const modal = useModal();
	const hasSortFlyout = computed(() => labels.get('shop_extra_info_flyout_title') && labels.get('shop_extra_info_flyout_content'));
	const {layout, changeItemsLayout} = inject('baseCatalogItemsLayoutData');
</script>

<style lang="less" scoped>
	.c-toolbar-extra{
		display: flex; align-items: center; justify-content: flex-end; flex-shrink: 0; flex-grow: 1; gap: 12px;
		@media (max-width: @m){display: none;}
	}
	.c-toolbar-info{
		font-size: 12px; line-height: 1.3; color: var(--gray5);
		&.has-flyout{text-decoration: underline; cursor: pointer;}
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
		@media (max-width: @m){display: none;}
	}
</style>
