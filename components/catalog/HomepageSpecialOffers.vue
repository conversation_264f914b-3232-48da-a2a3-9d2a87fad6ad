<template>
	<BaseCatalogLists :fetch="{code: 'special_offers', limit: 1, response_fields: ['code','title','url_without_domain','external_url']}" v-slot="{items: lists}">
		<BaseCatalogProductsWidget 
			v-if="lists?.length"
			thumb-preset="catalogEntry"
			:fetch="{mode: 'widget', list_code: lists[0].code, sort: 'list_position', only_available: true, limit: 10, user_use_loyalty: userLoyaltyCode}"
			v-slot="{items}"
			@loadProductsWidget="loadProductsWidget">
			<div class="cw" v-if="items?.length">
				<div class="wrapper" v-if="lists[0].title || lists[0].url_without_domain || lists[0].external_url">
					<CmsSectionHeader>
						<div v-if="lists[0].title" v-html="lists[0].title"></div>
						<template #btn>
							<NuxtLink :to="lists[0].external_url ? lists[0].external_url : lists[0].url_without_domain" class="widget-btn-show-all" v-if="lists[0].url_without_domain || lists[0].external_url">
								<span><BaseCmsLabel code="show_all" /></span>
							</NuxtLink>
						</template>
					</CmsSectionHeader>
				</div>
				<UiSwiper
					name="hp-special-offers"
					:options="{
						breakpoints: {
							980: {
								slidesPerView: 5,
								slidesPerGroup: 5,
								spaceBetween: 24,
							},
							650: {
								slidesPerView: 2.5,
								slidesPerGroup: 2,
								spaceBetween: 12,
							},
							0: {
								slidesPerView: 1.7,
								slidesPerGroup: 1,
								spaceBetween: 12,
							}
						}
					}">
					<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id" class="slide">
						<CatalogIndexEntry :item="item" :index="index" />
					</BaseUiSwiperSlide>
				</UiSwiper>
			</div>
		</BaseCatalogProductsWidget>
	</BaseCatalogLists>	
</template>

<script setup>
	const auth = useAuth();
	const gtm = useGtmBB();
	
	// Loyalty code
	const userLoyaltyCode = computed(() => {
		const user = auth.getUser();
		if(user?.loyalty_code) {
			return user.loyalty_code;
		} else {
			return null;
		}
	});

	// GTM tracking
	function loadProductsWidget(value) {
		if(value?.items?.length) {
			gtm.viewItemList(value.items, value.data);
		}
	}
</script>

<style scoped>
	.cw{
		margin-bottom: 45px; position: relative;
		@media (max-width: @t){margin-bottom: 25px;}
	}
	:deep(.swiper){overflow: initial;}
</style>