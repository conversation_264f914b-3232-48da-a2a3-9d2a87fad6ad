<template>
	<BaseCatalogLists :fetch="{homepage: true, sort: 'homepage_position', limit: 5, response_fields: ['id','code','title','external_url','url_without_domain','main_image_3','main_image_3_thumbs']}" thumb-preset="catalogList" v-slot="{items: lists}">
		<template v-if="lists?.length">
			<!-- First show black friday lists, then all other lists -->
			<template v-for="list in lists.filter(item => item.code.startsWith('bf_'))" :key="list.id">
				<template v-if="list?.id">
					<CatalogProductsWidget 
						:fetch="{list_code: list.code, sort: 'list_position', only_available: true, limit: 20, user_use_loyalty: userLoyaltyCode}" 
						thumb-preset="catalogEntry"
						:frosmo="list.code"
						:transform-products="transformProducts" 
						@loadProductsWidget="loadProductsWidget" 
						v-slot="{items}">
						<LazyCatalogProductsGrid :list="list" :items="items" v-if="items?.length" />
					</CatalogProductsWidget>
				</template>
			</template>

			<template v-for="list in lists.filter(item => !item.code.startsWith('bf_'))" :key="list.id">
				<template v-if="list?.id">
					<CatalogProductsWidget 
						:fetch="{list_code: list.code, sort: 'list_position', only_available: true, limit: 20, user_use_loyalty: userLoyaltyCode}" 
						thumb-preset="catalogEntry"
						:frosmo="list.code"
						:transform-products="transformProducts"
						@loadProductsWidget="loadProductsWidget" 
						v-slot="{items}">
						<LazyCatalogProductsGrid :list="list" :items="items" v-if="items?.length" />
					</CatalogProductsWidget>
				</template>
			</template>
		</template>
	</BaseCatalogLists>
</template>

<script setup>
	const auth = useAuth();
	const gtm = useGtmBB();

	// Loyalty code
	const userLoyaltyCode = computed(() => {
		const user = auth.getUser();
		if(user?.loyalty_code) {
			return user.loyalty_code;
		} else {
			return null;
		}
	});

	// Group products by 2 and add readmore item
	function transformProducts(data) {
		let items = data;
		if(items?.length && !items.find(item => item.id == 'readmore')) {
			items.push({
				id: 'readmore',
			})
		}

		const chunkSize = 2;
 		const chunks = [];
		if(items?.length) {
			for (let i = 0; i < items.length; i += chunkSize) {
				chunks.push(items.slice(i, i + chunkSize));
			}
			items = chunks;
		}

		return items;
	}

	// GTM tracking
	function loadProductsWidget(value) {
		if(value?.items?.length) {
			gtm.viewItemList(value.items, value.data);
		}
	}
</script>