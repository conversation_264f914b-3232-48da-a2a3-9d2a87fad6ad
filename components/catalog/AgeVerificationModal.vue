<template>
	<div class="age-check" v-if="isAgeModalActive">
		<div class="age-check-body">
			<div class="age-check-content">
				<div class="title"><BaseCmsLabel code="age_check_title" /></div>
				<BaseCmsLabel class="content" code="age_check_content" tag="div" />
				<div class="age-check-btns">
					<button class="btn" @click="approveAge()">Da</button>
					<button class="btn btn-outline" @click="declineAge()">Ne</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const {isAgeModalActive, approveAge, declineAge} = useAgeVerification();
</script>

<style lang="less" scoped>
	.age-check{position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 1112; background: rgba(0,0,0,.4);}
	.age-check-body{
		position: absolute; bottom: 0; left: 0; right: 0; background: #fff; z-index: 100; filter: drop-shadow(0px 4px 18px #00000033); text-wrap: balance; font-size: clamp(14px, 1.5vw, 16px);
		&:before{
			.pseudo(160px,160px); position: absolute; left: 50%; margin-left: -80px; top: -80px; background: #fff url(assets/images/icons/age.svg) no-repeat center center; background-size: 100px auto; border-radius: 100px;
			@media (max-width: @m){.pseudo(130px,130px); background-size: 80px auto; margin-left: -65px; top: -65px;}
		}
	}
	.age-check-content{
		width: 580px; max-width: 580px; margin: auto; text-align: center; padding: 80px 0;
		@media (max-width: @m){width: 100%; padding: 60px 20px 50px;}
	}
	.title{font-size: clamp(24px, 3vw, 28px); font-weight: bold; padding: 0 0 13px;}
	.age-check-btns{display: flex; gap: 10px; justify-content: center; padding-top: 40px;}
	.btn{
		height: 60px; border-radius: 100px; min-width: 160px;
		@media (max-width: @m){height: 48px; flex-grow: 1;}
	}
</style>
