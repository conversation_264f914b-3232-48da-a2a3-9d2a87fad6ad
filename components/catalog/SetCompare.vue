<template>
	<ClientOnly>
		<div class="set-compare-cnt" :class="[mode]">
			<BaseCatalogSetCompare :item="item" v-slot="{onToggleCompare, active, loading, message}">
				<div class="set-compare" :class="[mode, {'loading': loading, 'active': active}]" @click="onToggleCompare">
					<span class="loader" v-show="loading"><UiLoader class="inline" /></span>
					<BaseCmsLabel code="add_compare_product" />
					<UiTooltip class="right wrap-text" v-if="message && message == 'compare_error_limit'"><BaseCmsLabel code="compare_error_limit" /></UiTooltip>
				</div>
			</BaseCatalogSetCompare>
		</div>
	</ClientOnly>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
</script>

<style lang="less" scoped>
	.set-compare{
		display: flex; align-items: center; color: var(--gray5); justify-content: center; flex-shrink: 0; font-size: 0; width: 36px; height: 36px; background: rgba(255, 255, 255, 0.90); border-radius: 100%; text-decoration: none; cursor: pointer;
		&:before{
			width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; .icon-arrows-horizontal(); font: 26px/1 var(--fonti); color: var(--gray5);
			@media (max-width: @m){font-size: 22px;}
		}
		&.active{
			color: var(--blueDark);
			&:before{color: var(--blueDark);}
		}
		&.loading:before{display: none;}
		&.detail{
			width: 46px; height: 46px; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);
			@media (max-width: @m){width: 36px; height: 36px;}
		}
	}
	.loader{width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;}
	.tooltip{width: 130px;}
	.list{
		.set-compare{width: auto; font-size: 15px; margin-right: 25px; font-weight: bold; gap: 5px;
			@media (max-width: @m){font-size: 0; margin-right: 0;}
		}
	}
	.detail{
		.tooltip{right: 7px;}
	}
</style>