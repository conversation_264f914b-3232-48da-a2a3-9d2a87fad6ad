<template>
	<BaseCatalogLists :fetch="{category: props.category.id, limit: 1, response_fields: ['id', 'title', 'code']}" v-slot="{items: list}">
		<template v-if="list?.length">
			<CatalogProductsWidget
				:fetch="{mode: 'full', list_code: list[0]?.code, sort: 'list_position', list_title: list[0]?.title, only_available: true, limit: 20}"
				:transform-products="transformProducts"
				@loadProductsWidget="loadProductsWidget" 
				v-slot="{items}"
				>
				<CatalogProductsGrid :list="list[0]" :items="items" v-if="items?.length" />
			</CatalogProductsWidget>
		</template>
	</BaseCatalogLists>
</template>

<script setup>
	const gtm = useGtmBB();
	const props = defineProps({
		category: Object,
	})

	// Group products by 2
	function transformProducts(data) {
		let items = data;
		const chunkSize = 2;
 		const chunks = [];
		if(items?.length) {
			for (let i = 0; i < items.length; i += chunkSize) {
				chunks.push(items.slice(i, i + chunkSize));
			}
			items = chunks;
		}

		return items;
	}

	// GTM tracking
	function loadProductsWidget(value) {
		if(value?.items?.length) {
			gtm.viewItemList(value.items, value.data);
		}
	}	
</script>

<style scoped>

</style>