<template>
	<BaseCatalogProductsWidget v-bind="$attrs" v-slot="{items, meta}">
		<slot :items="frosmoProducts?.length ? frosmoProducts : items" :meta="meta" />
	</BaseCatalogProductsWidget>
</template>

<script setup>
	const config = useAppConfig();
	const info = useInfo();
	const catalog = useCatalog();
	const {waitForWindowProperty} = useMeta();
	const props = defineProps({
		frosmo: String
	})

	const frosmoStrategies = info.getInfo('frosmo_strategies');
	const frosmoArray = frosmoStrategies.split('\n');
	const frosmoRecCode = shallowRef(null);
	const frosmoStrategy = shallowRef(null);
	const frosmoProducts = shallowRef([]);

	async function fetchFrosmoProducts(recommendation_code, strategy) {
		//console.debug('frosmo.easy.strategies, recommendation_code:', recommendation_code, ' strategy:', strategy);

		if (window.frosmo?.easy && window.frosmo?.easy?.strategies && strategy) {
			window.frosmo.easy.strategies.fetch(strategy).then(async function (response) {
				if(response?.data?.length) {
					const productsID = response.data?.map(product => product.id);
					if(!productsID?.length) return;
					
					const frosmoProducts = await catalog.fetchProducts({
							mode: 'widget',
							code_exact_sort: 1,
							limit: productsID.length,
							code: productsID,
							response_fields: config.catalog.productsResponseFields
						});

					if(frosmoProducts?.data?.items?.length) frosmoProducts.value = frosmoProducts.data.items;
				}
			})
			.catch(function (error) {
				console.error(error);
			});
		}
	}

	// If frosmo strategy is defined, fetch frosmo products. If frosmo products are not available use regular list products
	onMounted(async () => {
		if(frosmoArray.length && props.frosmo) {
			const foundValue = frosmoArray.find(item => item.startsWith(props.frosmo));
			if (foundValue) {
				const splitValue = foundValue.split('=');
				frosmoRecCode.value = (splitValue.length >= 2) ? splitValue[0] : null;
				frosmoStrategy.value = (splitValue.length >= 2) ? splitValue[1] : null;
			}

			if(frosmoStrategy.value) {
				waitForWindowProperty('frosmo', async () => {
					await fetchFrosmoProducts(frosmoRecCode.value, frosmoStrategy.value);
				})
			}
		}
	});
</script>