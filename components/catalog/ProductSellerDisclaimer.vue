<template>
	<div v-if="item.seller_id && item.seller_code != '9999' && labels.get('sellers_info') != 'sellers_info'" class="sellers-info">
		<span v-html="labels.get('sellers_info')"></span>
		<span v-if="labels.get('sellers_info_btn') != 'sellers_info_btn'" class="sellers-info-btn" @click="modal.open('flyout', {header: true, title: labels.get('sellers_info_flyout_title'), content: labels.get('sellers_info_flyout')})">{{ labels.get('sellers_info_btn') }}</span>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: Object,
	});
	const labels = useLabels();
	const modal = useModal();
</script>

<style scoped lang="less">
	.sellers-info{color: var(--gray5); font-size: 15px; padding: 0 0 20px;}
	.sellers-info-btn{text-decoration: underline; cursor: pointer; margin-left: 3px; text-decoration-thickness: 1px; text-underline-offset: 3px;}
</style>