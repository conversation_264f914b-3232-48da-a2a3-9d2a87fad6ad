<template>
	<div class="info">
		<div class="info-table">
			<div class="row" v-if="item.seller_id">
				<div class="col col1"><BaseCmsLabel code="seller_item_title" /></div>
				<div class="col col2"><NuxtLink :to="item.seller_url_product_page_without_domain ? item.seller_url_product_page_without_domain : item.seller_url_without_domain">{{ item.seller_title }}</NuxtLink></div>
			</div>
			<div class="row" v-if="item.element_state_code && item.element_state_code != 'n'">
				<div class="col col1"><BaseCmsLabel code="item_condition" /></div>
				<div class="col col2"><CatalogProductBadges :item="item" :types="['condition']" /></div>
			</div>
			<div class="row">
				<div class="col col1"><BaseCmsLabel code="return_policy" /></div>
				<div class="col col2">
					<span class="link" @click="modal.open('flyout', {title: labels.get('return_policy_flyout_title'), header: true, content: labels.get('return_policy_info')})"><BaseCmsLabel code="return_policy_value" /></span>
				</div>
			</div>
			<div class="row" v-if="warranty">
				<div class="col col1">Garancija</div>
				<div class="col col2">{{ warrantyLabels[warranty] || warranty }}</div>
			</div>
			<div class="row" v-if="guarantee">
				<div class="col col1">Jamstvo</div>
				<div class="col col2">{{ warrantyLabels[guarantee] || guarantee }}</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: Object,
	});
	const labels = useLabels();
	const modal = useModal();

	const warrantyLabels = {
		months12: '12 mesecev',
		months24: '24 mesecev',
		months36: '36 mesecev',
		months48: '48 mesecev',
		months60: '60 mesecev',
	};
	const warranty = computed(() => {
		const w = props.item.channel_element_warranty || props.item.element_warranty || '';
		if(w == 'months0') return '-';
		return w;
	});
	const guarantee = computed(() => {
		const g = props.item.channel_element_guarantee || props.item.element_guarantee || '';
		if(g == 'months0') return '-';
		return g;
	});
</script>

<style scoped lang="less">
	.info{
		font-size: 15px;
		@media (max-width: @m){font-size: 14px;}
	}
	.col1{flex: 0 0 150px; color: var(--gray5);}
	.row{display: flex; gap: 10px;}
	.info-table{display: flex; gap: 10px; flex-direction: column;}
	a{color: var(--textColor); text-decoration-thickness: 1px; text-underline-offset: 3px;}
</style>