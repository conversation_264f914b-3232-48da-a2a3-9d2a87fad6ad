<template>
	<div class="cp">
		<div class="cp-img">
			<NuxtLink :to="item.url_without_domain">
				<BaseUiImage loading="lazy" :data="item.main_image_upload_path_thumb" default="/images/no-image.jpg" />
				<!--<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width240-height240']" default="/images/no-image.jpg" />-->
			</NuxtLink>
		</div>
		<div class="cp-cnt">
			<NuxtLink :to="item.url_without_domain" class="cp-title">{{ item.title }}</NuxtLink>
			<div class="cp-price">
				<span class="cp-current-price" :class="{'discount': item.basic_price_custom > item.price_custom}"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
				<span v-if="item.basic_price_custom > item.price_custom" class="cp-basic-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
				<span v-if="item.basic_price_custom > item.price_custom" class="cp-price-info">
					<span class="cp-price-tooltip"><BaseCmsLabel code='lowest_price' /></span>
				</span>
			</div>
		</div>
		<CatalogAgeVerificationOverlay :item="props.item" class="cp-menu-age-verification" />
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.cp{
		display: flex; align-items: flex-start; padding: 24px 8px 16px; background: var(--white); border-bottom: 1px solid #F4F4F4; color: var(--black); text-decoration: none; position: relative;
		&:nth-child(2){padding-top: 16px;}
		
		@media (max-width: @m){
			padding: 24px 8px 16px;
			&:nth-child(2){padding-top: 12px;}
			&:last-child{border-bottom: 0; padding-bottom: 8px;}
		}
	}
	.cp-img{
		width: 90px; height: 90px; flex-shrink: 0; flex-grow: 0; margin-right: 16px; position: relative;
		a{display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}

		@media (max-width: @m){
			width: 70px; height: 70px;
		}
	}
	.cp-cnt{display: block; width: 100%; position: relative;}
	.cp-title{
		display: block; font-size: 14px; color: var(--black); text-decoration: none; position: relative;

		@media (max-width: @m){display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; font-size: 12px;}
	}
	.cp-price{
		display: flex; align-items: flex-end; margin-top: 5px; font-size: 16px; font-weight: 700; position: relative;
		:deep(.p-comma){display: none;}
		:deep(.p-d){font-size: 12px; vertical-align: text-top;}
	}
	.cp-current-price.discount{color: var(--red);}
	.cp-basic-price{margin-left: 8px; font-size: 12px; font-weight: 400; letter-spacing: -0.24px; color: var(--black); text-decoration: line-through;}
	.cp-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 14px; height: 14px; margin-bottom: 2px; margin-left: 6px; position: relative;
		&:before{.icon-info(); font: 14px/1 var(--fonti); color: var(--black);}
		&:hover .cp-price-tooltip{display: flex;}
	}
	.cp-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: #000; position: absolute; left: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); left: 12px; z-index: 1; .rotate(45deg);}
	}
</style>
