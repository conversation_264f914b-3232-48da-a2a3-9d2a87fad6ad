<template>
	<div v-if="item.price_custom > 0" class="price">
		<template v-if="isRecommendedPrice">
			<div class="current-price red"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
			<div v-if="item.basic_price_custom" class="old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
		</template>
		<template v-else-if="isPromoPrice">
			<div class="current-price red"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
			<div v-if="Number(item.discount_percent_custom)" class="old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
			<div v-if="showBadges" class="badges">
				<div v-if="item.price_custom < item.basic_price_custom" class="badge yellow">
					<BaseCmsLabel code="prihranek_promotion" />: <strong><BaseUtilsFormatCurrency :price="item.basic_price_custom - item.price_custom" /></strong>
				</div>
			</div>
		</template>
		<template v-else-if="item.selected_price == 'uau'">
			<div class="current-price"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
			<div class="price-uau"></div>
		</template>
		<template v-else-if="isDiscountPrice">
			<span v-if="priceFrom" class="old-price price-label var"><BaseCmsLabel code='price_variation' /></span>
			<div class="current-price discount-price red"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
			<div class="old-price line-through"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
			<span class="price-info">
				<span class="price-tooltip"><BaseCmsLabel code='lowest_price' /></span>
			</span>
		</template>
		<template v-else-if="isLoyaltyPrice">
			<span v-if="priceFrom" class="old-price price-label var"><BaseCmsLabel code='price_variation' /></span>
			<div class="current-price discount-price blue"><BaseUtilsFormatCurrency :wrap="true" :price="item.loyalty_price_custom" /></div>
			<div class="old-price line-through">
				<template v-if="item.selected_price == 'recommended' && (item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
					<BaseUtilsFormatCurrency :price="item.price_custom" />
				</template>
				<template v-else>
					<BaseUtilsFormatCurrency :price="item.basic_price_custom" />
				</template>
			</div>
		</template>
		<template v-else>
			<div class="current-price"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: {
			type: Object,
			required: true
		},
		showBadges: {
			type: Boolean,
			default: true
		}
	});
	const {priceFrom, isRecommendedPrice, isPromoPrice, isLoyaltyPrice, isDiscountPrice} = useProductData(props.item);
</script>

<style lang="less" scoped>
	.price{
		display: flex; align-items: flex-end; padding-top: 16px; gap: 6px; line-height: 1; flex-wrap: wrap;
		@media (max-width: @t){flex-wrap: wrap; row-gap: 2px;}
		@media (max-width: @m){padding-top: 10px;}
		.red{color: var(--errorColor);}
	}
	.current-price{
		font-weight: 700; font-size: 22px;
		@media (max-width: @t){font-size: 16px;}
		.p-comma{display: none;}
		.p-d{font-size: 12px; vertical-align: text-top;}
	}
	.old-price{font-size: 14px; color: var(--black); padding-bottom: 3px; text-decoration: line-through;}
	.price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 11px; height: 11px; position: relative; margin-bottom: 8px;
		@media (max-width: @t){margin-bottom: 1px;}
		&:before{.icon-info(); font: 11px/1 var(--fonti); color: var(--black);}
		&:hover .price-tooltip{display: flex;}
	}
	.price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: var(--black); position: absolute; right: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); right: 12px; z-index: 1; .rotate(45deg);}
	}
	.price-uau{display: flex; align-items: center; justify-content: center; width: 53px; height: 25px; background: url(assets/images/uau-badge.svg) no-repeat center; background-size: auto 40px; z-index: 1;}
	.badges{width: 100%;}
	.badge{
		padding: 5px 10px; display: inline-block; font-weight: bold; background: var(--blueDark); border-radius: 4px; font-size: 12px; color: var(--white);
		&.red{background: var(--errorColor);}
		&.blue{background: var(--blue);}
		&.yellow{background: #CDD700; color: var(--blueDark);}
	}
</style>