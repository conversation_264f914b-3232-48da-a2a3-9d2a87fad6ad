<template>
	<CatalogProductContainer class="flyout icon shipping" v-if="item.shipping_options?.length && item.active_shipping_options_count" @click="shippingFlyout()">
		<!-- <div class="cd-item-info-shipping cd-container flyout" @click="flyoutOpen('shipping')">-->
		<template #title><BaseCmsLabel code="item_delivery_tab_title" /></template>
		<template #content>
			<template v-for="(options, index) in item.shipping_options" :key="options.id">
				<div v-if="options.id != 'p'" class="cd-shipping-desc" @click="modal.open('flyout', {mode: 'shipping', header: true, headerIcon: 'shipping', content: {id: options.id, shipping_options: item.shipping_options}})">
					<span v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery')"></span>
					<span v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery')"></span>
					<span v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery')"></span>
					<span v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery')"></span>
					<template v-if="item.status == 5">
						<span if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium_title')"></span> <span v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
					</template>
					<template v-else-if="item.status != 5">
						<span v-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium').replace('%s%', options.fast_shipping_titles?.join(''))"></span>
						<template v-if="options.id != 'bb_fast'">
							<span v-if="new Date(options.min_delivery_date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', shippingDate[index])"></span>
							<span v-else-if="new Date(options.min_delivery_date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', shippingDate[index])"></span>
							<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', shippingDate[index])"></span>
						</template>
					</template>
				</div>
			</template>
		</template>
	</CatalogProductContainer>

	<CatalogProductContainer class="icon stores flyout" v-if="item.status != 7 && item.shipping_options?.some(obj => obj.id === 'p')" @click="modal.open('flyout', {mode: 'stores', header: true, footer: true, headerIcon: 'stores', content: item?.warehouses_display || []})">
		<template #title>
			<template v-if="item.status == '4' && item.warehouses_display"><BaseCmsLabel code="item_delivery_tab_title" /></template>
			<template v-else>
				<BaseCmsLabel code="item_stores_tab_title" /> 
				<template v-if="item.warehouses_display?.length">({{ item.warehouses_display?.filter(warehouse => warehouse.available_qty > 0).length }})</template>
			</template>
		</template>
		<template #content>
			<div class="cd-container-content">
				<template v-if="item.status == 5">
					<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_time_of_delivery_preorder_single').replace('%s%', item.warehouses_single_pickup_display.title).replace('%s2%', formatDate(item.shipping_date))"></span>
					<span v-else v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
				</template>
				<template v-else-if="item.shipping_options?.some(obj => obj.id === 'p' && obj.active == true)">
					<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_delivery_pickup_single').replace('%s%', item.warehouses_single_pickup_display.title)"></span>
					<span v-else v-html="labels.get('item_delivery_pickup_store')"></span><span v-if="new Date(date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', pickupDate)"></span>
					<span v-else-if="new Date(date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', pickupDate)"></span>
					<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', pickupDate)"></span>
				</template>
			</div>
		</template>
	</CatalogProductContainer>
</template>

<script setup>
	const labels = useLabels();
	const {formatDate} = useText();
	const props = defineProps({
		item: Object,
	});
	const modal = useModal();

	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	const calendarMonth = ['januarja', 'februarja', 'marca', 'aprila', 'maja', 'junija', 'julija', 'avgusta', 'septembra', 'oktobra', 'novembra', 'decembra'];
	const calendarDays = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote'];

	const shippingDate = computed(() => {
		return props.item?.shipping_options?.map((item) => {
			const shippingDateDay = new Date(item.min_delivery_date * 1000).getDay();
			const shippingDateMonth = new Date(item.min_delivery_date * 1000).getMonth();

			if (calendarDays[shippingDateDay] && calendarMonth[shippingDateMonth]) {
				if (new Date(item.min_delivery_date * 1000).toDateString() === new Date().toDateString() || new Date(item.min_delivery_date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
					return new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				} else {
					return calendarDays[shippingDateDay] + ', ' + new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				}
			} else {
				return new Date(item.min_delivery_date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
			}
		});
	});

	const date = (props.item.status == 5) ? props.item.shipping_date : props.item.shipping_options?.find(option => option.id === 'p')?.min_delivery_date || null;
	const pickupDate = computed(() => {
		const pickupDateDay = new Date(date * 1000).getDay();
		const pickupDateMonth = new Date(date * 1000).getMonth();

		if (calendarDays[pickupDateDay] && calendarMonth[pickupDateMonth]) {
			if (new Date(date * 1000).toDateString() === new Date().toDateString() || new Date(date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
				return new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			} else {
				return calendarDays[pickupDateDay] + ', ' + new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			}
		} else {
			return new Date(date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
		}
	});

	// Remap shipping options to match flyout format (cart and product have different format)
	function shippingFlyout() {
		const content = {
			shipping_dates: shippingDate,
			date: props.item.shipping_date,
			status: props.item.status,
			shipping_options: props.item.shipping_options
		};

		modal.open('flyout', {mode: 'shipping', header: true, headerIcon: 'shipping', content});
	}
</script>

<style scoped lang="less">
	.cd-shipping-desc{
		padding-bottom: 5px;
		span{padding-right: 5px;}
	}
</style>