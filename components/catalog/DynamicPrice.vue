<template>
	<div class="dynamic-price" v-if="item.is_available">
		
		<!--
		<div class="cd-conf-price-section">
			<div class="cd-conf-price-header">
				<BaseCmsLabel code="configured_price" tag="span" class="cd-conf-price-title" />
				<div class="cd-conf-price-remove" @click="dynamicPriceRemove(item)"><BaseCmsLabel code='remove' /></div>
			</div>
			<div class="cd-conf-price">
				<span class="cd-current-price red"><BaseUtilsFormatCurrency :wrap="true" :price="500.56" /></span>
				<div class="cd-conf-price-timer">
					<BaseCmsLabel code="conf_price_valid" />
					<span class="cd-conf-price-timer-bottom">
						<ClientOnly><span class="countdown" v-html="countdownFormatted"></span></ClientOnly>
					</span>
				</div>
			</div>
			<div v-if="labels.get('conf_price_note') != 'conf_price_note'" class="cd-conf-price-note"><BaseCmsLabel code="conf_price_note" tag="span" /></div>
		</div>
		-->
		
		<!--
		<div class="label" :class="{'active': !dynamicPriceActive}">
			<span>
				<BaseCmsLabel code="conf_price_label" />
			</span>
			<span class="label-btn" @click="dynamicPriceSelect(item)"><BaseCmsLabel code="conf_price" /></span>
		</div>
		<div class="desc" v-interpolation>
			<div  class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
			<div  class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
		</div>
		-->



		<div v-if="item.price_custom_prices_cart && item.price_custom_prices_cart_expire" class="cd-conf-price-section">
			<div class="cd-conf-price-header">
				<BaseCmsLabel code="configured_price" tag="span" class="cd-conf-price-title" />
				<div class="cd-conf-price-remove" @click="dynamicPriceRemove(item)"><BaseCmsLabel code='remove' /></div>
			</div>
			<div class="cd-conf-price">
				<span class="cd-current-price red"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom_prices_cart" /></span>
				<div class="cd-conf-price-timer">
					<BaseCmsLabel code="conf_price_valid" />
					<span class="cd-conf-price-timer-bottom">
						<span class="countdown" v-html="countdownFormatted"></span>
					</span>
				</div>
			</div>
			<div v-if="labels.get('conf_price_note') != 'conf_price_note'" class="cd-conf-price-note"><BaseCmsLabel code="conf_price_note" tag="span" /></div>
		</div>
		<template v-else-if="item.extra_price_cart_dynamicprice != 0 && item.extra_price_dynamicprice != 0">
			<div class="cd-conf-price-label" :class="{'active': !dynamicPriceActive}">
				<span><BaseCmsLabel code="conf_price_label" />&nbsp;</span>
				<a href="javascript:void(0)" @click="dynamicPriceSelect(item)"><BaseCmsLabel code="conf_price" /></a>
			</div>
			<template v-if="item.extra_price_mode_dynamicprice">
				<div v-if="item.extra_price_mode_dynamicprice == 'show_form_our'" class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
				<div v-else-if="item.extra_price_mode_dynamicprice == 'show_form_limit'" class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
			</template>
		</template>
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const endpoints = useEndpoints();
	const route = useRoute();
	const {getLastUrlSegment} = useUrl();
	const labels = useLabels();
	const props = defineProps({
		item: Object,
	});

	let mountedTimeout;
	let dynamicPriceDate = ref(null);
	let dynamicPriceActive = ref(false);
	let dynamicPriceCartPriceActive = ref(false);
	
	onMounted(async () => {
		clearInterval(updateDynamicPriceTimer);

		//dynamic price countdown
		dynamicPriceDate.value = props.item?.price_custom_prices_cart_expire;
		if (dynamicPriceDate.value) {
			setInterval(updateDynamicPriceTimer, 1000);
		}
	});

	onBeforeUnmount(() => {
		clearInterval(updateDynamicPriceTimer);
	});	

	async function dynamicPriceSelect(item) {
		if(item?.extra_price_mode_dynamicprice === 'show_price') {
			var itemPriceIntg = parseFloat(item?.extra_price_dynamicprice);
			const options = {
				prices_cart: {
					code: item?.shopping_cart_code,
					category: 'dynamicprice',
					price: itemPriceIntg
				}
			}
			await useApi(`${endpoints.get('_post_hapi_customer')}`, {
				method: 'POST',
				body: options,
			}).then(async (res) => {
				dynamicPriceCartPriceActive.value = true;
				await webshop.fetchCart();
			})

			dynamicPriceActive.value = false;
		} else {
			dynamicPriceActive.value = true;
		}
	}

	async function dynamicPriceRemove(item) {
		const options = {
			remove_prices_cart: item.shopping_cart_code,
		}
		await useApi(`${endpoints.get('_post_hapi_customer')}`, {
			method: 'POST',
			body: options,
		}).then(async (res) => {
			dynamicPriceCartPriceActive.value = false;
			await webshop.fetchCart();
		})

		dynamicPriceActive.value = false;
	}

	watch(
		() => dynamicPriceCartPriceActive.value,
		async (newValue) => {
			const url = getLastUrlSegment(route.path).split('-');
			const productId = url[url.length - 1];
			const slug = url.slice(0, -2).join('-');
			const fetchOptions = {
				item_id: productId,
				item_slug: slug,
			};

			const urlParams = Object.keys(fetchOptions).length ? '?' + new URLSearchParams(fetchOptions).toString() : '';
			await useApi(`${endpoints.get('_get_hapi_catalog_product')}${urlParams}`, {
				method: 'GET',
			}).then(async (res) => {
				item.value = res.data;
				dynamicPriceDate.value = res.data?.price_custom_prices_cart_expire;

				if (dynamicPriceDate.value) {
					setInterval(updateDynamicPriceTimer, 1000);
				}
			})
		}
	)

	//dynamic price countdown
	const remainingTime = ref(0);
	const countdownFormatted = computed(() => {
		const targetTime = new Date(dynamicPriceDate.value * 1000);
		const currentTime = new Date();
		remainingTime.value = Math.max(0, Math.floor((targetTime - currentTime) / 1000));

		const hours = Math.floor(remainingTime.value / 3600);
		const minutes = Math.floor((remainingTime.value % 3600) / 60);
		const seconds = remainingTime.value % 60;

		return `
			<span><span class="value semi-bold hours">${hours}</span><span class="frame"> ur </span></span>
			<span><span class="value semi-bold">${minutes}</span><span class="frame"> min </span></span>
			<span><span class="value semi-bold">${seconds}</span><span class="frame"> s</span></span>
		`;
	});

	function updateDynamicPriceTimer() {
		remainingTime.value = Math.max(0, remainingTime.value - 1);
	};
</script>

<style scoped lang="less">
	.dynamic-price{font-size: 14px;}
	.label{display: flex; gap: 7px; padding-bottom: 6px;}
	.label-btn{
		text-decoration: underline; cursor: pointer;
		&:hover{text-decoration: none;}
	}
	.desc{
		:deep(p){padding: 0 0 6px;}
	}
</style>