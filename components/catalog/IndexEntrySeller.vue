<template>
	<component :is="item.url_without_domain && item.status != 'Suspended' ? NuxtLink : 'div'" :to="item.url_product_page_without_domain ? item.url_product_page_without_domain : item.url_without_domain" class="seller">
		<div class="image">
			<BaseUiImage loading="lazy" :data="item.main_image_upload_path_thumb" default="/images/no-image.jpg" />
			<!--<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width200-height200']" default="/images/no-image.jpg" />-->
		</div>
		<div class="body">
			<div class="title" v-if="item.title">{{item.title}}</div>
			<div class="closed strong" v-if="item.status == 'Close' && (item.closed_from || item.closed_to)">
				<BaseCmsLabel code="seller_closed" />
				(
				<template v-if="item.closed_from">
					{{ formatDate(item.closed_from) }}
				</template>
				<template v-if="item.closed_to"> - {{ formatDate(item.closed_to) }} </template>
				<template v-else> - </template>
				)
			</div>
			<div class="info">
				<template v-if="item.corporate_name">{{item.corporate_name}}, </template>
				<template v-if="item.address">{{item.address}}, </template>
				<template v-if="item.zipcode">{{item.zipcode}}, </template>
				<template v-if="item.city">{{item.city}}, </template>
				<template v-if="item.country">{{item.country}}, </template>
				<template v-if="item.identification_number">
					<BaseCmsLabel code="seller_identification_number" />: {{item.identification_number}},
				</template>
				<template v-if="item.vat_number">
					<BaseCmsLabel code="seller_vat_number" />: {{item.vat_number}}
				</template>
			</div>
			<div class="country" v-if="item.shipping_country">
				<BaseCmsLabel code="seller_shipping_country" /> <strong>{{item.shipping_country}}</strong>
			</div>
			<div class="view" v-if="item.url_without_domain && item.status != 'Suspended'">
				<span><BaseCmsLabel code="view_offers" /></span>
			</div>
		</div>
	</component>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const props = defineProps({
		item: Object
	})

	const {formatDate} = useText();
</script>

<style lang="less" scoped>
	.seller{
		background: #fff; border-radius: var(--borderRadius); display: flex; padding: 25px 20px; gap: 20px; text-decoration: none; color: var(--gray5); font-size: 12px; height: 100%;
		@media (max-width: @t){gap: 10px; padding: 15px 10px;}
	}
	.image{
		flex: 0 0 150px; display: flex; justify-content: center; align-items: flex-start;
		@media (max-width: @t){flex: 0 0 80px;}
	}
	.title{font-size: clamp(16px, 1.5vw, 18px); font-weight: bold; color: var(--textColor);}
	.body{display: flex; flex-direction: column; gap: clamp(8px, 1vw, 13px);}
	.info{
		span{display: block;}
	}
	.view{
		color: var(--blueDark); font-weight: bold;
		span{
			position: relative; display: inline-flex; align-items: center;
			&:after{.icon-arrow-right(); font: 11px/1 var(--fonti); margin-left: 7px;}
		}
	}
</style>
