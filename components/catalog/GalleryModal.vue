<template>
	<BaseThemeUiModal name="gallery-modal" :zoom="true" :wheel-zoom="false" :zoom-toggle="true" :mask-closable="true" @slide-change="onSlideChange">
		<template #thumbs="{swiper, activeIndex}">
			<BaseUiTabs class="tabs" ref="tabs">
				<BaseUiTab id="images" title="Fotografije">
					<div class="thumbnails">
						<div class="thumb" :class="{'active': index == activeIndex}" v-for="(item, index) in images" :key="item.id" @click="swiper.slideTo(index)">
							<BaseUiImage :data="item.file_thumb" loading="lazy" default="/images/no-image.jpg" />
						</div>
					</div>
				</BaseUiTab>
				<BaseUiTab id="videos" title="Videozapisi" v-if="videos.length">
					<div class="thumbnails">
						<div class="thumb thumb-video" :class="{'active': activeIndex == images.length + index}" v-for="(item, index) in videos" :key="item.id" @click="swiper.slideTo(images.length + index)"></div>
					</div>
				</BaseUiTab>
			</BaseUiTabs>
		</template>
	</BaseThemeUiModal>
</template>

<script setup>
	const modal = useModal();
	const items = computed(() => modal.get('gallery-modal')?.items);
	const images = computed(() => items.value.filter(item => item.kind == 'image'));
	const videos = computed(() => items.value.filter(item => item.kind == 'video'));
	
	const tabs = ref();
	function onSlideChange(e) {
		tabs.value.setActiveTab(e.activeIndex < images.value.length ? 'images' : 'videos');
	}
</script>

<style scoped lang="less">
	:deep(.gallery-modal){
		.base-modal-body{
			background: #fff; z-index: 2; border-radius: var(--borderRadius); width: var(--pageWidth); padding: 30px 35px; max-height: 80dvh; position: relative;
			@media (max-width: @l){width: calc(~"100% - 64px");}
			@media (max-width: @m){width: 100%; max-height: 100dvh; border-radius: 0; padding: 15px;}
		}
		.base-modal-mask{background: rgba(0,0,0,0.5);}
		.base-modal-gallery{display: flex; flex-direction: row-reverse; gap: 30px;}
		.base-modal-gallery-main{
			flex-grow: 1; width: 500px; position: relative;
			@media (max-width: @m){width: 100%;}
		}
		.base-modal-gallery-thumbs{
			position: relative; top: auto; bottom: auto; padding: 0; flex: 0 0 205px; overflow: visible;
			@media (max-width: @m){display: none;}
		}
		.base-modal-gallery-nav{position: absolute; left: 0; right: 0;}
		img{max-height: 100%;}
		.swiper-zoom-container{height: 100%; display: flex;}
		.base-modal-toolbar{top: 5px; right: 5px;}
		.base-modal-toolbar-close{
			background: none; width: 50px; height: 50px;
			svg{fill: var(--gray5);}
		}
		.base-modal-toolbar-zoom-in, .base-modal-toolbar-zoom-out{display: none;}
	}
	:deep(.tabs){height: 100%; display: flex; flex-direction: column; flex-grow: 1;}
	:deep(.tabs-nav){
		display: flex; list-style: none; padding: 0; margin: 0; font-size: 15px; font-weight: bold; position: relative; margin-bottom: 35px; color: var(--gray5);
		&:after{.pseudo(auto, 4px); background: var(--gray3); left: 0; right: 0; bottom: -2px; border-radius: 4px;}
		.tab-nav{
			padding: 0 0 7px 0; position: relative; cursor: pointer; flex-grow: 1; text-align: center;
			&.active{
				color: var(--textColor);
				&:after{.pseudo(auto, 4px); background: var(--blueDark); left: 0; right: 0; bottom: -2px; z-index: 1; border-radius: 4px;}
			}
		}
	}
	@media (max-width: @m){
		video{height: auto;}
	}
	:deep(.tabs-content){
		overflow: auto; flex-grow: 1; padding-right: 10px; margin-right: -10px;
			&::-webkit-scrollbar{width: 5px; height: 5px;}
			&::-webkit-scrollbar-thumb{background: var(--blueDark); border: 3px solid transparent; border-radius: 100px;}
			&::-webkit-scrollbar-track {background: var(--gray3); border-radius: 100px;}
			//&::-webkit-scrollbar-track-piece {margin-top: 0; : 24px;}	
	}
	:deep(.swiper-slide){cursor: pointer;}
	.thumbnails{display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; height: 100%; overflow: auto;}
	.thumb{
		border-radius: 8px; background: var(--gray3); aspect-ratio: 1; display: flex; align-items: center; justify-content: center; border: 1px solid transparent; cursor: pointer; overflow: hidden;
		&.active{border: 1px solid var(--blueDark);}
		:deep(img){max-width: 85%; max-height: 85%; height: auto; width: auto; mix-blend-mode: multiply;}
	}
	.thumb-video{
		aspect-ratio: 16/9;
		&:before{.icon-play(); font: 26px/1 var(--fonti); color: var(--gray5); position: absolute; z-index: 1;}
	}
	:deep(video){border-radius: 8px;}
	:deep(.base-modal-gallery-nav-btn), .cursor-icon{
		display: flex; align-items: center; justify-content: center; width: 46px; height: 46px; background: #fff; border-radius: 100%; font-size: 0; line-height: 0; position: absolute; z-index: 1; cursor: pointer; box-shadow: 0px 0.63px 2.5px 0px #00000040;
		svg{display: none;}
		&:before{.icon-arrow-down(); margin-right: 4px; font: 14px/1 var(--fonti); color: var(--gray5); position: absolute; z-index: 1; .rotate(90deg); .transition(color);}
		&.swiper-button-disabled{display: none;}
	}
</style>
