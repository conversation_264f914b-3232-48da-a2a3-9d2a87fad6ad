<template>
	<div v-if="item.item_type_config?.product_data_summary" class="variations">
		<div v-for="(variation, key) in item.item_type_config.product_data_summary" :key="key" class="variation">
			<div class="variation-title">{{ getConfAttributeById(item.item_type_config.attribute_data, key)?.title }}: <strong class="strong">{{ selectedVariationTitle(key) }}</strong></div>
			<div :class="['variation-items', 'variation-item_' + getConfAttributeById(item.item_type_config.attribute_data, key)?.code]">
				<div
					v-for="(variationItem, key) in variation"
					:key="key"
					@click="chooseVar(variationItem, variationItem.data.id, variationItem.data.attribute_id)"
					:class="[
						'attribute',
						{
							'special': variationItem.data.image_upload_path,
							'active': isVariationSelected(variationItem),
							'ac': ['z-montazo', 'brez-montaze'].includes(variationItem.data.slug)
						},
						getClass(variationItem, variationItem.data.code, variationItem.data.attribute_id)
					]">
					<template v-if="['z-montazo', 'brez-montaze'].includes(variationItem.data.slug)">
						<div class="header">{{ variationItem.data.title }}</div>
						<div class="cnt">
							<div class="price">
								<div class="current">{{variationItem.data.price}}</div>
							</div>
						</div>
					</template>
					<template v-else>
						<!--
						<span v-if="variationItem.data.image_upload_path" class="img">
							<BaseUiImage :src="variationItem.data.image_upload_path" width="42" height="42" :alt="variationItem.data.title" />
						</span>
						-->
						<span v-if="!variationItem.data.image_upload_path" class="title">{{ variationItem.data.title }}</span>
					</template>
				</div>
			</div>
		</div>
	</div>

	<!--
	<div v-if="item.type == 'advanced' && item.type_config" class="variations">
		<div class="variation">
			<div class="variation-title">
				{{ item.attribute_title }}: <strong class="strong">{{ advancedItemTitle }}</strong>
			</div>
			<div class="variation-items">
				<div v-for="(item, key) in item.type_config" :key="key" @click="chooseAdvancedVar(key, item.title)" :class="['attribute', 'variation-attribute', 'variation-' + item.code + '', {'active': advancedItem && advancedItem == key}]">
					<span class="title">{{ item.title }}</span>
				</div>
			</div>
		</div>
	</div>
	-->
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
	const {emit, bus} = useEventBus();
	let isAvailable = ref([]);

	// Track selected variations by attribute_id
	const selectedVariations = ref({});

	// Check if a variation is selected
	function isVariationSelected(variationItem) {
		return (props.item.primary_product_id && props.item.primary_product_id == variationItem.data.active) || (props.item.primary_product_data && props.item.primary_product_data.id == variationItem.data.active) || props.item.offer_id == variationItem.items?.[0]?.offer_id
	}

	function selectedVariationTitle(attributeId) {
		// Get variations for the specific attribute
		const attributeVariations = props.item.item_type_config.product_data_summary[attributeId];
		if (!attributeVariations) return '';

		// First check if there's a selected variation for this attribute
		if (selectedVariations.value[attributeId]) {
			const selectedVariation = Object.values(attributeVariations)
				.find((variationItem) => variationItem.data.id == selectedVariations.value[attributeId]);
			if (selectedVariation) return selectedVariation.data.title;
		}

		// If no selected variation, find the initially active one
		const activeVariation = Object.values(attributeVariations)
			.find((variationItem) => isVariationSelected(variationItem));
		
		return activeVariation ? activeVariation.data.title : '';
	}

	function getConfAttributeById(attributes, id) {
		let attribute = null;
		Object.values(attributes).forEach(attr => {
			if(attr.id == id) attribute = attr;
		});
		return attribute;
	}

	//conf item
	function chooseVar(data, id, attributeId) {
		
		// Update selected variation for this attribute
		selectedVariations.value[attributeId] = data.data.id;

		//find all active attributes and exclude the attribute from the group that is clicked
		const activeVariationItems = Object.values(props.item.item_type_config.product_data_summary)
		.flatMap((variation) => Object.values(variation))
		.filter((variationItem) => selectedVariations.value[variationItem.data.attribute_id] && variationItem.data.attribute_id != attributeId)
		.map((variationItem) => selectedVariations.value[variationItem.data.attribute_id]);

		//add the id of attribute on which the user clicked
		activeVariationItems.push(data.data.id);

		//find offer shopping cart code for product that have values from the activeVariationItems array
		const product = Object.values(props.item.item_type_config.product_data).filter((obj) =>
			obj.configurable_attribute_items_ids.every((value) => activeVariationItems.includes(value))
		);

		if(!product.length) {
			// If product array is empty, find the first item with the same id in configurable_attribute_items_ids
			const fallbackProduct = Object.values(props.item.item_type_config.product_data).find((obj) =>
				obj.configurable_attribute_items_ids.includes(id)
			);

			if (fallbackProduct) {
				const productCartCode = [fallbackProduct.offer_shopping_cart_code];
				emit('productUpdate', productCartCode);
			}
		} else {
			const productCartCode = product.map((obj) => obj.offer_shopping_cart_code);

			emit('productUpdate', productCartCode);
		}
	}


	function checkIfAvailable() {
		// Found the object with the matching key
		isAvailable.value = [];
		const itemCode = props.item?.code || null;
		const attrData = props.item?.item_type_config?.attribute_data ? Object.values(props.item?.item_type_config?.attribute_data) : [];
		if(!itemCode || !attrData) return;

		if(itemCode && attrData?.length > 1) {
			let foundObject = null;
			const productData = props.item?.item_type_config?.product_data;
			if(!productData) return;

			for (const key in productData) {
				if (productData[key].code === itemCode) {
					foundObject = productData[key];
					break;
				}
			}

			if (foundObject) {
				const matchingObjects = [];

				for (const key in productData) {
					const attrItemsIds = productData[key]?.configurable_attribute_items_ids || [];
					if (attrItemsIds.includes(foundObject.configurable_attribute_items_ids[0]) ||
						attrItemsIds.includes(foundObject.configurable_attribute_items_ids[1])) {
						matchingObjects.push(productData[key]);
					}
				}

				isAvailable.value = matchingObjects;
			}
		}
	}

	watch(
		() => bus.value,
		async (newValue) => {
			//advanced item
			if (newValue.event === 'confAvailableClass') {
				checkIfAvailable();
				bus.value.event = null;
			}
		}
	);

	function getClass(data, code, attributeId) {
		setTimeout(() => {
			//find all selected attributes and exclude the attribute from the group that is clicked
			const activeVariationItems = Object.values(selectedVariations.value)
				.filter((_, index) => {
					const attrId = Object.keys(selectedVariations.value)[index];
					return attrId != attributeId;
				});

			//add the id of attribute on which the user clicked
			activeVariationItems.push(data.data.id);

			if(isAvailable.value.length) {
				const matchingProduct = isAvailable.value.find(item =>
					item.configurable_attribute_items_ids.every(id => activeVariationItems.includes(id))
				);

				if (matchingProduct) {
					return matchingProduct.is_available ? 'available' : 'unavailable';
				} else {
					return 'hidden';
				}
			}

			return false;
		},100);
	}

	//advanced item
	const advancedItem = ref(null);
	const advancedItemTitle = ref(null);

	//advanced item attribute select
	function chooseAdvancedVar(key, title) {
		advancedItem.value = key;
		advancedItemTitle.value = title;

		emit('advancedItem', {'advancedItem': key, 'advancedItemTitle': title});
	}

	onMounted(() => {
		const firstKey = (props.item.type_config) ? Object.keys(props.item.type_config)[0] : null;
		advancedItem.value = firstKey
		advancedItemTitle.value = (firstKey) ? props.item.type_config[firstKey].title : null
		emit('advancedItem', {'advancedItem': advancedItem.value, 'advancedItemTitle': advancedItemTitle.value});
		if(props.item?.item_type_config) {
			checkIfAvailable();
		}
	});
</script>

<style lang="less" scoped>
	.variations{padding: 0 0 24px;}
	.variation-title{font-size: 16px; padding-bottom: 15px;}
	.variation-items{display: flex; flex-wrap: wrap; gap: 12px;}
	.attribute{
		border-radius: 8px; padding: 8px 15px; border: 2px solid var(--gray2); font-size: 15px; cursor: pointer;
		&.active{
			border-color: var(--blueDark);
		}
	}
	.ac{
		border: 2px solid var(--gray2); border-radius: 8px; overflow: hidden; font-size: 15px; line-height: 1.3; text-decoration: none; color: var(--textColor); padding: 0; width: 135px;
		@media (max-width: @m){font-size: 14px;}
		.header{font-size: 14px; padding: 4px 8px; border-bottom: 1px solid var(--gray2);}
		.cnt{padding: 10px 8px; font-size: 16px; font-weight: bold;}
		.old{font-weight: normal; font-size: 12px; text-decoration: line-through;}
		&.active .header{background: #eaeaf2; border-color: transparent;}
	}
</style>
