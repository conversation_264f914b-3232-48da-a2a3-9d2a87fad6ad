<template>
	<div class="status" v-if="item.status">
		<div v-if="item.status == 1" class="available-qty">
			<template v-if="(item.last_piece_sale != 0 && item.last_piece_sale) && item.warehouses_single_pickup_display">
				<span class="available-last"><BaseCmsLabel code='odprodaja' tag="span" /></span>
			</template>
			<template v-else-if="item.last_piece_sale != 0 && item.last_piece_sale">
				<span class="available-last"><BaseCmsLabel code='odprodaja_2' tag="span" /></span>
			</template>
			<template v-else>
				<span><BaseCmsLabel code='na_zalogi' tag="span" /></span>
			</template>
		</div>
		<div v-else-if="item.status == 2" class="available-qty">
			<span v-if="item.availability_info">{{ labels.get('na_zalogi_dobavitelja').replace('%MIN_DAY%', item.availability_info.min_days).replace('%MAX_DAY%', item.availability_info.max_days) }}</span>
		</div>
		<div v-else-if="item.status == 4" class="available-qty">
			<span class="available-last"><BaseCmsLabel code='na_zalogi_ena' tag="span" /></span>
		</div>
		<div v-else-if="item.status == 5" class="available-qty">
			<template v-if="item.is_available && item.date_available">
				<span>{{ labels.get('na_voljo') }} {{ item.date_available.includes('.') ? item.date_available : formatDate(item.date_available) }}</span>
			</template>
			<template v-else>
				<span class="unavailable"><BaseCmsLabel code='ni_na_zalogi_preorder' tag="span" /></span>
			</template>
		</div>
		<div v-else-if="item.status == 7" class="available-qty">
			<span class="unavailable"><BaseCmsLabel code='ni_na_zalogi' tag="span" /></span>
		</div>
		<div v-else-if="item.status == 9" class="available-qty">
			<span class="unavailable"><BaseCmsLabel code='dalj_ni_na_zalogi' tag="span" /></span>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const {formatDate} = useText();
	const props = defineProps({
		item: Object,
	});
</script>

<style scoped lang="less">
	.available-qty{
		&>span{
			display: flex; align-items: center; padding-left: 24px; font-size: 18px; font-weight: 600; color: #1FB549; position: relative;
			&:before{.icon-check-round(); font: 17px/1 var(--fonti); color: #1FB549; position: absolute; left: 0; font-weight: bold;}
		}
		.unavailable{
			color: var(--textColor); padding-left: 0;
			&:before{display: none;}
		}
	}
</style>