<template>
	<div class="header" :class="[{'icon': item.headerIcon}, item.headerIcon]" v-if="item?.header">
		<slot name="header">
			<div v-if="item.title">{{ stripHtml(item.title) }}</div>
		</slot>
	</div>
	<div class="body">
		<slot name="content">
			<div class="content cms-content" :class="[{'no-header': !item?.header}]" v-interpolation>
				<slot />
			</div>
		</slot>
	</div>
	<div class="footer" v-if="item?.footer">
		<slot name="footer" />
	</div>	
</template>

<script setup>
	const modal = useModal();
	const item = computed(() => modal.get('flyout'));
	const {stripHtml} = useText();
</script>

<style scoped lang="less">
	.header{
		font-size: 24px; padding: 25px calc(var(--flyoutSideOffset) + 50px) 20px var(--flyoutSideOffset); font-weight: bold; display: flex; gap: 10px; border-bottom: 1px solid var(--gray3);
		@media (max-width: @t){font-size: 16px; padding: 17px 50px 13px 15px;}
		:deep(p){padding: 0; margin: 0;}
		&.icon:before{
			.icon-truck(); font: 21px/1 var(--fonti); top: 7px; position: relative;
			@media (max-width: @t){top: 1px;}
		}
		&.stores:before{
			.icon-pin(); font-size: 25px; top: 4px;
			@media (max-width: @t){top: -2px;}
		}
		&.services:before{
			.icon-service(); font-size: 25px; top: 5px;
			@media (max-width: @t){top: -2px;}
		}
		&.insurance:before{
			.icon-insurance(); font-size: 25px; top: 5px;
			@media (max-width: @t){top: -2px;}
		}
		&.fire:before{
			.icon-fire(); font-size: 25px; top: 5px; color: var(--red);
			@media (max-width: @t){top: -2px;}
		}
		&.energy:before{.icon-energy(); font-size: 25px; top: 5px;}
		&.star:before{
			.icon-star2(); font-size: 25px; top: 4px;
			@media (max-width: @t){font-size: 21px; top: -1px;}
		}
		&.seller:before{.icon-store(); font-size: 25px; top: 4px;}
	}
	.body{
		overflow: auto; height: 100%; overscroll-behavior: contain; margin-right: 10px;
		&::-webkit-scrollbar{-webkit-appearance: none; width: 5px; background: var(--gray2); border-radius: 4px;}
		&::-webkit-scrollbar-thumb{background-color: var(--blueDark); border-radius: 4px;}
		&::-webkit-scrollbar-track{border-top: 20px solid var(--white); border-bottom: 20px solid var(--white); border-radius: 100px;}
		&::-webkit-scrollbar-track-piece{margin-top: 20px; margin-bottom: 20px; border-radius: 6px;}
	}
	.content{
		margin: var(--flyoutSideOffset) calc(var(--flyoutSideOffset) / 2) var(--flyoutSideOffset) var(--flyoutSideOffset); padding-right: calc(var(--flyoutSideOffset) + 30px);
		&.no-header{margin-top: var(--flyoutSideOffset);}
		:deep(h2), :deep(h3){padding: 0 0 10px; font-size: 16px; font-weight: bold; line-height: 1.4;}
	}
	.footer{
		background: #fff; padding: 20px var(--flyoutSideOffset); display: flex; gap: 10px; margin-top: auto;
		@media (max-width: @t){flex-direction: column-reverse; padding: 15px; border-top: 1px solid var(--gray3);}
	}
	:deep(.btn){
		width: 50%; height: 60px; font-size: 18px;
		@media (max-width: @t){width: 100%; height: 48px; font-size: 15px;}
	}
</style>