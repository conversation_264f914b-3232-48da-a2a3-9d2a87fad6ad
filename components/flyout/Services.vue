<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel v-if="['service', 'productService'].includes(mode)" code="services_flyout_title" />
			<BaseCmsLabel v-else-if="['insurance', 'productInsurance'].includes(mode)" code="insurances_flyout_title" />
		</template>
		
		<template #content>
			<div v-if="services?.length" class="cd-flyout-service-content" v-interpolation>		
				<div class="service" v-for="service in services" :key="service.id">
					<input v-if="['insurance', 'productInsurance'].includes(mode)" class="special" v-model="currentlySelectedInsurance" type="radio" name="service" :value="service.id" :id="'service-' + service.id" />
					<input v-else class="special" v-model="currentlySelectedServices" type="checkbox" name="service" :value="service.id" :id="'service-' + service.id" />
					<label :for="'service-' + service.id">
						<span class="title">{{ service.title }}</span>
						<span class="price"><BaseUtilsFormatCurrency :price="service.price" /></span>
						<div class="desc" v-if="service.description" v-html="service.description"></div>
					</label>
				</div>
				<div class="service service-none" v-if="['insurance', 'productInsurance'].includes(mode)">
					<input class="special" v-model="currentlySelectedInsurance" type="radio" name="service" value="" id="service-0" />
					<label for="service-0">
						<span class="title title-wide"><BaseCmsLabel code="no_warranty" /></span>
					</label>
				</div>
			</div>
		</template>

		<template #footer>
			<button class="btn btn-outline btn-icon" @click="modal.close()"><BaseCmsLabel code="return_to_product" /></button>
			<button class="btn" @click="updateService()" :disabled="loading">
				<UiLoader mode="dots" v-if="loading" />
				<BaseCmsLabel v-if="!loading" code="flyout_close_confirm" />
			</button>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const modal = useModal();
	const webshop = useWebshop();
	const mode = computed(() => modal.get('flyout')?.mode || null);
	const content = computed(() => modal.get('flyout')?.content || null);

	const currentlySelectedServices = ref([]);
	const currentlySelectedInsurance = ref(null);
	const loading = ref(false);

	const services = computed(() => {
		// Selecting services from cart
		if(mode.value == 'insurance') return content.value?.insurances?.available || [];
		if(mode.value == 'service') return content.value?.services?.available || [];
		
		// Selecting services from product details
		if(mode.value == 'productInsurance') return content.value?.services?.find(service => service.code == 'insurance')?.items || [];
		if(mode.value == 'productService') return content.value?.services?.find(service => service.code == 'service')?.items || [];
		return [];
	});

	watch(content, (newValue, oldValue) => {
		if(!newValue) return;

		// Initially selected services from product details or cart item
		if(['productInsurance', 'productService'].includes(mode.value)) {
			currentlySelectedInsurance.value = useState('productSelectedInsurance').value;
			currentlySelectedServices.value = [...(useState('productSelectedServices').value || [])];
		} else {
			currentlySelectedServices.value = newValue?.services?.selected?.length ? newValue?.services?.selected.map(el => el.id) : [];
			currentlySelectedInsurance.value = newValue?.insurances?.selected?.length ? newValue?.insurances?.selected[0].id : "";
		}
	}, {immediate: true});

	async function updateService() {
		loading.value = true;

		// Selected services are stored in state and used in product details. Otherwise we update the cart item.
		if(['productInsurance', 'productService'].includes(mode.value)) {
			useState('productSelectedInsurance').value = currentlySelectedInsurance.value;
			useState('productSelectedServices').value = currentlySelectedServices.value;
		} else {
			let data = [];

			if(currentlySelectedInsurance.value) data.push(currentlySelectedInsurance.value);
			if(currentlySelectedServices.value?.length) currentlySelectedServices.value.forEach(el => data.push(el));
			
			await webshop.updateProduct([{
				shopping_cart_code: content.value.shopping_cart_code,
				quantity: content.value.quantity,
				services: data.length ? data : [''],
			}]);
		}

		loading.value = false;
		modal.close();
	};	
</script>

<style scoped lang="less">
	input[type=checkbox]+label, input[type=radio]+label{
		padding-left: 0; position: relative; display: block;
		@media (max-width: @t){top: -1px;}
	}
	.title{
		display: block; padding-left: 30px; font-weight: bold; font-size: 16px; padding-right: 100px;
		@media (max-width: @t){font-size: 14px; padding-right: 80px;}	
	}
	.title-wide{padding-right: 0;}
	.desc{
		display: block; padding-top: 10px; font-size: 15px;
		@media (max-width: @t){font-size: 14px;}
		:deep(p){padding: 0;}
	}
	.price{
		font-weight: bold; position: absolute; top: 0; right: 0; font-size: 16px;
		@media (max-width: @t){font-size: 14px;}
	}
	.service{
		padding: var(--flyoutSideOffset); border-bottom: 1px solid var(--gray3);
		@media (max-width: @t){padding: 15px 10px 15px 15px;}
		&:last-child{border-bottom: none;}
	}
</style>