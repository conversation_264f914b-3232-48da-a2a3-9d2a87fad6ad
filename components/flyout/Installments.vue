<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel code="installments_flyout_title" :strip-html="true" />
		</template>
		<div v-if="content?.installment_list?.payment_title" class="installments-title">
			<span v-html="content.installment_list.payment_title"></span>
		</div>
		<div v-if="content?.installment_calculation?.regular" class="installments-table">
			<div v-for="(item, key) in content.installment_calculation.regular" :key="key" class="installments-table-row" v-html="labels.get('installment_single_rate').replace('%RATE%', key).replace('%RATE_PRICE%', item)"></div>
		</div>
		<div v-if="content?.installment_list?.payment_description" class="installments-desc" v-html="content.installment_list.payment_description"></div>
		<div v-else class="installments-desc" v-html="labels.get('informativni_izracun')"></div>
	</FlyoutLayout>
</template>

<script setup>
	const modal = useModal();
	const labels = useLabels();
	const content = computed(() => modal.get('flyout')?.content || null);
</script>

<style scoped lang="less">
	.installments-table{
		margin: 10px 0; font-size: 15px; border-radius: var(--borderRadius); overflow: hidden;
	}
	.installments-table-row{
		padding: 7px 10px;
		:deep(strong){font-weight: normal;}
		&:nth-child(odd){background: var(--gray3);}
	}
	.installments-title{font-weight: bold; font-size: 18px;}
</style>