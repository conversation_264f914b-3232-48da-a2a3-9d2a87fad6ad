<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel code="item_stores_flyout_title" tag="div" />
		</template>
		<div class="stores">
			<template v-if="content?.length">
				<div v-for="item in content" :key="item.id" class="store" :class="{'store-unavailable': item.available_qty <= 0}">
					<div class="indicator"></div>
					<div class="title">
						<NuxtLink v-if="item.url_without_domain" :to="item.url_without_domain" target="_blank">{{ (item.title2) ? item.title2 : item.title }}</NuxtLink>
						<template v-else>{{ (item.title2) ? item.title2 : item.title }}</template>
					</div>
				</div>
			</template>
		</div>
		<template #footer>
			<div class="store-legend">
				<div class="store-legend-item">
					<div class="indicator"></div>
					<div><BaseCmsLabel code="store_available" /></div>
				</div>
				<div class="store-legend-item store-legend-item-unavailable">
					<div class="indicator"></div>
					<div><BaseCmsLabel code="store_not_available" /></div>
				</div>
			</div>
		</template>
	</FlyoutLayout>
</template>

<script setup>
const modal = useModal()
const content = computed(() => {
	if(!modal.get('flyout')?.content) return null;
	
	// Sort locations by available qty and then alphabetically
	const sorted = modal.get('flyout')?.content?.sort((a, b) => {
		if (a.available_qty > 0 && b.available_qty <= 0) {
			return -1;
		} else if (a.available_qty <= 0 && b.available_qty > 0) {
			return 1;
		} else {
			// If quantities are equal, sort alphabetically by title
			const titleA = (a.title2 || a.title).toLowerCase();
			const titleB = (b.title2 || b.title).toLowerCase();
			return titleA.localeCompare(titleB);
		}
	});
	
	return sorted || null;
});
</script>

<style lang="less" scoped>
.store{
	margin-bottom: 12px; display: flex; gap: 10px;
	&:last-child{margin-bottom: 0;}
}
.store-unavailable{
	color: var(--gray6);
	.indicator{background-color: #B6B6B6;}
	.title a{color: var(--gray6);}
}
.indicator{
	width: 12px; height: 12px; border-radius: 50%; background-color: #4CAF50; flex-grow: 0; flex-shrink: 0; margin-top: 5px;
	@media (max-width: @t){margin-top: 4px;}
}
.title{
	font-size: 15px;
	@media (max-width: @t){font-size: 14px;}
	a{color: var(--textColor); text-decoration: none;}
}
.store-legend{
	display: flex; gap: 25px; color: var(--gray5); font-size: 12px;
	.indicator{margin: 0;}
}
.store-legend-item{display: flex; gap: 5px; align-items: center;}
.store-legend-item-unavailable{
	.indicator{background-color: #B6B6B6;}
}
</style>