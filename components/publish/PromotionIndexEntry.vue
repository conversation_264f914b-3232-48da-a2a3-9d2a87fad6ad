<template>
	<NuxtLink class="item" :to="item.external_url ? item.external_url : item.url_without_domain">
		<div class="image">
			<BaseUiImage :data="item?.main_image_upload_path_thumb" default="/images/no-image.jpg" loading="lazy" :alt="item.title" />
			<!--<BaseUiImage :data="item?.main_image_thumbs?.['width800-height600-crop1']" default="/images/no-image.jpg" loading="lazy" :alt="item.title" />-->
		</div>
		<div class="content">
			<div class="title">{{item.title}}</div>
			<div class="short-description" v-if="item.short_description" v-html="stripHtml(item.short_description)"></div>
			<div class="footer">
				<div class="category footer-item" v-if="item.category_title">{{item.category_title}}</div>
				<div class="date footer-item" v-if="item.datetime_published"><BaseUtilsFormatDate :date="item.datetime_published" format="DD MMM, YYYY" /></div>
			</div>
		</div>
	</NuxtLink>
</template>

<script setup>
const {stripHtml} = useText();
const props = defineProps({
	item: Object
})
</script>

<style scoped lang="less">
	.item{
		background: #fff; overflow: hidden; font-size: 12px; line-height: 1.4; text-decoration: none; color: var(--textColor); border-radius: var(--borderRadius); display: flex; flex-direction: column; text-wrap: pretty;
		@media (max-width: @ms){border-radius: 0; margin: 0 calc(var(--wrapperMargin) * -1);}
		&:hover .title{color: var(--blueDark);}
	}
	.content{padding: 15px 17px 18px; flex-grow: 1; display: flex; flex-direction: column;}
	.title{font-size: 18px; font-weight: bold; margin-bottom: 10px; .transition(color);}
	.footer{display: flex; flex-wrap: wrap; column-gap: 15px; row-gap: 8px; color: var(--blueDark); margin-top: auto;}
	.footer-item{
		position: relative; display: flex; align-items: center; gap: 5px;
		&:before{.icon-layers(); font: 15px/1 var(--fonti);}
	}
	.short-description{padding-bottom: clamp(13px, 2vw, 20px); flex-grow: 1;}
	.date{
		text-transform: capitalize; position: relative;
		&:before{.icon-date(); font: 14px/1 var(--fonti); margin-top: -1px;}
	}
	.image{
		&:deep(img){display: block; width: 100%; margin: auto;}
	}

	.promo-special-item{
		.title{
			font-size: clamp(16px, 2vw, 24px); line-height: 1.3;
			@media (max-width: @m){order: 2;}
		}
		@media (max-width: @m){
			margin: 0; border-radius: var(--borderRadius);
			.footer{order: 1; padding-bottom: 10px;}
			.title{order: 2;}
			.short-description{order: 3; padding: 0;}
		}
	}
	.promo-special-item-2, .promo-special-item-3{
		width: auto; flex-direction: row; flex-grow: 1;
		@media (max-width: @m){flex-direction: column;}
		.image{
			width: 55%; flex-grow: 0; flex-shrink: 0;
			@media (max-width: @m){width: auto;}
			:deep(img){width: 100%; height: 100%; object-fit: cover;}
		}
	}
	.promo-special-item-3{
		flex-direction: row-reverse;
		@media (max-width: @m){flex-direction: column;}
	}
</style>