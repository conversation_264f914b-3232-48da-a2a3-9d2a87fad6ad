<template>
	<BaseUiAccordion class="accordion" v-if="props.category?.children?.length">
		<BaseUiAccordionPanel v-for="subcategory in props.category.children" :active="subcategory.id == props.category.id" :key="subcategory.id" :id="subcategory.id" v-slot="{onToggle, active}">
			<div class="accordion-panel" :class="{'active': active}">
				<div class="accordion-panel-title" @click="onToggle">{{ subcategory.title }}<span class="toggle-icon" :class="{'active': active}"></span></div>
				<div class="accordion-panel-content" v-interpolation>
					<div class="cms-content" v-html="subcategory.short_description" />
					<BasePublishPostsWidget :fetch="{category_code_only: [subcategory.code], response_fields: ['id', 'title', 'url_without_domain']}" v-slot="{items}">
						<ul class="services-list" v-if="items?.length">
							<li v-for="post in items" :key="post.id">
								<NuxtLink :to="post.url_without_domain">{{ post.title }}</NuxtLink>
							</li>
						</ul>
					</BasePublishPostsWidget>
				</div>
			</div>
		</BaseUiAccordionPanel>
	</BaseUiAccordion>
</template>

<script setup>
	const props = defineProps({
		category: {
			type: Object,
			required: true
		}
	})
</script>

<style scoped lang="less">
	.accordion{
		display: flex; gap: var(--elementGap); flex-direction: column; padding-bottom: 40px;
		@media (max-width: @m){
			margin: 0 calc(var(--wrapperMargin) * -1);
		}
	}
	.accordion-panel{
		background: #fff; border-radius: var(--borderRadius);
		@media (max-width: @m){
			border-radius: 0;
		}
		&.active{
			.accordion-panel-content{display: block;}
			.accordion-panel-title{padding-bottom: 15px;}
		}
	}
	.accordion-panel-title{
		padding: 23px 25px; font-size: clamp(16px, 2vw, 24px); font-weight: bold; display: flex; align-items: center; justify-content: space-between; gap: 20px; cursor: pointer;
		@media (max-width: @m){
			padding: 20px var(--wrapperMargin);
		}
	}
	.accordion-panel-content{
		padding: 0 25px 20px; display: none;
		@media (max-width: @m){padding: 0 var(--wrapperMargin);}
	}
	.services-list{
		list-style: none; padding: 0; margin: 0;
		li{padding: 0 0 5px;}
		a{
			text-decoration: none;
			&:hover{color: var(--blueDark); text-decoration: underline;}
		}
	}
</style>