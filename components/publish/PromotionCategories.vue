<template>
	<div v-if="category.children?.length" class="subcategories">
		<UiSwiper :options="{
			breakpoints: {
				1300: {
					slidesPerView: 10,
					spaceBetween: 25,
					slidesPerGroup: 6
				},
				980: {
					slidesPerView: 8,
					spaceBetween: 20,
					slidesPerGroup: 6
				},
				500: {
					slidesPerView: 4.5,
					spaceBetween: 20,
					slidesPerGroup: 4
				},
				0: {
					slidesPerView: 3.5,
					spaceBetween: 20,
					slidesPerGroup: 3
				},
			}
		}" name="promotion-categories">	
			<BaseUiSwiperSlide>
				<NuxtLink :to="category.url_without_domain" class="subcategory" :class="{'active': route.path == category.url_without_domain}">
					<div class="subcategory-image">
						<img src="~/assets/images/fire.svg" alt="">
					</div>
					<div class="subcategory-title">
						<BaseCmsLabel code="show_all_promotions" />
					</div>
				</NuxtLink>
			</BaseUiSwiperSlide>
			<BaseUiSwiperSlide v-for="child in category.children" :key="child.id">
				<NuxtLink :to="child.url_without_domain" class="subcategory" :class="{'active': route.path == child.url_without_domain}">
					<div class="subcategory-image">
						<BaseUiImage :data="child?.main_image_thumbs?.['width200-height200']" default="/images/no-image.jpg" :alt="child.title" />
					</div>
					<div class="subcategory-title">
						{{child.title}}
					</div>
				</NuxtLink>
			</BaseUiSwiperSlide>
		</UiSwiper>
	</div>
</template>

<script setup>
	const route = useRoute();
	const props = defineProps({
		category: Object
	})
</script>

<style scoped lang="less">
	.wrapper{margin: auto;}
	:deep(.swiper-slide){height: auto;}
	:deep(.swiper){overflow: initial;}
	:deep(.swiper-button){margin-top: -22px;}
	.subcategories{padding-bottom: 30px; padding-top: 20px;}
	.subcategory{
		display: flex; flex-direction: column; color: var(--gray5); text-decoration: none; text-align: center; text-wrap: balance; max-width: 150px;
		&:hover, &.active{
			color: var(--blueDark);
			.subcategory-image{
				border-color: var(--blueDark);
			}
		}
	}
	.subcategory-title{font-size: clamp(14px, 2vw, 15px); font-weight: bold;}
	.subcategory-image{
		width: 100%; aspect-ratio: 1/1; border-radius: 100px; overflow: hidden; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; background: #fff; border: 2px solid #fff; .transition(border-color);
		img{max-width: 90%; max-height: 90%; width: auto; height: auto; object-fit: cover;}
	}
</style>