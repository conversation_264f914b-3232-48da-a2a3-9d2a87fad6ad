<template>
	<NuxtLink class="pp" :to="item.url_without_domain">
		<div class="image">
			<BaseUiImage :data="item.main_image_thumbs?.['width480-height220-crop1']" default="/images/no-image.jpg" :alt="item.title" />
		</div>
		<div class="cnt">
			<div class="title">{{ item.title }}</div>
			<div class="short-description" v-if="item.short_description" v-html="stripHtml(item.short_description)"></div>
		</div>
	</NuxtLink>
</template>

<script setup>
	const {stripHtml} = useText();
	const props = defineProps({
		item: {
			type: Object,
			required: true
		}
	})
</script>

<style scoped lang="less">
	.pp{
		background: #fff; text-decoration: none; border-radius: var(--borderRadius); border-radius: var(--borderRadius); overflow: hidden; font-size: 12px; line-height: 1.4; color: var(--textColor); text-wrap: pretty;
		@media (max-width: @ms){border-radius: 0; margin: 0 calc(var(--wrapperMargin) * -1);}
		
		&:hover{
			.title{color: var(--blueDark);}
		}
	}
	.image{
		:deep(img){display: block; width: 100%;}
	}
	.title{font-size: 18px; font-weight: bold; margin-bottom: 10px; .transition(color);}
	.cnt{padding: clamp(15px, 2vw, 20px) clamp(15px, 2vw, 20px);}
</style>