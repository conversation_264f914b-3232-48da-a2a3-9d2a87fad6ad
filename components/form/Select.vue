<template>
	<BaseFormSelect v-bind="$attrs">
		<slot/>
	</BaseFormSelect>
</template>

<style scoped lang="less">
	.base-select-menu{color: #6D6D6D; font-size: clamp(14px, 1.5vw, 15px);}
	:deep(.base-select-menu-header){
		background: #fff; border-radius: 8px; min-height: 42px; display: flex; align-items: center; padding: 0 12px 0 15px; cursor: pointer; gap: 5px;
		&:after{.icon-arrow-fill(); font: 12px/1 var(--fonti);}
		.grid:before{font-size: 21px;}	
	}
	:deep(.base-select-menu-body){background: #fff; box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12); border-radius: 8px; top: calc(~"100% + 4px"); width: 170px; padding: 6px;}
	:deep(.base-select-menu-item){
		padding: 5px 9px; border-radius: 8px; transition: background .3s, color .3s; cursor: pointer;
		&:hover, &.active{background: var(--gray3); color: #000;}
	}
	&.active{
		:deep(.base-select-menu-header){
			color: #000;
			&:after{.scaleY(-1);}
		}
	}
	:deep(.base-select-menu-items){max-height: none;}
	
	.sort:deep(.base-select-menu-header):before{.icon-arrows(); font: 15px/1 var(--fonti);}
	.sort-menu-right{
		:deep(.base-select-menu-body){left: auto; right: 0;}
	}
	.layout{
		:deep(.grid), :deep(.list){
			display: flex; align-items: center; gap: 5px;
			&:before{.icon-grid(); font: 19px/1 var(--fonti);}
		}
		:deep(.list):before{.icon-list(); font-size: 16px;}
		.base-select-menu-body{left: auto; right: 0;}
	}
</style>