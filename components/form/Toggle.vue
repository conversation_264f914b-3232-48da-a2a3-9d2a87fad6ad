<template>
	<div class="toggle">
		<input type="checkbox" :id="props.id">
		<span class="toggle-slider"></span>
	</div>
</template>

<script setup>
	const props = defineProps({
		id: String
	});
</script>

<style lang="less" scoped>
	.toggle{
		position: relative; display: inline-block; width: 45px; height: 20px; flex-shrink: 0;
		input{opacity: 0; width: 0; height: 0;
			&:checked + .toggle-slider{background-color: var(--blue);}
			&:checked + .toggle-slider:before{transform: translateX(25px);}
		}
	}
	.toggle-slider{
		position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .3s; border-radius: 34px;
		&:before{position: absolute; content: ""; height: 16px; width: 16px; left: 2px; bottom: 2px; background-color: white; transition: .3s; border-radius: 50%; box-shadow: 0px 1.24px 2.48px 0px #00000040;}
	}
	.toggle-content{
		.toggle-title{font-size: 16px; font-weight: 500; color: #000; margin-bottom: 4px;}
		.toggle-subtitle{font-size: 14px; color: #666;
			.email{color: #000;}
		}
	}
</style>
