<template>
	<div class="company-oib-field" :class="['input-field input-field-company', {'field-error': errorMessage && errorMessage != 'empty' && isDirty, 'field-success': !errorMessage && company, 'floated-label': floatingLabel}]">
		<div class="input-container">		
			<input type="tel" :name="field.name + '-1'" :id="field.name + '-1'" :placeholder="labels.get('b_company_oib')" :readonly="company" v-model="value" @blur="onBlur($event)" @click="onFocus($event)" @keyup="onKeyUp($event)" />
			<button v-if="loading" class="btn-white btn-spinner"></button>
			<template v-else>
				<button v-if="!company" class="btn-white" @click.prevent="getCompany('add')" :disabled="disabledButton"><BaseCmsLabel code="attach_card" /></button>
				<button v-if="company" class="btn-white" @click.prevent="getCompany('remove')"><BaseCmsLabel code="m_remove_product" /></button>
			</template>
		</div>
		<span class="error" v-if="errorMessage && errorMessage != 'empty' && isDirty" v-html="errorMessage" />
		<span class="error company-cnt" v-if="company">{{ selectedCompany }}</span>
	</div>
</template>

<script setup>
	import {useField, defineRule, useFieldValue, useValidateField} from 'vee-validate';

	const labels = useLabels();
	const webshop = useWebshop();
	const company = ref(null);
	const isDirty = ref(false);
	const loading = ref(false);
	const props = defineProps(['field']);
	const {customerFields, checkoutLoading} = useCheckout();

	// input field
	const fieldIndex = customerFields.value.findIndex(obj => obj.name == props.field.name);
	const validationShema = props.field.related_field ? 'company:' + props.field.related_field : 'company:null';
	const {setErrors, errorMessage, value, resetField} = useField(props.field.name, validationShema, {
		initialValue: props.field.value && customerFields.value[fieldIndex].value,
	});

	let floatingLabel = ref(value.value ? true : false);

	function onFocus(event) {
		if (!['checkbox', 'radio'].includes(value.value)) floatingLabel.value = true;
	}

	function onBlur(event) {
		floatingLabel.value = value.value ? true : false;
	}

	// if field is not valid, return false
	const disabledButton = computed(() => {
		const regex = /^[0-9]*$/i;
		if (!value.value || !regex.test(value.value)) {
			return true;
		}
		return false;
	});

	// set isDirty to true on keyup
	async function onKeyUp(event) {
		if (event.key != 'Tab' && event.key != 'Escape') {
			isDirty.value = true;
		}
	}

	// success message / selected company
	const selectedCompany = computed(() => {
		const l = labels.get('success_company_api');
		if (l) return l.replace('%COMPANY_NAME%', company.value.b_company_name);
	});

	// clear field on parent checkbox update
	if (props.field.related_field) {
		const relatedField = customerFields.value.find(obj => obj.name == props.field.related_field);
		watch(
			() => relatedField.value,
			async () => {
				if (relatedField.value == 0 && value.value) {
					await getCompany('remove');
					isDirty.value = 0;
				}
			}
		);
	}

	// set initial data
	onMounted(() => {
		if (props.field.extra_info.b_company_name) {
			company.value = props.field.extra_info;
		}
	});

	// validate field on related field change. If related field is not active, reset field
	if (props.field.related_field) {
		const relatedField = useFieldValue(props.field.related_field);
		watch(
			() => relatedField.value,
			async () => {
				isDirty.value = false;
				await validate();

				if (relatedField.value == 0 && value.value) {
					await getCompany('remove');
				}
			}
		);
	}

	// validation rules
	defineRule('company', (value, [related], ctx) => {
		if (related != 'null') {
			if (ctx.form[related]) {
				// check if field is empty
				if (!value || !value.length || value.match(/^ *$/) !== null) {
					const l = labels.get('error_not_empty');
					return l ? l : 'error_not_empty';
				}

				// check if field is a number
				const regex = /^[0-9]*$/i;
				if (!regex.test(value)) {
					const l = labels.get('error_number');
					return l ? l : 'error_number';
				}

				if (!props.field.value) {
					return 'empty';
				}

				if (props.field.value && props.field.value != value) {
					return 'empty';
				}

				return true;
			}

			return true;
		}

		return true;
	});

	// update store field value
	function setStoreValue(value, extraInfo) {
		customerFields.value[fieldIndex].value = value;
		customerFields.value[fieldIndex].extra_info = extraInfo;
	}

	// get API data
	// test oib 39411117
	const validate = useValidateField(props.field.name);
	async function getCompany(action) {
		company.value = '';
		checkoutLoading.value = true;

		if (action == 'remove') {
			value.value = '';
			setErrors('');
			setStoreValue();
			floatingLabel.value = false;
		}

		await webshop.fetchCompany({b_company_oib: value.value}).then(async res => {
			// on error
			if (res.data?.errors?.length) {
				setErrors(labels.get('error_company_api'));
				company.value = null;
				checkoutLoading.value = false;
				return res.data.errors;
			}

			// on success
			setErrors('');
			if (res.data.extra_info?.b_company_oib) {
				company.value = res.data.extra_info.b_company_oib;
				value.value = res.data.extra_info.b_company_oib.b_company_oib;
				setStoreValue(value.value, res.data.extra_info.b_company_oib);
			} else {
				company.value = null;
			}

			await validate();

			if (res.success) {
				await webshop.fetchCart();
			}

			checkoutLoading.value = false;
		});
	}
</script>

<style scoped lang="less">
	.company-oib-field{width: calc(~"50% - 6px");
		@media (max-width: @t){width: 100%;}
	}
	input{border-radius: 8px 0 0 8px; border-right: none;}
	button{border-radius: 0 8px 8px 0;}
	.input-container{display: flex; position: relative;}
	.company-cnt{color: var(--blueDark);}
</style>
