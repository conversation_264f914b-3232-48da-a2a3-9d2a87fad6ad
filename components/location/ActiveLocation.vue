<template>
	<div v-if="location" class="location-details">
		<div class="location-title">
			<div class="title">{{ location.title }}</div>
		</div>
		
		<div class="cols">
			<div class="col col1">
				<BaseLocationGoogleMap pin="/images/pin.svg" :locations="[location]" api-key="dev" map-id="locationMap" :map-options="{fullscreenControl: false, cameraControl: false}" :watch-locations="true" :info-window="false">
					<template v-slot="{item}">
						<div class="infoBox">
							<div class="title">{{item.title}}</div>
							<div class="address">{{item.address}}</div>
							<div v-if="item.contact" class="contact" v-html="item.contact"></div>
							<div v-if="item.business_hour" class="working-hours" v-html="item.business_hour"></div>
						</div>
					</template>
				</BaseLocationGoogleMap>
			</div>
			
			<div class="col col2">
				<div class="location-contact">
					<div class="panel-title"><BaseCmsLabel code="store_details" /> </div>
					<p>{{ location.address }}</p>
					<div class="contact" v-html="location.contact"></div>
					<div class="additional" v-if="location.short_description">
						<div class="panel-title"><BaseCmsLabel code="additional_info" /></div>
						<div class="cms-content" v-html="location.short_description" />
					</div>
				</div>
			</div>
			
			<div class="col col3" v-if="location.business_hour">
				<div class="panel-title"><BaseCmsLabel code="business_hour_title" /></div>
				<div class="business-hour" v-html="location.business_hour" />
			</div>
		</div>
		
		<div class="location-directions">
			<button class="btn" @click.prevent="openGoogleMaps"><BaseCmsLabel code="show_directions" /></button>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		location: {
			type: Object,
			required: true	
		}
	});

	// Open Google Maps with directions from current location
	function openGoogleMaps() {
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				position => {
					const userLat = position.coords.latitude;
					const userLng = position.coords.longitude;
					const destination = encodeURIComponent(props.location.address);
					window.open(`https://www.google.com/maps/dir/${userLat},${userLng}/${destination}`);
				},
				error => {
					//console.warn('Geolocation error:', error.code);	
					// Fallback: Open Google Maps with just the destination
					const destination = encodeURIComponent(props.location.address);
					window.open(`https://www.google.com/maps/dir/?api=1&destination=${destination}`);
				}
			);
		} else {
			// Fallback for browsers without geolocation
			const destination = encodeURIComponent(props.location.address);
			window.open(`https://www.google.com/maps/dir/?api=1&destination=${destination}`);
		}
	}
</script>

<style lang="less" scoped>
.location-details{
	padding: 80px 0 0;
	@media (max-width: @m){padding: 30px 0 0;}
}
.location-title{
	font-size: clamp(30px, 3vw, 42px); font-weight: bold; text-align: center; padding-bottom: 40px;
	@media (max-width: @m){
		text-align: left; padding-bottom: 20px;
	}
}
.cols{
	display: flex; gap: 25px; margin-bottom: 25px;
	@media (max-width: @m){flex-wrap: wrap; gap: var(--wrapperMargin); margin: 0 calc(var(--wrapperMargin) * -1);}
}
.col{
	background: #fff; padding: 20px 25px; border-radius: 12px; color: var(--gray5); flex-grow: 1; width: 33%;
	@media (max-width: @m){width: 100%; border-radius: 0;}
	p{padding: 0 0 10px;}
}
.col1{
	flex: 0 0 33%; background: none; padding: 0;
	@media (max-width: @m){flex-grow: 1;}
}
.contact{
	:deep(a){display: block;}
}
.map{
	height: 100%; min-height: 320px; border-radius: var(--borderRadius); overflow: hidden;
	@media (max-width: @m){min-height: 230px; border-radius: 0;}
}
.panel-title{font-size: clamp(20px, 2vw, 24px); font-weight: bold; padding: 0 0 15px; color: var(--textColor);}
.location-directions{
	text-align: left;
	@media (max-width: @m){display: none;}
}
.btn{min-width: 270px;}
.additional{
	padding: 20px 0 0;
	:deep(ul), :deep(ol){margin-bottom: 0;}
}
</style>
