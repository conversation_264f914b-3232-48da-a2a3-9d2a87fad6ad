<template>
	<BaseCmsRotator v-slot="{items}" :fetch="{code: 'store_orders', limit: 4, response_fields: ['id','title','content']}">
		<div class="orders" v-if="items?.length">
			<div class="header">
				<div class="title"><BaseCmsLabel code="store_order_title" /></div>
				<div class="content" v-if="labels.get('store_order_content')" v-html="labels.get('store_order_content')" />
			</div>
			<div class="items">
				<div class="item" v-for="item in items" :key="item.id">
					<div class="item-title">{{ item.title }}</div>
					<div v-html="item.content" />
				</div>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.orders{
		padding: 80px 0 0;
		@media (max-width: @m){padding: 40px 0 0;}
	}
	.header{
		text-align: center;
		@media (max-width: @m){text-align: left;}
	}
	.title{
		font-size: clamp(30px, 3vw, 42px); font-weight: bold; padding: 0 0 20px;
		@media (max-width: @m){line-height: 1.3;}
	}
	.content{
		font-size: clamp(15px, 2vw, 16px); padding: 0 0 20px; max-width: 840px; margin: auto; text-wrap: balance;
		@media (max-width: @t){max-width: none;}
		@media (max-width: @m){padding: 0;}
	}
	.items{
		display: flex; gap: 20px; padding: 20px 0 0;
		@media (max-width: @m){flex-direction: column; gap: var(--wrapperMargin); margin: 0 calc(var(--wrapperMargin) * -1); padding-top: 5px;}
	}
	.item{
		background: #fff; border-radius: var(--borderRadius); padding: 20px 30px 30px; font-size: 15px; color: var(--gray5);
		:deep(p:last-of-type){padding-bottom: 0;}
		@media (max-width: @m){border-radius: 0; padding: 20px 25px;}
	}
	.item-title{color: var(--textColor); font-size: 24px; font-weight: bold; padding: 0 0 10px;}
</style>