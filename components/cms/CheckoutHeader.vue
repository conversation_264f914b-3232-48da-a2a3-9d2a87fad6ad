<template>
	<div class="header">
		<div class="wrapper">
			<BaseCmsLogo class="logo" />
			<div class="header-buttons">
				<span class="safe-purchase"><PERSON>gu<PERSON> kupovina</span>
			</div>
		</div>
	</div>
</template>

<style scoped lang="less">
	.header{
		background: var(--blue); color: #fff; font-size: 16px;
		@media (max-width: @m){font-size: 14px;}
	}
	.wrapper{
		height: 85px; display: flex; align-items: center; justify-content: space-between;
		@media (max-width: @m){height: 55px;}
	}
	.logo{
		display: block; flex-shrink: 0; width: 163px; height: 34px; background: url(assets/images/logo.svg) no-repeat center center; background-size: contain; z-index: 1;
		@media (max-width: @m){width: 120px; height: 23px;}
	}	
	.safe-purchase{
		text-decoration: none; color: #fff; font-weight: bold; position: relative; display: flex; align-items: center; gap: 8px;
		&:before{
			.icon-lock(); font: 20px/1 var(--fonti);
			@media (max-width: @m){font-size: 16px;}
		}
	}
</style>