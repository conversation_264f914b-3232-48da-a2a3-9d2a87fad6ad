<template>
	<footer class="footer">
		<div class="f-row1">
			<div class="f-row1-wrapper wrapper">
				<BaseCmsNav code="footer_col1" v-slot="{items}">
					<div class="f-col f-col1" :class="{'active': col1}" v-if="items?.length">
						<div class="f-title" @click="toggleCol(1)"><BaseCmsLabel code="footer_title1" /></div>
						<ul class="f-nav">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</div>
				</BaseCmsNav>

				<BaseCmsNav code="footer_col2" v-slot="{items}">
					<div class="f-col f-col2" :class="{'active': col2}" v-if="items?.length">
						<div class="f-title" @click="toggleCol(2)"><BaseCmsLabel code="footer_title2" /></div>
						<ul class="f-nav">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</div>
				</BaseCmsNav>

				<BaseCmsNav code="footer_col3" v-slot="{items}">
					<div class="f-col f-col3" :class="{'active': col3}" v-if="items?.length">
						<div class="f-title" @click="toggleCol(3)"><BaseCmsLabel code="footer_title3" /></div>
						<ul class="f-nav">
							<li v-for="item in items" :key="item.id">
								<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
							</li>
						</ul>
					</div>
				</BaseCmsNav>

				<div class="f-col f-col4">
					<ClientOnly>
						<NewsletterSignup />

						<div class="support-container">
							<div class="support-title"><BaseCmsLabel code="customer_support" /></div>
							<BaseCmsLabel tag="div" class="support" code="support" />
						</div>
					</ClientOnly>
				</div>
			</div>
		</div>
		<div class="f-row2">
			<div class="f-row2-wrapper wrapper">
				<CmsSelectLanguage />
				<CmsSocial />
			</div>
		</div>
		<div class="f-row3">
			<div class="f-row3-wrapper wrapper">
				<BaseCmsCopyright class="copy" label="copyright" />
				<BaseUtilsVersion />
				<BaseCmsLabel tag="div" class="cards" code="cards" />
			</div>
		</div>
		<div class="f-row4">
			<div class="f-row4-wrapper wrapper">
				<BaseCmsLabel code='eu' tag="div" class="eu-conteiner" />
				<div class="f-trustmark smdWrapperTag"></div>
			</div>
		</div>
	</footer>
</template>

<script setup>
	// toggle columns on mobile
	const col1 = ref(false);
	const col2 = ref(false);
	const col3 = ref(false);
	function toggleCol(num) {
		if(num == 1) col1.value = !col1.value;
		if(num == 2) col2.value = !col2.value;
		if(num == 3) col3.value = !col3.value;
	}

	// certificated shop script
	onMounted(async () => {
		var smdWrapper = document.createElement("script"), smdScript;
		smdWrapper.async = true;
		smdWrapper.type = "text/javascript";
		smdWrapper.src = "https://cpx.smind.si/Log/LogData?data=" + JSON.stringify({
			Key: "Slo_862",
			Size: "80",
			Type: "badge",
			Version: 2,
			BadgeClassName: "smdWrapperTag"
		});
		smdScript = document.getElementsByTagName("script")[0];
		smdScript.parentNode.insertBefore(smdWrapper, smdScript);
	});
</script>

<style lang="less" scoped>
	.footer{position: relative;}
	.f-row1{
		padding: 64px 0 52px; background: var(--blueDark);
		@media (max-width: @m){padding: 35px 0;}
	}
	.f-row1-wrapper{
		display: flex; align-items: flex-start; justify-content: space-between;
		@media (max-width: @m){flex-direction: column; margin: 0;}
	}
	.f-col4{
		width: 500px; flex-grow: 0; flex-shrink: 0;
		@media (max-width: @m){padding: 0 12px 40px;}
	}
	@media (max-width: @m){
		.f-col{
			width: 100%; border-bottom: 1px solid rgba(255, 255, 255, 0.15);
			&.active{
				.f-nav{max-height: 1000px; padding-bottom: 20px;}
				.f-title:before{.scaleY(-1);}
			}
		}
		.f-col4{order: 1;}
		.f-col2{order: 2;}
		.f-col1{order: 3;}
		.f-col3{order: 4;}
	}
	.f-title{
		padding-bottom: 16px; font-size: 18px; line-height: 1.4; font-weight: 600; color: var(--white);
		@media (max-width: @m){
			display: flex; align-items: center; padding: 12px; font-size: 16px; position: relative;
			&:before{.icon-arrow-down(); font: 8px/1 var(--fonti); font-weight: normal; color: var(--white); position: absolute; right: 12px; .transition(transform);}
		}
	}
	.f-nav{
		position: relative; list-style: none;
		@media (max-width: @m){max-height: 0; overflow: hidden; padding: 0 35px; transition: max-height 0.3s ease-in-out, padding-bottom 0.3s ease-in-out;}
		li{
			display: block; margin-bottom: 12px; font-size: 0; line-height: 0;
			@media (max-width: @m){margin-bottom: 0; padding: 0;}
			a{
				display: block; font-size: 15px; line-height: 1.4; color: var(--white); text-decoration: none; opacity: 0.8; .transition(opacity);
				@media (min-width: @t){
					&:hover{opacity: 1;}
				}
				@media (max-width: @m){font-size: 14px; line-height: 2; color: rgba(255, 255, 255, 0.70);}
			}
		}
	}
	.support-container{flex-grow: 1; position: relative;}
	.support-title{
		margin-bottom: 12px; font-size: 18px; font-weight: 600; color: var(--white);
		@media (max-width: @m){font-size: 16px;}
	}
	.support{
		font-size: 15px;
		&>*{display: block; padding-bottom: 12px; color: var(--white); text-decoration: none;}
		:deep(a){color: var(--white); text-decoration: none;}
		:deep(.support-tel){font-size: 24px;}

		@media (max-width: @m){
			font-size: 14px;
			&>*{padding-bottom: 8px;}
			:deep(.support-tel){font-size: 20px; line-height: 1.3;}
		}
	}

	.f-row2{
		padding-bottom: 32px; background: var(--blueDark); position: relative;
		@media (max-width: @m){padding-bottom: 24px;}
	}
	.f-row2-wrapper{
		display: flex; align-items: center; justify-content: space-between;
		@media (max-width: @m){flex-direction: column-reverse; align-items: flex-start;}
	}
	.f-row3{
		margin-bottom: 32px; padding: 40px 0; background: var(--blueDark); border-top: 1px solid rgba(255, 255, 255, 0.15); border-bottom: 1px solid rgba(255, 255, 255, 0.15); position: relative;
		&:before{.pseudo(auto,32px); background: var(--blueDark); left: 0; right: 0; top: calc(~"100% - -1px");}
		@media (max-width: @m){
			border: none; margin: 0; padding: 0 0 42px;
			&::before{content: none;}
		}
	}
	.f-row3-wrapper{
		display: flex; align-items: center; justify-content: space-between;
		@media (max-width: @m){display: block;}
	}
	.copy{font-size: 15px; color: var(--white);}
	.cards {
		margin-left: 20px; display: inline-flex; align-items: center;
		:deep(p){display: flex; align-items: center;}
		:deep(a){display: flex; margin-left: 8px;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 20px;}

		@media (max-width: @m){
			margin: 12px 0 0;
			:deep(a){margin: 0 8px 0 0;}
		}
	}

	.f-row4{padding: 24px 0; background: var(--white);}
	.f-row4-wrapper{
		display: flex; align-items: center; justify-content: start;
		@media (max-width: @m){display: block;}
	}
	.eu-conteiner{
		display: flex; align-items: center; flex-grow: 1; font-size: 14px; font-weight: 500;
		:deep(br){display: none;}
		:deep(p){margin-right: 12px; padding-bottom: 0;}
		:deep(p:first-child){display: flex; align-items: center;}
		:deep(img){width: auto; height: auto; max-height: 40px;}
		:deep(a){display: block; color: var(--blue); text-decoration: none;}

		@media (max-width: @m){
			flex-wrap: wrap; font-size: 11px;
			:deep(img){max-height: 21px;}
			:deep(p:last-child){margin-top: 12px;}
		}
	}
	.f-trustmark{
		display: flex; align-items: center; flex-shrink: 0; margin-left: 10%;
		:deep(a){display: inline-block; height: 70px;}
		:deep(img){height: 70px;}
		@media (max-width: @m){margin: 12px 0 0;}
	}
</style>