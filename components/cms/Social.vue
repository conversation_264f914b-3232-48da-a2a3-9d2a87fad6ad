<template>
	<div class="social-container">
		<div class="social-title"><BaseCmsLabel code="social_title" /></div>
		<BaseCmsLabel code="social" tag="div" class="social" />
	</div>
</template>

<style scoped lang="less">
	.social-container{
		flex-shrink: 0; text-align: right;
		@media (max-width: @m){padding-bottom: 40px;}
	}
	.social-title{
		margin-bottom: 12px; font-size: 18px; font-weight: 500; color: var(--white); text-align: right;
		@media (max-width: @m){font-size: 16px; text-align: left;}
	}
	.social{
		display: flex; gap: 12px; font-size: 0; line-height: 0;
		:deep(a){
			display: flex; align-items: center; justify-content: center; width: 18px; height: 18px; text-decoration: none; position: relative;
			@media (max-width: @m){width: 24px; height: 24px;}
			&:after{
				.icon-fb(); font: 18px/1 var(--fonti); color: var(--white); z-index: 1;
				@media (max-width: @m){font-size: 24px;}
			}
			&.ig:after{.icon-ing();}
			&.yt{
				@media (max-width: @m){width: 32px;}
				&:after{
					.icon-yt(); font-size: 15px;
					@media (max-width: @m){font-size: 23px;}
				}
			}
			&.tik-tok:after{.icon-tik-tok();}
			&.ln:after{.icon-linkedin();}
		}
		@media (max-width: @m){gap: 18px;}
	}	
</style>