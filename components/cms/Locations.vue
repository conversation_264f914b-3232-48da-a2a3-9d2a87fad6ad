<template>
	<div class="locations">
		<div class="header" v-if="props.header">
			<div class="stores-headline"><BaseCmsLabel code="stores_headline" /></div>
			<div class="stores-title"><BaseCmsLabel code="stores_title" /></div>
		</div>

		<BaseLocationPoints v-slot="{items}" :place="props.activePlace">
			<div class="content" v-if="items?.length">
				<div class="col col1">
					<div class="location-items">
						<div class="location-item" v-for="item in items" :key="item.id" :class="{active: activeLocation?.code === item.code}" @click="$emit('update:modelValue', activeLocation)">
							<div class="title">{{item.title}}</div>
							<div class="address" v-html="item.address"></div>
							<div class="button" @click="googleMap.openPopup(item.code)"><PERSON><PERSON><PERSON><PERSON> trgo<PERSON></div>
						</div>
					</div>
				</div>
				<div class="col col2">
					<BaseLocationGoogleMap pin="/images/pin.svg" :locations="items" ref="googleMap" api-key="dev" v-model="activeLocation" :info-window-options="{offsetX: 0, offsetY: 0}">
						<template v-slot="{item}">
							<div class="infoBox">
								<div class="title">{{item.title}}</div>
								<div class="address" v-html="item.address"></div>
								<div v-if="item.contact" class="contact" v-html="item.contact"></div>
								<div v-if="item.business_hour" class="working-hours" v-html="item.business_hour"></div>
							</div>
						</template>
					</BaseLocationGoogleMap>
				</div>
			</div>
		</BaseLocationPoints>
	</div>
</template>

<script setup>
	const props = defineProps({
		header: {
			type: Boolean,
			default: true,
		},
		activePlace: Object,
	});
	const googleMap = ref(null);
	const activeLocation = ref(null);
</script>

<style lang="less" scoped>
.locations{
	margin-top: 20px; background: #fff; border-radius: var(--borderRadius); padding: 5%;
	@media (max-width: @m){
		margin: 13px -12px 0; border-radius: 0; padding: 15px;
	}
	.header{
		padding: 0 0 50px;
		@media (max-width: @m){
			padding: 0 0 30px;
		}
	}
	&.no-spacing{
		margin: 0;
		@media (max-width: @m){margin: 0;}
	}
}
.content{
	display: flex; flex-direction: row; gap: 20px; height: 470px;
	@media (max-width: @m){
		flex-direction: column-reverse; height: auto; gap: 40px;
	}
}
.location-item{
	padding: 20px 20px 20px 25px; font-size: 15px; color: var(--gray5); position: relative; direction: ltr;
	@media (max-width: @m){
		font-size: 14px; padding: 0 25px 0 0; width: calc(100vw / 2.5); flex-grow: 0; flex-shrink: 0;
	}
	.title{
		font-size: 24px; font-weight: 600; color: var(--textColor); padding: 0 0 5px;
		@media (max-width: @m){
			font-size: 20px; line-height: 1.3;
		}
	}
	.address{padding: 0 0 10px;}
	.button{
		color: var(--blueDark); font-weight: 600; position: relative; cursor: pointer; display: flex; align-items: center; gap: 7px; line-height: 1;
		&:after{.icon-arrow-right(); font: 14px/1 var(--fonti); margin-top: 2px;}
	}
}
.col1{
	flex: 0 0 25%;  position: relative;
	@media (max-width: @m){
		flex: 0 0 100%;
	}
}
.location-items{
	overflow: auto; height: 100%; direction: rtl;
	@media (max-width: @m){
		direction: ltr; margin: 0 -15px; display: flex; padding: 0 15px 15px;
	}
	&::-webkit-scrollbar{width: 5px;}
	&::-webkit-scrollbar-thumb{background: var(--blueDark); border-radius: 100px;}
	&::-webkit-scrollbar-track{background: var(--gray2); border-radius: 100px;}
	@media (max-width: @m){
		&::-webkit-scrollbar{height: 5px;}
		&::-webkit-scrollbar-thumb{height: 5px;}
		&::-webkit-scrollbar-track{margin-left: 15px; margin-right: 15px;}
	}
}
.col2{flex-grow: 1;}
.stores-headline{
	font-size: 18px; font-weight: 600; color: var(--blueDark); padding: 0 0 5px;
	@media (max-width: @m){
		font-size: 12px;
	}
}
.stores-title{
	font-size: 42px; font-weight: 600; letter-spacing: -1px;
	@media (max-width: @m){
		font-size: 20px;
	}
}
.map{
	height: 100%; width: 100%; border-radius: var(--borderRadius);
	@media (max-width: @m){
		height: 230px;
	}
}
:deep(.infoBox-window){
	padding: 25px 20px; margin-bottom: 10px; background: #fff; border-radius: var(--borderRadius); font-size: 15px; color: #818181; max-width: 270px; position: relative; line-height: 1.4; box-shadow: 0px 4px 12px 0px #00000040;
	&:before{.pseudo(20px, 20px); background: #fff; position: absolute; bottom: -10px; left: 50%; margin-left: -10px; transform: rotate(45deg);}
	.infoBox>div:not(.title){padding: 2px 0;}
	.title{font-size: 18px; color: var(--textColor); font-weight: 600; line-height: 1.3; padding: 0 0 5px;}
	.infoBox-close{
		position: absolute; top: 2px; right: 2px; font-weight: bold; width: 30px; height: 30px; font-weight: bold; display: flex; align-items: center; justify-content: center; font-size: 0; cursor: pointer;
		&:before{.icon-x(); font: 11px/1 var(--fonti);}
	}
	.contact{
		a{text-decoration: none; color: #818181; display: block;}
	}
}
:deep(.gm-style-iw-chr) {
	display: none !important;
}

.locations2{
	padding: 0;
	.col1{
		padding: 0 0 0 20px; flex: 0 0 30%;
		@media (max-width: @m){padding: 0; flex: 0 0 100%;}
	}
	.location-items{
		display: flex; flex-direction: column; gap: 12px; padding: 25px 0 25px 20px;
		&::-webkit-scrollbar-track{margin-block: 20px;}
		@media (max-width: @m){flex-direction: row; padding: 25px var(--wrapperMargin) 15px; margin: 0;}
	}
	.location-item{
		border: 1px solid var(--gray2); border-radius: var(--borderRadius);
		@media (max-width: @m){width: 275px; padding: 20px;}
		&.active{border-color: var(--blueDark);}
	}
	.map{
		border-top-left-radius: 0; border-bottom-left-radius: 0;
		@media (max-width: @m){
			border-radius: var(--borderRadius);
		}
	}
	@media (max-width: @m){
		.content{flex-direction: column; gap: 15px;}
		.col2{padding: 0 var(--wrapperMargin) 25px;}
	}
}
</style>