<template>
	<BaseCmsRotator :transform-items="transformItems" :fetch="{code: 'promo_double', limit: 15, response_fields: ['id','link','url_without_domain','link_target_blank','template','image_upload_path','image_thumbs', 'title', 'element_button']}" v-slot="{items}">
		<div v-if="items?.length" class="promo-double">
			<BasePublishCategory :fetch="{code: ['promotions'], response_fields: ['url_without_domain']}" v-slot="{item: category}">
				<div class="wrapper">
					<CmsSectionHeader>
						<BaseCmsLabel code="promo_title" />
						<template #btn>
							<NuxtLink :to="category?.url_without_domain" class="widget-btn-show-all">
								<BaseCmsLabel code="show_all" />
							</NuxtLink>
						</template>
					</CmsSectionHeader>
				</div>

				<div class="promo-double-section">
					<UiSwiper
						name="promo-double"
						:options="{
							slidesPerView: 1.7,
							slidesPerGroup: 1,
							spaceBetween: 12,
							breakpoints: {
								980: {
									slidesPerView: 5,
									slidesPerGroup: 5,
									spaceBetween: 24,
								},
								650: {
									slidesPerView: 2.5,
									slidesPerGroup: 2,
									spaceBetween: 12,
								}
							}
						}">
						<template v-for="(slide, index) in items" :key="index">
							<BaseUiSwiperSlide class="slide">
								<template v-for="item in slide" :key="item.id">
									<NuxtLink v-if="item.id == 'readmore'" :to="category.url_without_domain" class="promo-read-more"><BaseCmsLabel code="read_more" /></NuxtLink>
									<NuxtLink v-else :to="item.url_without_domain" :target="item.link_target_blank == 1 ? '_blank' : null" class="promo-double-item">
										<template v-if="item.image_upload_path && item.template == 'template_hp_promo_big'">
											<BaseUiImage loading="lazy" :data="item.image_thumbs?.['width269-height477-crop1']" />
										</template>
										<template v-else-if="item.image_upload_path">
											<BaseUiImage loading="lazy" :data="item.image_thumbs?.['width269-height226-crop1']" />
										</template>
										<span v-if="item.title" class="promo-double-title" v-html="item.title"></span>
										<span v-if="item.element_button" class="promo-double-button" v-html="item.element_button"></span>
									
									</NuxtLink>
								</template>
							</BaseUiSwiperSlide>
						</template>
					</UiSwiper>
				</div>
			</BasePublishCategory>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	// Append readmore item and separate items by 2 for swiper grid
	function transformItems(data) {
		let items = data;
		
		if (items?.length && !items.find(item => item.id === 'readmore')) {
			items.push({
				id: 'readmore',
			});
		}

		const chunkSize = 2;
		const chunks = [];
		
		if (items?.length) {
			let tempGroup = [];

			for (let i = 0; i < items.length; i++) {
				const item = items[i];

				if (item.template === 'template_hp_promo_big') {
					if (tempGroup.length) {
						chunks.push(tempGroup);
						tempGroup = [];
					}
					chunks.push([item]); // Add special item as its own slide
				} else {
					tempGroup.push(item);
					if (tempGroup.length === chunkSize) {
						chunks.push(tempGroup);
						tempGroup = [];
					}
				}
			}

			if (tempGroup.length) {
				chunks.push(tempGroup);
			}

			items = chunks;
		}

		return items;
	}
</script>

<style scoped lang="less">
	.promo-double{margin-bottom: var(--widgetSpacing);}
	:deep(.swiper){overflow: initial;}
	.slide{display: flex; flex-direction: column; gap: var(--elementGap);}
	.promo-double-item{
		display: flex; align-items: center; justify-content: center; height: 100%; text-decoration: none; position: relative; border-radius: 12px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: 12px;}
		@media (max-width: @m){
			border-radius: 6px;
			:deep(img){border-radius: 6px;}
		}
	}
	.promo-double-title{
		padding: 0 5%; font-size: 28px; line-height: 1.2; font-weight: 600; letter-spacing: -0.28px; text-align: center; color: var(--white); position: absolute; z-index: 1;
		@media (max-width: @m){font-size: 20px; letter-spacing: normal;}
	}
	.promo-double-button{
		display: flex; align-items: center; padding-right: 36px; font-size: 12px; line-height: 1.2; font-weight: 600; color: var(--white); position: absolute; right: 12px; bottom: 24px;
		&:before{.pseudo(25px,25px); display: flex; align-items: center; justify-content: center; background: var(--white); border-radius: 100%; .icon-arrow-right(); font: 11px/1 var(--fonti); color: var(--black); position: absolute; right: 0;}
		@media (max-width: @m){bottom: 16px;}
	}
</style>