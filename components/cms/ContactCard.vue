<template>
	<div class="card">
		<div class="card-header">
			<div>
				<slot name="header"></slot>
			</div>
		</div>
		<div class="card-content">
			<slot name="content"></slot>
		</div>
	</div>
</template>

<style lang="less" scoped>
.card{
	flex: 1 1 calc(33.333% - 14px); min-width: 280px; background: #fff; border-radius: var(--borderRadius);
	@media (max-width: @t){
		flex: 1 1 calc(50% - 14px);
	}
	@media (max-width: @m){
		flex: 1 1 100%;
	}
}
.card-header{
	font-size: 28px; min-height: 85px; font-weight: 600; padding: 10px; display: flex; text-align: center; border-bottom: 1px solid var(--gray3); justify-content: center; align-items: center;
	@media (max-width: @m){
		font-size: 20px; min-height: 70px;
	}
	:deep(span){
		font-size: 15px; display: block; font-weight: 400; color: var(--gray5);
		@media (max-width: @m){
			font-size: 14px;
		}
	}
}
.card-content{
	font-size: 18px; padding: 25px 20px; text-align: center;
	:deep(h1){font-weight: bold;}
	@media (max-width: @m){
		font-size: 14px; padding: 20px 15px;
	}
	:deep(p){
		position: relative; display: flex; align-items: center; gap: 10px; padding: 0 0 12px;
		&:last-child{padding-bottom: 0;}
		&:before{
			.icon-clock(); font: 23px/1 var(--fonti); color: var(--blue);
			@media (max-width: @m){
				font-size: 20px;
			}
		}
		&.phone:before{.icon-phone();}
		&.email:before{
			.icon-email(); font-size: 19px;
			@media (max-width: @m){
				font-size: 17px;
			}
		}
		a{color: var(--textColor); text-decoration: none;}
	}
}
</style> 