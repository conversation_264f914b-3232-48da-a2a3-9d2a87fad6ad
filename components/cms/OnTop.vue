<template>
	<div @click="scrollTo('body')" class="to-top"></div>
</template>

<script setup>
	const {scrollTo} = useDom();
</script>

<style scoped lang="less">
	.to-top{
		display: flex; align-items: center; justify-content: center; width: 60px; height: 60px; border-radius: 100%; background: #D2D2D7; text-decoration: none; position: fixed; .transition(all); z-index: 11; opacity: 0; visibility: hidden; cursor: pointer; .transition(background);
		&:after{.icon-arrow-down(); margin-top: -3px; font: 18px/1 var(--fonti); color: var(--blueDark); transform: rotate(180deg); .transition(color);}
		&.active{bottom: 18px; right: 24px; opacity: 1; visibility: visible; text-decoration: none !important;}
		@media (min-width: @t){
			&:hover{background: rgba(154, 154, 154, 0.8);}
		}

		@media (max-width: @m){
			width: 48px; height: 48px;
			&:after{margin-top: -2px; font-size: 14px;}
			&.active{bottom: 28px; right: 16px;}
		}
	}

	.page-catalog-index{
		@media (max-width: @m){
			.to-top{display: none!important;}
		}
	}
</style>