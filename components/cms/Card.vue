<template>
	<div class="card">
		<figure class="card-image">
			<slot name="image" />
		</figure>
		<div class="card-body">
			<slot name="content" />
		</div>
	</div>
</template>

<style scoped lang="less">
	.card{
		background: #fff; border-radius: var(--borderRadius); border-radius: var(--borderRadius); overflow: hidden; font-size: clamp(14px, 1.5vw, 16px); line-height: 1.5; color: var(--textColor); text-wrap: pretty;
		@media (max-width: @m){border-radius: 0; margin: 0 calc(var(--wrapperMargin) * -1);}
	}
	.card-image{
		:deep(img){display: block; width: 100%;}
	}
	.card-body{
		padding: 20px 25px 25px;
		@media (max-width: @m){padding: 15px var(--wrapperMargin) 20px;}
	}
	:deep(.title){
		font-size: 21px; line-height: 1.4; font-weight: 600; padding-bottom: 10px; color: var(--textColor); padding-bottom: 10px;
		a{text-decoration: none; color: var(--textColor);}
	}
	:deep(ul){
		list-style: none; padding: 0; margin: 0;
		li{padding: 3px 0;}
	}
	:deep(p){
		padding-bottom: 10px;
	}
	:deep(.title){font-size: clamp(20px, 2vw, 24px);}
	:deep(a){
		text-decoration: none; color: var(--blueDark); text-underline-offset: 3px;
		&:hover{text-decoration: underline; text-decoration-thickness: 1px;}
	}	
</style>