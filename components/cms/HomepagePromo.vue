<template>
	<BaseCmsRotator :fetch="{code: 'homepage_promo', limit: 8, response_fields: ['id','link','url_without_domain','link_target_blank','template','image_upload_path','image_thumbs']}" v-slot="{items}">
		<div v-if="items?.length" class="posr promo wrapper">
			<CmsSectionHeader>
				<BaseCmsLabel code="best_offers" />
				<template #btn>
					<BasePublishCategory :fetch="{code: ['promotions'], response_fields: ['url_without_domain']}" v-slot="{item}">
						<NuxtLink :to="item?.url_without_domain" class="widget-btn-show-all">
							<span><BaseCmsLabel code="show_all" /></span>
						</NuxtLink>
					</BasePublishCategory>
				</template>
			</CmsSectionHeader>
			<div class="promo-section">
				<NuxtLink
					v-for="item in items" :key="item.id"
					:to="item.url_without_domain"
					:target="item.link_target_blank == 1 ? '_blank' : null"
					class="promo-item"
					:class="{'promo-item-big': item.template == 'template_hp_promo_big'}">
					<template v-if="item.image_upload_path">
						<BaseUiImage loading="lazy" :data="item.template == 'template_hp_promo_big' ? item.image_thumbs?.['width952-height464-crop1'] : item.image_thumbs?.['width464-height464-crop1']" default="/images/no-image.jpg" />
					</template>
				</NuxtLink>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<style scoped lang="less">
	.promo{margin-bottom: var(--widgetSpacing);}
	.promo-section{
		display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--elementGap);
		@media (max-width: @m){grid-template-columns: 1fr 1fr;}
	}
	.promo-item{
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: var(--borderRadius);}
	}
	.promo-item-big{grid-column: 1 / span 2;}
</style>