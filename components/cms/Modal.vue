<template>
	<BaseUiModal v-slot="{onClose, active, item}" name="quick" :key-closable="true">
		<div v-if="active" class="cms-modal" :class="[$attrs?.name]">
			<div style="position: relative; pointer-events: all;">
				<div class="cms-modal-close" @click="onClose()" />
				<div class="cms-modal-cnt">
					<slot :item="item" :onClose="onClose">
						<h1 v-if="item?.seo_h1">{{ item.seo_h1 }}</h1>
						<div v-if="item?.content" v-html="item?.content" v-interpolation />
					</slot>
				</div>
			</div>
			<div class="cms-modal-mask" @click="onClose()" />
		</div>
	</BaseUiModal>
</template>

<style>
	body.modal-active {
		touch-action: none;
		-ms-scroll-chaining: none;
		overscroll-behavior: none;
		-webkit-overflow-scrolling: auto;
		overflow: hidden;
	}
</style>
