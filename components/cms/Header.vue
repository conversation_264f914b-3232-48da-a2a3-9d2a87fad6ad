<template>
	<Body :class="{'nav-categories-active': categoriesNavActive || secondNavActive, 'm-nav-active': mobileNavActive}" />
	<header class="header">
		<div class="header-top">
			<div class="wrapper header-wrapper top">
				<div class="m-hamburger" :class="{'active': mobileNavActive}" @click="toggleMobileNav()"><span class="icon"></span></div>
				<BaseCmsLogo class="logo" id="logo" />
				<SearchForm />
				<div class="header-buttons">
					<CmsBtnWishlist />
					<AuthUserBox />
					<WebshopWidgetShoppingCart />
				</div>
			</div>
		</div>
		<div class="header-bottom">
			<div class="wrapper header-wrapper header-bottom-wrapper">
				<div class="header-nav main">
					<div class="nav-item-categories" :class="{'active': categoriesNavActive}" ref="categoriesItem" v-if="!mobileBreakpoint">
						<div class="header-nav-item header-nav-item-categories" @click="toggleCategories">
							<span class="icon"></span>
							<span class="title"><BaseCmsLabel code='header_categories_btn' /></span>
						</div>
						<CmsNavCategories :navOpen="categoriesNavActive" />
					</div>
					
					<BaseCmsNav code="header_featured" v-slot="{items}" data-key="header-featured-nav">
						<template v-if="items?.length">
							<NuxtLink v-for="item in items" :key="item.id" class="header-nav-item" :target="item.target_blank == 1 ? '_blank' : null" :to="item.url_without_domain">
								<span v-if="item.image" class="image"><BaseUiImage :src="item.image_upload_path" width="18" height="18" /></span>
								<span class="title">{{ item.title }}</span>
							</NuxtLink>
						</template>
					</BaseCmsNav>
				</div>
				<div class="header-nav second">
					<BaseCmsNav code="header_second" v-slot="{items}" data-key="header-second-nav">
						<template v-if="items?.length">
							<template v-for="(item, index) in items" :key="item.id">
								<NuxtLink v-if="index <= 4" 
										class="header-nav-item" 
										:target="item.target_blank == 1 ? '_blank' : null" 
										:to="item.url_without_domain">
									<span v-if="item.image" class="image">
										<BaseUiImage :src="item.image_upload_path" width="18" height="18" />
									</span>
									<span class="title">{{ item.title }}</span>
								</NuxtLink>
							</template>
						</template>
						<div ref="secondNav" v-if="items?.length > 4" class="header-nav-item special" :class="{'active': secondNavActive}" @click="toggleSecondNav">
							<span class="dots"></span>
							<div class="header-second-nav-dropdown">
								<template v-for="(item, index) in items" :key="item.id">
									<NuxtLink v-if="index >= 4" :target="item.target_blank == 1 ? '_blank' : null"  :to="item.url_without_domain">{{ item.title }}</NuxtLink>
								</template>
							</div>
						
							<div class="c-nav-extra">
								<BaseCmsLabel code='header_categories_btn' tag="div" />
								<BaseCmsNav code="header_extra_m" v-slot="{items}" data-key="header-extra-nav">
									<NuxtLink v-for="item in items" :key="item.id" :target="item.target_blank == 1 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
								</BaseCmsNav>
							</div>
						</div>
					</BaseCmsNav>
				</div>
			</div>
		</div>
	</header>
	<Teleport to="body">
		<CmsNavCategories v-if="mobileBreakpoint && mobileNavActive" :navOpen="categoriesNavActive" />
	</Teleport>
	<div class="header-placeholder"></div>
</template>

<script setup>
	const route = useRoute();
	const router = useRouter();
	const {onClickOutside} = useDom();
	const {mobileBreakpoint} = inject('rwd');
	const {overlay} = inject('layout');

	//toggle categories
	const mobileNavActive = ref(false);
	const categoriesItem = ref(null);
	const categoriesNavActive = ref(false);
	provide('header', {mobileNavActive});

	let savedScrollPosition = 0;
	function toggleMobileNav() {	
		if (!mobileNavActive.value) {
			savedScrollPosition = window.scrollY;
			mobileNavActive.value = true;
		} else {
			mobileNavActive.value = false;
			setTimeout(() => {
				window.scrollTo(0, savedScrollPosition);
			}, 100);
		}
	}

	function toggleCategories() {
		categoriesNavActive.value = !categoriesNavActive.value;
		overlay.value = categoriesNavActive.value;
	}
	onClickOutside(categoriesItem, event => {
		categoriesNavActive.value = false;
	});

	//reset values on route change
	watch(
		() => router.currentRoute.value.path,
		(newPath, oldPath) => {
			categoriesNavActive.value = false;
		}
	);

	//toggle second nav dropdown
	const secondNav = ref(null);
	const secondNavActive = ref(false);
	function toggleSecondNav() {
		secondNavActive.value = !secondNavActive.value;
		overlay.value = secondNavActive.value;
	}
	onClickOutside(secondNav, event => {
		secondNavActive.value = false;
	});	
</script>


<style lang="less" scoped>
	.header-placeholder{height: 0;}
	.header{position: relative; z-index: 500;}
	.header-wrapper{
		display: flex; align-items: center; justify-content: space-between; flex-grow: 1; position: relative;
		&.top{justify-content: space-between;}

		@media (max-width: @m){
			&.top{flex-wrap: wrap; justify-content: flex-start;}
		}
	}

	.header-top{
		display: flex; align-items: center; width: 100%; height: 88px; background: var(--blue);

		@media (max-width: @t){height: 56px;}
		@media (max-width: @m){height: auto; padding: 12px 0;}
	}
	.m-hamburger{
		display: none;

		@media (max-width: @m){
			display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 24px; height: 24px; margin-right: 14px; order: 1;
			&.active{
				.icon{
					background: transparent;
					&:before{top: 0; .rotate(-45deg);}
					&:after{top: 0; .rotate(45deg);}
				}
			}
			.icon{
				width: 24px; height: 3px; background: #fff; border-radius: 2px; position: relative; .transition(all);
				&:after, &:before{.pseudo(24px,3px); background: #fff; border-radius: 2px; position: absolute; .transition(all);}
				&:before{top: -9px;}
				&:after{top: 9px;}
			}
		}
	}
	.logo{
		display: block; flex-shrink: 0; width: 163px; height: 34px; background: url(assets/images/logo.svg) no-repeat center center; background-size: contain; z-index: 1;
		@media (max-width: @t){width: 103px; height: 20px;}
		@media (max-width: @m){order: 2;}
	}
	.header-buttons{
		display: flex; gap: 10px;
		@media (max-width: @m){justify-content: flex-end; flex-grow: 1; gap: 5px; order: 3;}
	}

	.header-bottom{width: 100%; height: 44px; background: var(--blueDark);}
	.header-nav{
		display: flex;
		@media (max-width: @m){
			margin: 0; position: relative; white-space: normal;
		}
	}
	.header-nav-item{
		display: flex; align-items: center; height: 44px; margin-right: 24px; color: var(--white); font-size: 14px; font-weight: 500; text-decoration: none; cursor: pointer; .transition(opacity);
		@media (min-width: @m){
			&:hover{
				.title{color: #b3c0d5;}
				.dots{opacity: 0.7;}
			}
		}
		&.special{
			display: flex; justify-content: center; flex-shrink: 0; width: 25px; margin-right: 0; position: relative;
			@media (max-width: @m){display: none;}
			&.active{
				&:before{.pseudo(10px,10px); background: var(--white); position: absolute; bottom: -5px; .rotate(45deg);}
				.header-second-nav-dropdown{display: flex;}
			}
		}
		.dots{
			width: 5px; height: 5px; background: #fff; border-radius: 100%; position: relative; .transition(opacity);
			&:after, &:before{.pseudo(5px, 5px); background: #fff; border-radius: 100%; position: absolute;}
			&:before{left: -10px;}
			&:after{right: -10px;}
		}
		.image{display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 18px; height: 18px; margin-right: 6px;}
		.title{.transition(color);}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}

		@media (max-width: @t){
			margin-right: 18px;
			.image{width: 14px; height: 14px;}
		}
		@media (max-width: @m){
			white-space: nowrap;
			&:last-child{margin-right: 0;}
			&.categories{display: none;}
		}
	}

	.nav-item-categories{
		.icon{
			width: 18px; height: 2px; margin-right: 6px; background: #fff; border-radius: 2px; position: relative; .transition(background);
			&:after, &:before{.pseudo(18px,2px); background: #fff; border-radius: 2px; position: absolute; .transition(all);}
			&:before{top: -6px;}
			&:after{top: 6px;}
		}
		&.active{
			:deep(.c-cols){display: flex;}
			.icon{
				background: transparent;
				&:before{transform: rotate(-45deg); top: 0;}
				&:after{transform: rotate(45deg); top: 0;}
			}
			.title{
				display: flex; align-items: center; position: relative; height: 100%;
				&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); bottom: -5px; left: 50%; margin-left: -5px;}
			}
		}
	}
	:deep(.c-cols){
		position: absolute; top: 44px; left: -3px; z-index: 1;
		&.active{display: flex;}
	}
	

	.header-second-nav-dropdown{
		display: none; flex-direction: column; width: 155px; max-height: 350px; left: 0; .translate(calc(~"-50% + 11px")); gap: 12px; padding: 12px 24px 18px; background: var(--white); border-radius: 0 0 12px 12px; overflow: hidden; overflow-y: auto; position: absolute; top: 100%; z-index: 11; box-shadow: 0px 8px 18px 0px #0000001F;
		&>a{
			display: flex; align-items: center; font-size: 14px; font-weight: 500; text-decoration: none; color: var(--black); .transition(color);
			@media (min-width: @t){
				&:hover{color: var(--blueDark);}
			}
		}
	}
	.c-nav-extra{
		display: none;

		@media (max-width: @m){
			display: flex; width: auto; height: 54px; margin: 0; padding: 16px; border-bottom: 1px solid var(--gray2); overflow-y: hidden; white-space: normal; position: absolute; top: 0; left: 0; right: 0;
			&::-webkit-scrollbar{width: 3px; height: 3px;}
			&::-webkit-scrollbar-track{background: var(--gray2); border-radius: 4px;}
			&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 4px; border: 2px solid transparent;}
			&>div, &>a{
				margin-right: 32px; font-size: 16px; font-weight: 600; color: var(--black); text-decoration: none; white-space: nowrap;
				&:last-child{margin-right: 0;}
			}
		}
	}

	@media (max-width: @m){
		.m-nav-active{
			.header-nav-item{
				color: var(--textColor); font-weight: bold; font-size: 16px;
				.image{display: none;}
			}
			.header-bottom{
				background: #fff; position: relative; height: 50px;
				&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: -4px; left: 20px;}
			}
			.header-bottom-wrapper{
				&::-webkit-scrollbar{display: block; background: var(--gray2); height: 4px;}
				&::-webkit-scrollbar-thumb{display: block; background: var(--blue); border-radius: 100px;}
			}
		}
		.header-bottom-wrapper{
			gap: 18px; overflow: auto; margin: 0; padding: 0 15px;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
			
		}
		.header-nav a:last-of-type{margin-right: 0;}
		.header-nav-item-categories{display: none;}
	}

	.fixed-header{
		.header-placeholder{
			height: 132px;
			@media (max-width: @t){height: 148px;}
		}
		.header{position: fixed; left: 0; right: 0; top: -200px; .translate3d(0,200px); .transition(transform); z-index: 1111;}
		.nav-item-categories.active .title:after{top: 50px;}

		.header-top{
			height: 65px;
			@media (max-width: @t){height: 48px;}
		}
		.logo{
			width: 129px; height: 26px;
			@media (max-width: @t){width: 103px; height: 20px;}
		}

		.header-bottom{height: 0; max-height: 0;}
		.header-nav{
			position: absolute; top: -55px;
			&.main{left: 173px;
				@media (max-width: @t){display: none;}
			}
			&.second{display: none;}
		}
	}
</style>

<style lang="less">
	.fixed-header .c-cols{top: 55px;}
</style>