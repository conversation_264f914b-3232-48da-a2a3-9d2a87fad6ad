<template>
	<div class="content cms-content" v-interpolation>
		<slot />
	</div>
</template>

<style scoped lang="less">
	.content{
		background: #fff; border-radius: var(--borderRadius); padding: 25px; margin: 25px 0; text-wrap: pretty;
		@media (max-width: @m){margin: var(--wrapperMargin) calc(var(--wrapperMargin) * -1) 0; border-radius: 0; padding: 20px var(--wrapperMargin);}
		:deep(iframe){width: 100%; border-radius: var(--borderRadius);}
		:deep(h3:first-child, h2:first-child){padding-top: 0;}
		&.no-spacing{margin: 0;}
	}
</style>