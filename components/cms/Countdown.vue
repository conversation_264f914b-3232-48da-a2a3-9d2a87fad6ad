<template>
	<div class="counter">
		<div class="title"><BaseCmsLabel code="promotion_expire" /></div>
		<BaseUiCountdown :end="end" v-slot="{days, hours, minutes, seconds}">
			<div class="countdown-timer">
				<span v-if="days != 0">{{ days }} <span>{{ currentLang == 'hr' ? 'dana' : 'dni' }}</span></span>
				<span>{{ hours }} <span>{{ currentLang == 'hr' ? 'sati' : 'ure' }}</span></span>
				<span>{{ minutes }} <span>minute</span></span>
				<span>{{ seconds }} <span>sekunde</span></span>
			</div>
		</BaseUiCountdown>
	</div>
</template>

<script setup>
	const {get: currentLang} = useLang();
	const props = defineProps(['end']);
</script>

<style scoped lang="less">
	.counter{background: #fff; padding: clamp(20px, 4vw, 35px) 0; border-radius: var(--borderRadius); text-align: center;}
	.title{padding: 0 0 12px;}
	.countdown-timer{
		text-align: center; display: flex; justify-content: center; font-size: 31px; font-weight: bold; color: var(--blueDark); line-height: 1.2; text-transform: uppercase; gap: 20px;
		span span{display: block; font-size: 9px; font-weight: normal;}
	}
</style>