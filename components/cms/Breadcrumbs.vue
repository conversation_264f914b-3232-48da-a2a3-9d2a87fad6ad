<template>
	<div class="bc">
		<template v-for="(item, index) in visibleItems" :key="item" >
			<NuxtLink v-if="index != visibleItems.length - (props.links ? 0 : 1)" :to="item.url_without_domain" class="bc-item">
				<span>{{ item.title }}</span>
			</NuxtLink>
			<span v-else class="bc-last">{{ item.title }}</span>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps({
		items: {
			type: Array,
			required: true
		},
		maxDepth: {
			type: Number,
			default: Infinity
		},
		removeLast: {
			type: <PERSON><PERSON>an,
			default: false
		},
		links: {
			type: Boolean,
			default: false
		}
	});

	// Calculate visible items based on maxDepth
	const visibleItems = computed(() => {		
		if (!props.items || props.items.length === 0) return [];
		let items = props.items;
		if (props.removeLast) {
			items = items.slice(0, -1);
		}
		return items.slice(0, props.maxDepth);
	});
</script>

<style lang="less" scoped>
	.bc{
		padding: 14px 0; font-size: 12px; font-weight: 500; color: var(--gray5); position: relative;
		&>a{
			display: inline-flex; text-decoration: none; color: var(--gray5); position: relative; align-items: center; justify-content: center;
			&:after{.icon-arrow-right2(); font: 8px/1 var(--fonti); width: 5px; height: 8px; margin: 0 8px; color: var(--gray5);}
			&:hover{color: var(--blue);}
			&:last-child:after{display: none;}
		}
		@media (max-width: @m){
			text-align: center; font-size: 16px; font-weight: 600; color: var(--textColor); padding: 15px;
			&>a{
				display: none;
				&:after{font-size: 16px; color: var(--textColor); margin: 0; height: 15px; width: 10px; .scaleX(-1);}
				&:last-of-type{display: flex; width: 50px; justify-content: flex-start; font-size: 0; position: absolute; left: 0; top: 0; bottom: 0;}
			}
		}
	}
</style>
