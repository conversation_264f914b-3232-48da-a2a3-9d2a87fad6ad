<template>
	<BaseCatalogWishlistWidget v-slot="{counter, wishlistUrl}">
		<NuxtLink :to="wishlistUrl" class="btn-wishlist">
			<ClientOnly>
				<span v-show="counter" class="counter">{{ counter }}</span>
			</ClientOnly>
			<span class="text"><BaseCmsLabel code="header_wishlist_title" /></span>
		</NuxtLink>
	</BaseCatalogWishlistWidget>
</template>

<style lang="less" scoped>
	.btn-wishlist{
		display: flex; align-items: center; justify-content: center; width: 45px; height: 45px; font-size: 0; line-height: 0; text-decoration: none; position: relative;
		&:before{.icon-wishlist(); font: 22px/1 var(--fonti); color: var(--white);}

		@media (max-width: @t){
			width: 35px; height: 35px;
			&::before{font-size: 19px;}
		}
	}
	.counter{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 18px; height: 18px; background: var(--turquoise); border-radius: 100%; font-size: 11px; line-height: 17px; font-weight: 600; text-align: center; color: var(--blueDark); position: absolute; top: 0; right: 0;

		@media (max-width: @t){width: 16px; height: 16px; font-size: 10px; line-height: 16px;}
	}
</style>