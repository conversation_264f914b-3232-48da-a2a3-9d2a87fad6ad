<template>
	<BaseCmsRotator :fetch="{code: 'benefits', limit: 4, response_fields: ['id','link','url_without_domain','link_target_blank','image_upload_path','title']}" v-slot="{items}">
		<div v-if="items?.length" class="benefits" :class="props.class">
			<BaseUiSwiper
				name="benefits"
				:options="{
					slidesPerView: 1,
					rewind: true,
					autoplay: {
						delay: 2000,
						disableOnInteraction: true
					},
					enabled: true,
					breakpoints: {
						980: {
							slidesPerView: 4,
							enabled: false
						}
					}
				}">
				<BaseUiSwiperSlide v-for="item in items" :key="item.id" class="benefit">
					<NuxtLink v-if="item.url_without_domain" :to="item.url_without_domain" :target="item.link_target_blank == 1 ? '_blank' : null">
						<div v-if="item.image_upload_path" class="benefit-img"><BaseUiImage :src="item.image_upload_path" width="23" height="23" loading="lazy" /></div>
						<div v-if="item.title" class="benefit-title">{{ item.title }}</div>
					</NuxtLink>
					<div v-else>
						<div v-if="item.image_upload_path" class="benefit-img"><BaseUiImage :src="item.image_upload_path" width="23" height="23" loading="lazy" /></div>
						<div v-if="item.title" class="benefit-title">{{ item.title }}</div>
					</div>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const props = defineProps({
		class: String,
	});
</script>

<style scoped lang="less">
	.benefits{
		display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 50px; padding: 18px; background: var(--white); border-radius: var(--borderRadius); position: relative;
		&.no-spacing{margin: 0;}
		
		@media (max-width: @m){margin-bottom: 24px; padding: 12px; border-radius: 8px; display: block;}
	}
	.benefit{
		display: inline-flex; margin: 0 clamp(20px, 1.5vw, 36px); font-size: clamp(14px, 1.5vw, 16px); font-weight: 500; color: var(--black);
		:deep(a){display: flex; align-items: center; text-decoration: none; color: var(--black); position: relative;}
		@media (min-width: @m){
			width: auto!important;
		}

		@media (max-width: @m){justify-content: center; margin: 0; font-size: 14px;}
	}
	.benefit-img{
		display: flex; align-items: center; justify-content: center; width: 23px; height: 23px; margin-right: 6px;
		:deep(img){display: block; width: auto; height: auto; max-width: 23px; max-height: 23px;}

		@media (max-width: @m){
			width: 20px; height: 20px;
			:deep(img){max-width: 20px; max-height: 20px;}
		}
	}
</style>