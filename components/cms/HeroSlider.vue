<template>
	<BaseCmsRotator
		:fetch="{code: 'homepage_rotator', limit: 5, response_fields: ['id','title','code','template','link_target_blank','url_without_domain','image_upload_path','image_thumbs','image_4_upload_path','image_4_thumbs']}"
		v-slot="{items}">
		<div v-if="items?.length" class="hero">
			<BaseUiSwiper
				class="hero-slider"
				name="hero"
				:options="{
					slidesPerView: 1,
					effect: 'fade',
					pagination: {
						enabled: true	
					},
					autoplay: {
						delay: 5000,
						disableOnInteraction: true
					},
					loop: true,
				}"
				>
				<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id">
					<NuxtLink :target="item.link_target_blank == 1 ? '_blank' : null" :to="item.url_without_domain" class="hero-slide">
						<div class="hero-img">
							<BaseUiImage
								:loading="index == 0 ? 'eager' : 'lazy'"
								:data="item.image_thumbs?.['width1920-height500-crop1']"
								default="/images/no-image.jpg"
								:picture="[{maxWidth: '760px', src: item.image_4_thumbs?.['width760-height397-crop1']?.thumb ? item.image_4_thumbs['width760-height397-crop1'].thumb : item.image_thumbs?.['width1920-height500-crop1']?.thumb, default: '/images/no-image-hero.jpg'}]"
									/>
						</div>
					</NuxtLink>
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</BaseCmsRotator>
</template>

<style scoped lang="less">
	.hero{
		width: auto; margin: 0 auto 24px; text-align: center; position: relative; background: #fff;

		@media (max-width: @m){margin-bottom: 12px;}
	}
	.hero-slider{max-width: 1920px;}
	.hero-slide{display: flex; align-items: center; justify-content: center;}
	.hero-img{
		display: flex; align-items: center; justify-content: center;
		:deep(img){display: block; max-width: 100%; width: 100%; height: auto;}
	}

	:deep(.swiper-pagination){
		position: absolute; bottom: 15px; left: 0; right: 0; justify-content: center; z-index: 10; gap: 10px;
		&:not(.swiper-pagination-lock){display: flex;}
		.swiper-pagination-bullet{width: 8px; height: 8px; background: #D9D9D999; display: block; border-radius: 100px; .transition(width);}
		.swiper-pagination-bullet-active{width: 35px;}
	}
	:deep(.hero-swiper-dots){
		display: inline-flex; align-items: center; justify-content: center; position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 111;

		@media (max-width: @m){bottom: 10px;}
	}
	:deep(.hero-swiper-dot){
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 8px; height: 8px; margin: 0 6px; border-radius: 100%; background: rgba(217, 217, 217, 0.60); z-index: 1; transition: width 0.3s, border-radius 0.3s;
		&.is-active{width: 36px; border-radius: 6px;}

		@media (max-width: @m){
			width: 7px; height: 7px; margin: 0 5px;
			&.is-active{width: 29px; border-radius: 5px;}
		}
	}
</style>