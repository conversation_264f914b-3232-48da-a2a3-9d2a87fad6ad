<template>
	<div class="card">
		<div class="image">
			<slot name="header" />
		</div>
		<div class="content">
			<slot name="content" />
		</div>
	</div>
</template>

<style scoped lang="less">
.card{
	background: #fff; border-radius: var(--borderRadius); overflow: hidden; text-wrap: balance;
	@media (max-width: @m){
		border-radius: 0; margin: 0 calc(var(--wrapperMargin) * -1);
	}
}
.content{
	padding: 30px 20px 40px; background: #fff; text-align: center; font-size: clamp(14px, 1.5vw, 16px); line-height: 1.5;
	:deep(p:last-of-type){padding-bottom: 0;}
	@media (max-width: @m){
		padding: 20px var(--wrapperMargin); text-align: left; border-radius: 0;
	}
}
:deep(h1){
	padding: 0 0 20px; font-weight: bold;
	@media (max-width: @m){
		font-size: 30px; padding: 0 0 15px;
	}
}
.no-content :deep(h1){padding: 0;}
.narrow-content .content{
	max-width: 50%; margin: auto;
	@media (max-width: @m){
		max-width: 100%;
	}
}
.only-image{
	.content{display: none;}
}
.image:deep(img){display: block; margin: auto;}
</style>