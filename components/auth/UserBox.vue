<template>
	<BaseAuthUser v-slot="{isLoggedIn, urls}">
		<div class="aw" ref="aw" :class="{'active': awDropdownActive}">
			<BaseUiLink class="aw-link" :native="true" :prevent-default="!mobileBreakpoint && !isLoggedIn" :href="isLoggedIn ? urls.auth : urls.auth_login" @click="awToggle" />
			<ClientOnly>
				<template v-if="!mobileBreakpoint && !isLoggedIn">
					<div class="aw-dropdown" v-interpolation>
						<div class="aw-dropdown-row1">
							<div class="aw-dropdown-title"><BaseCmsLabel code='login_widget_title' /></div>
							<NuxtLink class="btn aw-dropdown-button" :to="urls.auth_login" @click="awToggle()"><BaseCmsLabel code="login" /></NuxtLink>
							<BaseCmsLabel code='login_widget_signup' tag="div" class="aw-dropdown-signup" :replace="[{'%LINK%': urls.auth_signup}]" @click="awToggle()" />
						</div>
						<!-- FIXME vidjeti gdje trebaju voditi ovi linkovi -->
						<div class="aw-dropdown-row2">
							<a class="aw-dropdown-link" href="#" @click="awToggle()">Prati svoju narudžbu</a>
							<a class="aw-dropdown-link viewed" href="#" @click="awToggle()">Nedavno pogledano</a>
						</div>
					</div>
				</template>
			</ClientOnly>
		</div>
	</BaseAuthUser>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const {overlay} = inject('layout');
	const {mobileBreakpoint} = inject('rwd');
	const aw = ref(null);

	//toggle dropdown
	const awDropdownActive = ref(false);
	onClickOutside(aw, event => {
		awDropdownActive.value = false;
	});

	function awToggle() {
		awDropdownActive.value = !awDropdownActive.value;
		overlay.value = awDropdownActive.value;
	}
</script>


<style lang="less" scoped>
	.aw{
		position: relative;
		&.active .aw-dropdown{display: block;}
	}
	.aw-link{
		display: flex; align-items: center; justify-content: center; width: 45px; height: 45px; font-size: 0; line-height: 0; text-decoration: none; position: relative; cursor: pointer;
		&:before{.icon-user(); font: 22px/1 var(--fonti); color: var(--white);}

		@media (max-width: @t){
			width: 35px; height: 35px;
			&::before{font-size: 19px;}
		}
	}
	.aw-dropdown{
		display: none; width: 285px; background: var(--white); border-radius: 0 0 12px 12px; position: absolute; top: 59px; left: 50%; .translate(-50%); z-index: 11; text-align: center; box-shadow: 0px 8px 18px 0px #0000001F;
		&:before{.pseudo(10px,10px); background: var(--white); position: absolute; left: 50%; margin-left: -5px; top: -5px; .rotate(45deg);}
	}
	.aw-dropdown-row1{width: 100%; flex-grow: 1; padding: 32px 16px 24px;}
	.aw-dropdown-title{display: block; margin-bottom: 12px; font-size: 20px; line-height: 1.2; font-weight: 700; color: var(--black);}
	.aw-dropdown-button{width: 100%; height: 39px;}
	.aw-dropdown-signup{
		margin-top: 12px; font-size: 12px; line-height: 1.4;
		:deep(a){color: var(--black); font-weight: 600; text-decoration: none;}
	}
	.aw-dropdown-row2{border-top: 1px solid #F1F1F4; padding: 20px 25px; display: flex; text-align: left; gap: 15px;}
	.aw-dropdown-link{
		display: flex; text-decoration: none; color: var(--textColor); font-size: 13px; line-height: 1.3; align-items: center;
		&:before{.icon-box(); font: 19px/1 var(--fonti); margin-right: 8px;}
		&.viewed:before{.icon-eye();}
	}

	//fixed header
	.fixed-header{
		.aw-dropdown{top: 47px;}
	}
</style>