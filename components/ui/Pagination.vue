<template>
	<BaseUiPagination :visible="true" />
</template>

<style scoped lang="less">
	.pagination{
		display: flex; justify-content: center; align-items: center; margin-top: 80px;
		:deep(a), :deep(span){display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 32px; height: 32px; margin: 0 4px; background: var(--white); border-radius: 100%; font-size: 14px; font-weight: 500; color: var(--black); text-align: center; text-decoration: none; position: relative; transition: background 0.3s, color 0.3s, z-index 0.3s;}
		:deep(.current-page){background: var(--blueDark); color: var(--white); z-index: 1; pointer-events: none;}
		:deep(.pagination-mid-page){align-items: flex-end; margin: 0 1px; padding-bottom: 8px; border: none;}
		:deep(.pagination-next-page), :deep(.pagination-prev-page){
			font-size: 0; line-height: 0;
			&:before{.icon-arrow-down(); font: 9px/1 var(--fonti); color: var(--black); margin-left: 5px; .rotate(-90deg); .transition(color);}
		}
		:deep(.pagination-prev-page:before){margin-right: 5px; .rotate(90deg);}

		@media (max-width: @m){margin-top: 36px;}
	}
</style>