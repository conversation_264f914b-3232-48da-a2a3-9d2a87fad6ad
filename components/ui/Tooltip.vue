<template>
	<div class="tooltip">
		<slot />
	</div>
</template>

<style scoped lang="less">
	.tooltip{
		position: absolute; top: 110%; left: 0; z-index: 50; font-size: 11px; color: var(--textColor); background: var(--gray3); padding: 5px 10px; border-radius: 5px; white-space: nowrap; font-weight: normal; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);
		&:before{
			.pseudo(8px, 8px); background: var(--gray3); transform: rotate(45deg); top: -4px; left: 12px;
		}
		&.right{left: auto; right: 0;
			&:before{left: auto; right: 12px;}
		}
		&.wrap-text{white-space: normal;}
	}
</style>