<template>
	<template v-if="mode=='dots' || props.class?.includes('dots')">
		<div class="dots-loader" :class="props.class">
			<div></div><div></div><div></div>
		</div>
	</template>
	<template v-else>
		<div class="loader inline" :class="props.class" v-if="props.class?.includes('inline')"/>	
		<div v-else class="loader-body" :class="props.class">
			<div class="loader-cnt">
				<div class="loader"></div>
				<div class="loader-text">
					<template v-if="currentLang == 'si'">Učitavanje...</template>
					<template v-else>Nalaganje...</template>
				</div>
			</div>
		</div>
	</template>
</template>

<script setup>
	const lang = useLang();
	const currentLang = ref(lang.get());
	const props = defineProps({
		mode: String,
		class: String,
	})
</script>

<style scoped lang="less">
	.dots-loader{
		gap: .25rem; transition:  transform 0.2s; display: inline-flex;
		&>div{width: .5rem; height: .5rem; background-color: white; border-radius: 50%; animation: 1.2s infinite ease-in-out load;}
		div:nth-child(1){animation-delay: -0.32s;}
		div:nth-child(2){animation-delay: -0.16s;}
		&.dark{
			&>div{background-color: var(--blueDark);}
		}
		&.small{
			gap: 3px;
			&>div{width: 5px; height: 5px;}
		}
	}

	.loader{position: relative; display: inline-block; width: 65px; height: 65px; border: 7px solid var(--blueDark); border-radius: 50%; border-top-color: var(--turquoise); animation: spin 1s linear infinite;}
	.loader-cnt{display: flex; flex-direction: column; align-items: center; text-transform: uppercase; font-size: 15px; font-weight: bold; color: var(--blueDark); gap: 7px;}
	.full{position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 10000; background: #fff; display: flex; align-items: center; justify-content: center;}
	.small{
		.loader-cnt{gap: 7px; font-size: 11px;}
	}
	.white{border-color: #fff; border-top-color: var(--turquoise);}
	.small .loader, .loader.inline{width: 20px; height: 20px; border-width: 3px;}
	.spacing{padding: 80px 0;}
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
</style>