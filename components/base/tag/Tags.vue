<template>
	<slot :items="items" :loading="loading" />
</template>

<script setup>
	const {fetchTags} = useTag();
	const model = defineModel();
	const route = useRoute();
	const props = defineProps({
		fetch: Object,
	});

	const loading = ref(true);
	const items = shallowRef([]);

	const fetchOptions = {
		module: route.meta?.controller == 'catalog' ? 'catalog' : 'publish',
		sort: 'total',
		limit: 100,
		...props.fetch,
	};

	const tagsData = await fetchTags(fetchOptions);
	items.value = tagsData?.data ? tagsData.data : [];

	model.value = items.value;
	loading.value = false;
</script>
