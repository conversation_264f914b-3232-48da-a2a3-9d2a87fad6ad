<template>
	<slot :fields="fields" :totalFields="totalFields" :onFilter="onFilter" :onClear="onClear" :selectedFilters="selectedFilters" :active="active" :onToggle="onToggle" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {emit, subscribe} = useEventBus();
	const {debounce} = useUtils();
	const {compareArrays} = useArrayUtils();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		active: {
			type: Boolean,
			default: props => props.item?.options_show,
		},
		autoSubmit: {
			type: Boolean,
			default: true,
		},
	});

	// get data provided by parent Filter component
	const {globalSelectedFilters} = inject('basePublishFiltersData');

	const selectedFilters = ref([]);
	const active = ref(props.active);
	const totalFields = computed(() => props.item?.options?.length);
	const fields = computed(() => {
		return props.item.options?.length ? props.item.options : [];
	});

	async function onFilter(event) {
		// check if event name and value are defined
		if (!event.target.name || !event.target.value || event.target.value == 'on') {
			return useLog('Filter name or value is not defined', 'error');
		}

		// add or remove selected filter if checkbox is checked or unchecked
		if (event.target.checked && !selectedFilters.value.includes(event.target.value)) {
			selectedFilters.value.push(event.target.value);
		}
		if (!event.target.checked) {
			selectedFilters.value = selectedFilters.value.filter(item => item.toString() !== event.target.value);
		}

		await debounceSubmit();
	}

	// Toggle filter visibility. If all is true, emit event to toggle all filters.
	function onToggle(event, options = {}) {
		if (options?.all) emit('publishFilterItemToggle', props.item);
		active.value = !active.value;
	}

	// Close other active filters when emit event is triggered
	subscribe(
		'publishFilterItemToggle',
		data => {
			if (!data || props.item.id != data?.id) active.value = false;
		},
		{deep: false}
	);

	// When clear button is clicked, remove all selected filters and submit
	async function onClear() {
		selectedFilters.value = [];
		await submit('clear');
	}

	// update selected filters on component mount and when filter is removed from currently active filters
	watchEffect(() => {
		selectedFilters.value = props.item.options_selected ? [...props.item.options_selected] : [];
	});

	// debounce submit (prevent rage clicks)
	const debounceSubmit = debounce(() => submit(), 1000);

	// if auto submit is enabled, update route query and emit event to update posts
	async function submit(mode) {
		let currentRouteFilters = route.query?.[props.item.filter_url] || [];
		if (!Array.isArray(currentRouteFilters)) currentRouteFilters = [currentRouteFilters];
		if (compareArrays(currentRouteFilters, selectedFilters.value)) return; // do not submit if filters are the same
		if (props.autoSubmit || mode) {
			await navigateTo({query: {...route.query, [props.item.filter_url]: selectedFilters.value, to_page: undefined, page: undefined}});
			emit('publishPostsUpdate', route.query);
		}

		updateGlobalFilters();
	}

	// update global filters to enable manual submit
	function updateGlobalFilters() {
		const globalFilters = globalSelectedFilters.value;

		// check if global filters object contain current props.item.filter_url. if it does, remove it
		if (globalFilters[props.item.filter_url]) {
			delete globalFilters[props.item.filter_url];
		}

		// if selectedFilters value is not empty add it to global filters
		if (selectedFilters.value.length > 0) {
			globalFilters[props.item.filter_url] = selectedFilters.value;
		}
	}

	// update global selected filters on component mount
	updateGlobalFilters();

	// expose values to parent component
	defineExpose({active});
</script>
