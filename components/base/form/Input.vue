<template>
	<input
		v-if="!['select', 'customselect', 'location', 'textarea', 'checkbox', 'radio', 'rate', 'file', 'datepicker'].includes(fieldType)"
		:class="[{'field_error_input': errorMessage}, props.class]"
		v-model="inputValue"
		:name="fieldName"
		:id="fieldId"
		:type="fieldType"
		:autocomplete="fieldAutoComplete"
		:placeholder="placeholder"
		@focus="onFocus"
		@blur="onBlur"
		@keyup="setTouchedField"
		@click="$emit('click', $event)"
		v-bind="$attrs" />

	<template v-if="fieldType == 'select'">
		<BaseFormSelect v-if="props.mode == 'custom'" :class="[{'field_error_input field_error_select': errorMessage}, props.class]" :name="fieldName" :id="fieldId" v-bind="$attrs" v-model="inputValue">
			<template v-if="item?.options">
				<BaseFormSelectOption v-for="option in item.options" :key="option.key" :value="option.key" :selected="option.selected" @click="$emit('change', option)">{{ option.title }}</BaseFormSelectOption>
			</template>
			<template v-else>
				<slot />
			</template>
		</BaseFormSelect>
		<select v-else :class="[{'field_error_input field_error_select': errorMessage}, props.class]" :name="fieldName" :id="fieldId" @blur="onBlur" @change="onSelectBlur($event), $emit('change', $event)" @click="onFocus($event), $emit('click', $event)" v-bind="$attrs" v-model="inputValue">
			<template v-if="item?.options">
				<option v-for="option in item.options" :key="option.key" :value="option.key" :selected="option.selected">{{ option.title }}</option>
			</template>
			<template v-else>
				<slot />
			</template>
		</select>
	</template>

	<textarea
		v-if="fieldType == 'textarea'"
		:class="[{'field_error_input': errorMessage}, props.class]"
		v-model="inputValue"
		:name="fieldName"
		:id="fieldId"
		:type="fieldType"
		:autocomplete="fieldAutoComplete"
		:placeholder="placeholder"
		@focus="onFocus"
		@blur="onBlur"
		@keyup="setTouchedField"
		@click="$emit('click', $event)"
		v-bind="$attrs" />

	<template v-if="fieldType == 'checkbox'">
		<input v-if="!item?.options || props.mode == 'custom'" :class="[{'field_error_input': errorMessage}, props.class]" v-model="inputValue" :name="fieldName" :id="fieldId" type="checkbox" @click="$emit('click', $event)" v-bind="$attrs" />
		<template v-else>
			<span v-for="option in item.options" :key="option.key" :class="'field-' + fieldName + '-' + option.key">
				<input type="checkbox" :name="item.name" :id="fieldId + '-' + option.key" v-model="chbxValues" :value="option.key" @click="$emit('click', $event)" v-bind="$attrs" />
				<label :for="fieldId + '-' + option.key">{{ option.title }}</label>
			</span>
		</template>
	</template>

	<template v-if="fieldType == 'radio'">
		<input v-if="!item?.options || props.mode == 'custom'" type="radio" :name="fieldName" :id="fieldId" :value="radioValue" v-model="inputValue" @click="$emit('click', $event)" @blur="handleBlur" @change="handleChange" v-bind="$attrs" />
		<template v-else>
			<span v-for="option in item.options" :key="option.key" :class="['field-' + fieldName + '-' + option.key, props.optionClass]">
				<slot :option="option" :updateFormValue="handleChange">
					<input type="radio" :name="item.name" :id="fieldId + '-' + option.key" :checked="option.selected" :value="option.key" @click="$emit('click', $event)" @blur="handleBlur" @change="handleChange" v-bind="$attrs" />
					<label :for="fieldId + '-' + option.key">
						<slot name="label" :option="option">
							{{ option.title }}
						</slot>
					</label>
				</slot>
			</span>
		</template>
	</template>

	<template v-if="fieldType == 'file'">
		<input type="hidden" :name="fieldName" v-model="inputValue" />
		<input :class="[{'field_error_input': errorMessage}, props.class]" type="file" @change="handleUpload($event)" :id="fieldId" @click="$emit('click', $event)" v-bind="$attrs" />
		<template v-if="inputValue">
			<span class="file-title" v-if="uploadedFile?.name">{{ uploadedFile.name.split('\\').pop() }}</span>
			<span class="file-remove" @click="inputValue = ''">x</span>
		</template>
		<span class="file-loading" v-if="uploadedFile?.loading" />
		<span class="file-status" v-if="!uploadedFile?.status?.success && uploadedFile?.status?.data?.label_name" v-html="labels.get('error_file_upload_' + uploadedFile.status.data.label_name, 'Greška kod prijenosa datoteke!')" />
	</template>

	<BaseFormRateField v-if="fieldType == 'rate'" :id="fieldId" :class="props.class" :name="fieldName" @update="inputValue = $event" v-bind="$attrs" />
	<BaseFormLocationField v-if="fieldType == 'location'" :id="fieldId" :name="fieldName" :class="props.class" :value="inputValue" :field="item" v-bind="$attrs" :placeholder="placeholder" />
</template>

<script setup>
	import {defineRule, useField, useFieldValue} from 'vee-validate';
	const endpoints = useEndpoints();
	const labels = useLabels();
	const emit = defineEmits(['click', 'change']);
	const props = defineProps({
		value: [String, Number, Boolean],
		name: String,
		type: String,
		id: String,
		class: String,
		placeholder: String,
		autocomplete: String,
		mode: String,
		checked: null,
		watchSelected: [String, Number],
		optionClass: [String, Array, Object],
	});

	// get data provided by parent component
	const {floatingLabel, isTouched, item} = inject('baseFormFieldData', {floatingLabel: false, item: null, isTouched: false});

	const fieldId = computed(() => {
		if (props.id) return props.id;
		if (item.value?.id) return item.value.id;
		if (item.value?.name) return item.value.name;
		return null;
	});

	const fieldName = computed(() => (props.name ? props.name : item.value.name));

	// parse input types
	const fieldType = computed(() => {
		const type = props.type ? props.type : item.value?.type;

		if (['enum'].includes(type)) {
			return 'select';
		}

		if (['positiveinteger'].includes(type)) {
			return 'number';
		}

		if (item.value?.name && ['location', 'b_location'].includes(item.value.name)) {
			return 'location';
		}

		if (item.value?.name == 'rate') {
			return 'rate';
		}

		return type;
	});

	// field value for radio buttons (this is needed because radio buttons have the same name but different values)
	const radioValue = computed(() => {
		if (props.value) return props.value;
		if (item.value.value) return item.value.value;
		return '';
	});

	const fieldAutoComplete = computed(() => {
		if (props.autocomplete) return props.autocomplete;
		if (fieldType.value == 'password') return 'new-password';
		if (fieldType.value == 'email') return 'username';
	});

	// set floating label on load if value is set
	onMounted(() => {
		if (item.value?.value || props.value) onFocus();
	});

	// set floating label on focus (ignore checkbox or radio fields)
	function onFocus(event) {
		if (item.value && !['checkbox', 'radio', 'rate'].includes(fieldType.value)) floatingLabel.value = true;
	}

	// remove floating label on blur if value is empty
	function onBlur(event) {
		if (item.value && !event.target.value) floatingLabel.value = false;
	}

	// onBlur for select fields
	function onSelectBlur(event) {
		if (!item.value) return;

		if (!event.target.value) floatingLabel.value = false;
		isTouched.value = true;
	}

	// set touched field on keyup
	function setTouchedField(event) {
		if (item.value && event.key?.match(/^[a-zA-Z0-9 ]$/)) {
			isTouched.value = true;
		}
	}

	// handle file upload field
	const uploadedFile = ref({
		name: '',
		status: null,
		loading: false,
	});
	async function handleUpload(event) {
		const file = event.target.files[0];
		const formData = new FormData();
		formData.append('files', file);

		uploadedFile.value.loading = true;
		const res = await useApi(endpoints.get('_post_hapi_misc_file_upload'), {
			method: 'POST',
			body: formData,
		});

		inputValue.value = res?.data?.filepaths[0];
		uploadedFile.value.status = res;
		uploadedFile.value.name = file.name;
		uploadedFile.value.loading = false;
	}

	// clear field on related field/checkbox update
	if (item.value?.related_field) {
		const relatedFieldValue = useFieldValue(item.value.related_field);
		watch(
			() => relatedFieldValue.value,
			() => {
				resetField({value: ''});
			}
		);
	}

	// set validation rules
	let validationSchema = {};
	if (item.value?.validation?.length > 0) {
		defineRule('related', (value, [related], ctx) => {
			if ((related == 'f_another_country' && ctx.form[related]) || (related == 'b_r1' && ctx.form[related]) || (related == 'b_same_as_shipping' && !ctx.form[related]) || (related == 'loyalty_request' && ctx.form[related])) {
				const val = value.toString();
				if (!val || !val.length || val.match(/^ *$/) !== null) {
					return labels.get('error_not_empty', 'error_not_empty');
				}
				return true;
			}
			return true;
		});
		defineRule('required', value => {
			if (!value || !String(value).trim().length) {
				return labels.get('error_not_empty', 'error_not_empty');
			}
			return true;
		});
		defineRule('required_checkbox', value => {
			if (Array.isArray(value) && value.length == 0) {
				return labels.get('error_not_empty', 'error_not_empty');
			}
			if (value === false) {
				return labels.get('error_not_empty', 'error_not_empty');
			}
			return true;
		});
		defineRule('email', value => {
			const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
			if (!re.test(String(value))) {
				return labels.get('error_email', 'error_email');
			}
			return true;
		});
		defineRule('number', value => {
			const stringValue = String(value);
			const regex = /^[0-9]+$/;
			if (!regex.test(stringValue)) {
				return labels.get('error_number', 'error_number');
			}
			return true;
		});
		defineRule('min', (value, [min]) => {
			if (value && String(value).length < min) {
				return labels.get('error_min_length', 'error_min_length') + ' ' + min;
			}
			return true;
		});
		defineRule('max', (value, [max]) => {
			if (value && String(value).length > max) {
				return labels.get('error_max_length', 'error_max_length') + ' ' + max;
			}
			return true;
		});
		defineRule('oib', value => {
			if (value) {
				const regex = /^[0-9]+$/;
				if (!regex.test(String(value))) {
					return labels.get('error_b_company_oib_oib', 'error_b_company_oib_oib');
				}
			}
			return true;
		});
		defineRule('matches', (value, [target], ctx) => {
			if (value === ctx.form[target]) {
				return true;
			}
			return labels.get('error_matches', 'error_matches');
		});
		defineRule('password_strong', value => {
			const str = String(value);
			let err = false;
			if (!/[A-Z]/.test(str)) err = true; // has uppercase
			if (!/[a-z]/.test(str)) err = true; // has lowercase
			if (!/[0-9]/.test(str)) err = true; // has digit
			if (str.length < 8) err = true; // has length

			if (err) return labels.get('error_password_strong', 'error_password_strong');
			return true;
		});
		defineRule('address_number_required', value => {
			if (value) {
				const regex = /\d/; // has at least two words and last word contains digit
				if (!regex.test(String(value))) {
					return labels.get('error_address_must_contain_number', 'error_address_must_contain_number');
				}
			}
			return true;
		});
		defineRule('phone_bigbangsi', value => {
			const regex = /^[0-9\+]*$/i;
			const l = labels.get('error_phone_bigbangsi');
			if (value.substring(0, 2) == '+0') {
				return l ? l : 'error_phone_bigbangsi';
			} else if (regex.test(value) && value && value.charAt(0) == 0 && value.charAt(1) != 0 && value.length == 9) {
				return true;
			} else if (regex.test(value) && value.substring(0, 4) == '+386' && value.length == 12) {
				return true;
			} else if (regex.test(value) && value.substring(0, 5) == '00386' && value.length == 13) {
				return true;
			} else if ((value.substring(0, 2) == '00' || value.charAt(0) == '+') && regex.test(value) && value.length > 8 && value.substring(0, 4) != '+386' && value.substring(0, 5) != '00386') {
				return true;
			} else if (value) {
				return l ? l : 'error_phone_bigbangsi';
			}

			return true;
		});

		// parse validation rules
		item.value.validation.forEach(el => {
			if (item.value.related_field) validationSchema.related = item.value.related_field;
			if (el.type == 'not_empty' && fieldType.value != 'checkbox') validationSchema.required = true;
			if (el.type == 'not_empty' && fieldType.value == 'checkbox') validationSchema.required_checkbox = true;
			if (el.type == 'email') validationSchema.email = true;
			if (el.type == 'oib') validationSchema.oib = true;
			if (el.type == 'number') validationSchema.number = el.value;
			if (el.type == 'min_length') validationSchema.min = el.value;
			if (el.type == 'max_length') validationSchema.max = el.value;
			if (el.type == 'password_strong') validationSchema.password_strong = true;
			if (el.type == 'matches') validationSchema.matches = el.value;
			if (el.type == 'address_number_required') validationSchema.address_number_required = true;
			if (el.type == 'phone_bigbangsi') validationSchema.phone_bigbangsi = true;
			if (['location', 'b_location'].includes(item.value.name)) validationSchema = undefined;
		});
	}

	// if checkbox has options, set initial checked values. Then watch for checkbox changes and update inputValue
	const chbxValues = ref([]);
	if (fieldType.value == 'checkbox' && item.value?.options) {
		chbxValues.value = item.value.options.filter(el => el.selected).map(el => el.key);
		watch(chbxValues, newValue => (inputValue.value = newValue));
	}

	// set initial value
	const useFieldConfig = computed(() => {
		let config = {};
		let value = fieldType.value === 'checkbox' ? false : '';
		if (item.value?.value) value = item.value.value;
		if (props?.value) value = props.value;

		if (fieldType.value === 'checkbox') {
			if (props.checked === false) value = false;
			config.type = 'checkbox';
			config.checkedValue = value;
			config.initialValue = item.value?.options ? chbxValues.value : value;
		} else if (fieldType.value === 'radio') {
			// check radio button if there is "selected" property in item or if checked prop is defined
			let isChecked = item.value?.selected || props.checked ? value : false;
			if (props.checked === false) isChecked = false;
			if (item.value?.options) {
				const selectedOption = item.value.options.find(el => el.selected);
				if (selectedOption) isChecked = selectedOption.key;
			}
			if (isChecked) config.initialValue = isChecked;
		} else {
			// set initial value for other fields
			config.initialValue = value;
		}

		return config;
	});

	// use vee-validate field
	const {value: inputValue, meta, errorMessage, handleChange, handleBlur, resetField} = useField(fieldName, validationSchema, useFieldConfig.value);

	// watch if value prop is changed from outside
	watch(
		() => props.value,
		() => (inputValue.value = props.value)
	);

	// watch watchSelected prop if defined and update inputValue
	if (typeof props.watchSelected !== 'undefined') {
		watch(
			() => props.watchSelected,
			newValue => (inputValue.value = newValue)
		);
	}

	// update floating label if input value is changed
	if (!['checkbox', 'radio'].includes(fieldType.value)) {
		watch(inputValue, newValue => (floatingLabel.value = newValue ? true : false));
	}
</script>
