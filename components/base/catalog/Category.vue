<template>
	<BaseMetaSeo :data="category" v-if="seo" />
	<slot :item="category" :contentType="contentType" :queryParams="queryParams" :isLanding="isLanding" :rootPage="rootPage" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const emit = defineEmits(['load', 'loadCategory']);
	const {getUrlSegments, getLastUrlSegment} = useUrl();
	const lastSlug = getLastUrlSegment(nuxtApp._route.path);
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const route = nuxtApp._route;
	const {isObjectEmpty, cleanObject, getLastArrayElement} = useArrayUtils();
	const {getAppUrl} = useApiRoutes();
	const page = usePage();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const config = useAppConfig();
	const props = defineProps({
		fetch: Object,
		seo: Boolean,
		thumbPreset: String,
		dataKey: {
			type: String,
			default: null,
		},
		gtmTracking: {
			type: Boolean,
			default: true,
		},
		fbTracking: {
			type: Boolean,
			default: true,
		},
		log: {
			type: Boolean,
			default: false,
		},
		listResponseFields: Array,
	});

	// get global content type
	const contentType = computed(() => {
		const globalCt = nuxtApp.$appGlobalData.contentType;
		const globalAction = nuxtApp.$appGlobalData.action;

		// preventing template "flash" when switching from search to category and product details
		if (route.query?.search_q) return 'search';
		if (globalAction == 'detail') return 'detail';

		return globalCt;
	});

	const rootPage = computed(() => {
		if (['/proizvodi/', '/products/', '/izdelki/', '/produkte/'].includes(route.path)) return true;
		return false;
	});

	const queryParams = computed(() => {
		return !isObjectEmpty(route.query, {ignoreArrays: true}) ? route.query : null;
	});

	// fetch category data
	const category = ref(null);
	if (props.fetch) {
		category.value = await catalog.fetchCategories(props.fetch, {dataKey: props.dataKey}).then(res => (res?.data ? res.data : null));
	} else {
		if (route.meta.contentType == 'tag') {
			const {fetchTags} = useTag();
			const lastUrlSegment = getLastUrlSegment(route.path);
			const tagId = getLastArrayElement(lastUrlSegment.split('-'));

			const res = await fetchTags({module: 'catalog', id: tagId, single: true});
			const tagData = res?.data?.length ? res.data[0] : null;
			category.value = {
				...tagData,
				seo_h1: tagData?.title,
			};
		} else {
			await fetchData();
		}

		if (props.gtmTracking && gtm && contentType.value === 'category') gtm.gtmTrack('pageView', {url: route.fullPath, title: category.value?.title});
		if (props.fbTracking && fbCapi && contentType.value === 'category') fbCapi.sendEvent('pageView', {title: category.value?.title});
	}

	async function fetchData() {
		const slug = getUrlSegments(route.path, {ignoreLang: true});
		if (slug.length <= 1 && !__CATALOG_SEO_URL__ && !__CATEGORY_WITHOUT_BASE_URL__) {
			const pageData = await page.fetch({slug: route.path});
			if (pageData) {
				category.value = pageData;
				category.value.pageId = pageData.id;
			} else {
				useLog('Missing page data! Check if CMS page exists.', 'debug');
			}
			return;
		}

		if (route.query.search_q) {
			// if route query contains search term, fetch search page. Otherwise fetch category by slug
			category.value = await page.fetch({slug: getAppUrl('search')});
			route.meta.entityId = null;
			route.meta.pageId = category.value.id;

			// append category position to page data. Needed when searching within specific category
			let catData = null;
			if (slug.length > 1) {
				const catRes = await catalog.fetchCategoryBySlug({slug: route.path});
				if (catRes?.data?.length) catData = catRes.data[0];
			}
			category.value.position_h = catData?.position_h ? catData?.position_h : null;
			return;
		}

		const categoryData = useState('catalogCategory');
		category.value = categoryData.value;
	}

	// Check if category is a landing page
	function isLanding(categoryData) {
		if (categoryData?.landing_page) {
			const queryParams = cleanObject(route.query);

			// Return true if there are no query parameters or if the only query parameter is "sort" or "fbclid" (facebook tracking)
			const allowedParams = ['sort', 'fbclid'];
			return Object.keys(queryParams).every(param => allowedParams.includes(param));
		}
		return false;
	}

	// add root and parent category positions to category data
	if (category.value?.position_h) {
		const positions = category.value.position_h.split('.') || [];
		category.value.root_position_h = positions.length > 1 ? positions[0] : category.value.position_h;
		category.value.parent_position_h = positions.length > 1 ? positions.slice(0, -1).join('.') : category.value.position_h;
	}

	// if content type is manufacturer, fetch manufacturer data
	if (contentType.value === 'manufacturer') {
		const manufacturerData = await catalog.fetchManufacturers({slug: lastSlug, limit: 1, mode: 'full'});
		if (manufacturerData.data?.length) {
			category.value = manufacturerData.data[0];
			route.meta.entityId = manufacturerData.data[0].id;
			if (props.gtmTracking && !props.fetch && gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: manufacturerData.data[0]?.title});
			if (props.fbTracking && !props.fetch && fbCapi) fbCapi.sendEvent('pageView', {title: manufacturerData.data[0]?.title});
		} else {
			useLog(`Manufacturer ${lastSlug} not found`, 'debug');
			category.value = null;
			await navigateTo('/404/');
		}
	}

	// if content type is seller, fetch seller data
	if (__SELLERS__ && contentType.value === 'seller') {
		const sellerSlug = getUrlSegments(route.path, {ignoreLang: true, limit: 1, offset: 1});
		const sellerData = await catalog.fetchSellers({slug: sellerSlug, limit: 1, mode: 'full', single: true});
		if (sellerData?.data?.length) {
			category.value = sellerData.data[0];
			if (props.gtmTracking && gtm && !props.fetch) gtm.gtmTrack('pageView', {url: route.fullPath, title: sellerData.data[0]?.title});
			if (category?.value?.status == 'Terminated') {
				await navigateTo('/404/');
			}
		} else {
			useLog(`Seller ${sellerSlug} not found`, 'debug');
			category.value = null;
			await navigateTo('/404/');
		}
	}

	// if content type is list, fetch list data
	if (contentType.value === 'list') {
		const fetchOptions = {
			slug: lastSlug,
			limit: 1,
			mode: 'full',
			single: true,
		};
		if (config?.catalog?.listsResponseFields?.length) fetchOptions.response_fields = config.catalog.listsResponseFields;
		if (props.listResponseFields) fetchOptions.response_fields = props.listResponseFields;

		const listData = await catalog.fetchLists(fetchOptions);
		if (listData.data?.length) {
			category.value = listData.data[0];
			if (props.gtmTracking && !props.fetch && gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: listData.data[0]?.title});
			if (props.fbTracking && !props.fetch && fbCapi) fbCapi.sendEvent('pageView', {title: listData.data[0]?.title});
			route.meta.entityId = listData.data[0].id;
		} else {
			useLog(`List ${lastSlug} not found`, 'debug');
			category.value = null;
			await navigateTo('/404/');
		}
	}

	// generate thumbs
	if (category.value && props.thumbPreset) {
		await generateThumbs({
			data: category.value,
			preset: props.thumbPreset,
			dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
		});
	}

	if (category.value?.landing_page == '0') category.value.landing_page = null; // FIXME PROG nekad je landing page '0' a nekad null. Treba uvijek biti null ako nije landing page

	// emit event on load/server side
	emit('load', {item: category.value});

	// emit event when component is mounted
	onMounted(() => {
		emit('loadCategory', {item: category.value});
		if (category.value?.id) {
			if (contentType.value === 'category' && hapi) hapi.sendEvent('catalogcategory', category.value.id);
			if (contentType.value === 'manufacturer' && hapi) hapi.sendEvent('catalogmanufacturer', category.value.id);
			if (contentType.value === 'list' && hapi) hapi.sendEvent('cataloglist', category.value.id);
		}
		if (props.log) useLog(['BaseCatalogCategory', category.value]);
	});

	// provide category data to child components
	provide('baseCatalogCategoryData', {category, contentType});
</script>
