<template>
	<slot
		:shippingData="woltData"
		:valid="valid"
		:onSelect="onSelect"
		:loading="loading"
		:dates="alternativeDates"
		:hours="hours"
		:minutes="minutes"
		:dropoff="scheduledDropoff"
		:selectedDate="selectedDate"
		:selectedHours="selectedHours"
		:selectedMinutes="selectedMinutes"
		:cash="cash"
		:selectedCash="cashAmount" />
</template>

<script setup>
	const {getCartData, updateShipping} = useWebshop();
	const cartData = computed(() => getCartData());
	const {debounce} = useUtils();
	const loading = ref(false);

	const scheduledDropoff = ref(false);
	const selectedDate = ref();
	const selectedHours = ref();
	const selectedMinutes = ref();

	const cash = ref(false);
	const cashAmount = ref();

	const woltData = computed(() => {
		return cartData.value?.parcels[0]?.shipping?.selected;
	});

	// Get alternative dates from wolt data
	const alternativeDates = computed(() => {
		if (!woltData.value?.shipment_promise?.schedule?.schedule) return [];
		return Object.entries(woltData.value?.shipment_promise?.schedule?.schedule);
	});

	const hours = computed(() => {
		if (!alternativeDates.value?.length || !selectedDate.value) return [];
		const date = alternativeDates.value.find(option => option[0] == selectedDate.value);

		const start = Number(date[1].opening_hour);
		const end = Number(date[1].closing_hour);

		let hours = [];
		for (let i = start; i <= end; i++) {
			hours.push(i);
		}
		return hours;
	});

	const minutes = computed(() => {
		if (!alternativeDates.value?.length || !selectedDate.value || !selectedHours.value) return [];
		const date = alternativeDates.value.find(option => option[0] == selectedDate.value);

		const openingHour = Number(date[1].opening_hour);
		const openingMinute = Number(date[1].opening_hour_minute);
		const closingHour = Number(date[1].closing_hour);
		const closingMinute = Number(date[1].closing_hour_minute);
		const selectedHour = Number(selectedHours.value);

		let minutes = [];
		let minMinute = 0;
		let maxMinute = 55;

		// If selected hour is the opening hour, minimum minutes should be opening minutes
		if (selectedHour === openingHour) {
			minMinute = openingMinute;
		}

		// If selected hour is the closing hour, maximum minutes should be closing minutes
		if (selectedHour === closingHour) {
			maxMinute = closingMinute;
		}

		// Generate minutes in 5-minute increments
		for (let i = minMinute; i <= maxMinute; i += 5) {
			// Round up to next 5-minute increment if opening minute is not divisible by 5
			if (selectedHour === openingHour && i < openingMinute) {
				continue;
			}
			minutes.push(i < 10 ? `0${i}` : i.toString());
		}

		return minutes;
	});

	// Initialize data from cart
	onMounted(() => {
		setTimeout(() => {
			const initData = cartData.value?.parcels[0]?.shipping?.selected?.shipment_promise;
			scheduledDropoff.value = initData?.is_dropoff_time_scheduled;
			selectedDate.value = initData?.stored_date;
			selectedHours.value = initData?.stored_time?.split(':')[0];
			selectedMinutes.value = initData?.stored_time?.split(':')[1];
		}, 1000);
	});

	const valid = computed(() => {
		let isValid = true;
		if (scheduledDropoff.value) {
			// Check if all required fields are selected
			if (!selectedDate.value || !selectedHours.value || !selectedMinutes.value) {
				isValid = false;
			}
			// Check if selected values are actually available in current options
			else if (selectedHours.value && !hours.value.includes(Number(selectedHours.value))) {
				isValid = false;
			} else if (selectedMinutes.value && !minutes.value.includes(selectedMinutes.value)) {
				isValid = false;
			}
		}
		if (cash.value && !cashAmount.value) {
			isValid = false;
		}
		return isValid;
	});

	const debouncedSubmit = debounce(onSubmit, 500);
	async function onSubmit() {
		if (!valid.value) return;

		loading.value = true;
		const cartProducts = getCartData('products');
		let shippingData = {};

		if (scheduledDropoff.value && selectedDate.value && selectedHours.value && selectedMinutes.value) {
			shippingData.scheduled_dropoff = 1;
			shippingData.scheduled_dropoff_date = selectedDate.value;
			shippingData.scheduled_dropoff_time = `${selectedHours.value}:${selectedMinutes.value}`;
		}

		if (cash.value && cashAmount.value) {
			shippingData.prepared_cash = 1;
			shippingData.prepared_cash_amount = cashAmount.value.toString();
		}

		const res = await updateShipping({
			shipping_id: woltData.value.id,
			shopping_cart_codes: cartProducts.map(product => product.shopping_cart_code),
			shipping_data: shippingData,
		});

		loading.value = false;
		return res;
	}

	async function onSelect(event) {
		// Update shipping data
		if (event.target.name == 'dropoff') scheduledDropoff.value = event.target.checked;
		if (event.target.name == 'date') selectedDate.value = event.target.value;
		if (event.target.name == 'hours') selectedHours.value = event.target.value;
		if (event.target.name == 'minutes') selectedMinutes.value = event.target.value;
		if (event.target.name == 'cash') cash.value = event.target.checked;
		if (event.target.name == 'cashAmount') cashAmount.value = event.target.value;

		// Reset fields based on user input
		if (event.target.name == 'date') {
			selectedHours.value = null;
			selectedMinutes.value = null;
		}
		if (event.target.name == 'hours') {
			selectedMinutes.value = null;
		}
		if (event.target.name == 'cash') {
			cashAmount.value = null;
		}

		// If form is valid, submit
		await debouncedSubmit();
	}
</script>
