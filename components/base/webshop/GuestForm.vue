<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" />
	</BaseForm>
</template>

<script setup>
	const auth = useAuth();
	const webshop = useWebshop();
	const {getAppUrl} = useApiRoutes();
	const emit = defineEmits(['load', 'submit']);
	const props = defineProps({
		submitUrl: {
			type: String,
			default: 'webshop_customer',
		},
	});
	const loading = ref(false);

	// emit event when step is loaded. Can be used to trigger analytics event or similar
	onMounted(() => emit('load'));

	// guest checkout fields
	const fields = [
		{
			'name': 'email',
			'value': '',
			'type': 'email',
			'validation': [
				{
					'type': 'not_empty',
					'value': null,
					'error': 'error_not_empty',
				},
				{
					'type': 'max_length',
					'value': 60,
					'error': 'error_max_length',
				},
				{
					'type': 'email',
					'value': null,
					'error': 'error_email',
				},
				{
					'type': 'email_domain',
					'value': null,
					'error': 'error_email_domain',
				},
			],
		},
	];

	async function onSubmit({values}) {
		loading.value = true;
		try {
			await webshop.continueAsGuest({
				email: values.email,
			});
			const data = await auth.fetchIsLogin({
				continue_as_guest: 1,
				update_customer_data: 1,
			});
			await webshop.fetchCart();
			await webshop.fetchCustomer();
			if (data?.user_id || data?.continue_as_guest_exist) {
				return navigateTo(getAppUrl(props.submitUrl));
			}
		} catch (err) {
			useLog(err, 'error');
		}
		emit('submit', values);
		loading.value = false;
	}
</script>
