<template>
	<slot />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const {order, fetchOrder, fetchOrderStatus} = useWebshop();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	onMounted(async () => {
		if (route?.query?.order_identificator && gtm) {
			// cover cases if order_identificator has multiple values separated by '|' (?order_identificator=111|xyz|webshoporder)
			const urlOrderIdentificator = route.query.order_identificator.split('|');
			const orderIdentificator = urlOrderIdentificator?.length > 1 ? `${urlOrderIdentificator[0]}-${urlOrderIdentificator[1]}` : urlOrderIdentificator[0];
			const orderRes = await fetchOrder({code: orderIdentificator});

			if (!orderRes?.success) {
				return console.error('BaseWebshopOrder error:', orderRes?.data?.label_name);
			}

			// gtm tracking of refund
			if (orderIdentificator) {
				const orderStatus = await fetchOrderStatus({code: orderIdentificator});
				if (!orderStatus.success) {
					gtm.gtmTrack('refund', {
						cart: orderRes.data,
						transaction_id: orderRes.data.cart.order_id,
					});
				}
			}
		}
	});
</script>
