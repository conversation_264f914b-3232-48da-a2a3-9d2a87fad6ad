<template>
	<slot :selectPromotion="onSelect" />
</template>

<script setup>
	const {gtmTrack} = useGtm();
	const {debounce} = useUtils();
	const props = defineProps({
		data: {
			type: Object,
			required: true,
		},
	});

	onMounted(() => gtmTrack('viewPromotion', props.data));

	// Debounced onSelect function to prevent multiple triggers
	const onSelect = debounce(() => {
		gtmTrack('selectPromotion', props.data);
	}, 100);
</script>
