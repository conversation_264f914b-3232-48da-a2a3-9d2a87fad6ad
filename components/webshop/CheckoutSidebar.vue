<template>
	<BaseWebshopCart v-slot="{cart, total, cartUrl, parcels, loading}">
		<aside class="cart-box">
			<div class="header">
				<BaseCmsLabel code="wc_sidebar_title" tag="div" class="title" />
				<NuxtLink :to="cartUrl" class="edit-cart">
					<BaseCmsLabel code="change_cart_short" />
				</NuxtLink>
			</div>

			<div class="cart-small" v-if="parcels?.length">
				<template v-for="parcel in parcels" :key="parcel.number">
					<WebshopCartItemSmall v-for="item in parcel.items" :data="item" :key="item.id" />	
				</template>
			</div>

			<div class="totals" v-if="total">
				<div class="other-totals">
					<div v-if="giftCard" class="total"><BaseCmsLabel code="value_card" />: <span>- <BaseUtilsFormatCurrency :price="giftCard" /></span></div>
					<div class="total red" v-if="Number(total.discount) > 0"><BaseCmsLabel code="total_discount" />: <span><BaseUtilsFormatCurrency :price="total.discount" /></span></div>
					<div class="total">
						<BaseCmsLabel code="shipping" />:
						<span v-if="Number(total.shipping) == 0" class="green"><BaseCmsLabel code="free" /></span>
						<span v-else><BaseUtilsFormatCurrency :price="total.shipping" /></span>
					</div>
				</div>
				<div class="total subtotal">
					<BaseCmsLabel code="total_to_pay_cart" />: <span><BaseUtilsFormatCurrency :wrap="true" :price="total.total_items_total" /></span>
					<div class="tax">Uklj. <template v-if="currentLang === 'hr'">PDV</template><template v-else>DDV</template></div>
				</div>
			</div>

			<div v-if="!props.formsMeta?.valid || cart?.warnings?.cart?.length" class="wc-note">
				<BaseCmsLabel code="cart_next_step_note" />
			</div>

			<button class="btn" :class="{'btn-disabled': !props.formsMeta?.valid || cart?.warnings?.cart?.length}">
				<UiLoader class="dots" v-if="checkoutLoading || creation" />
				<BaseCmsLabel v-else code="wc_next_step" />
			</button>
			
			<!--
			<button v-else class="btn btn-green btn-cart-box btn-icon-arrow btn-submit" type="submit">
				<span v-if="review"><BaseCmsLabel code="confirm_order" /></span>
				<span v-else><BaseCmsLabel code="wc_next_step" /></span>
			</button>
			-->
		</aside>
	</BaseWebshopCart>
</template>

<script setup>
	const webshop = useWebshop();
	const cartData = computed(() => webshop.getCartData());
	const labels = useLabels();
	const {get: currentLang} = useLang();
	const props = defineProps({
		formsMeta: Object,
	});

	const { checkoutLoading, formValid, creation } = useCheckout();

	// gift card
	const giftCard = computed(() => {
		const giftCardPayment = cartData.value?.cart?.payments?.selected?.find(el => el.widget == 'giftcard');
		if(!giftCardPayment) return null;

		const giftCardData = giftCardPayment?.giftcard_data?.find(el => el.extra_info != null);
		if(giftCardData) {
			return giftCardData.extra_info.amount_to_be_used;
		}
	});

	//loyalty code
	const totalLoyalty = computed(() => {
		let loyalty_code = cartData.value && cartData.value.customer?.loyalty ? cartData.value.customer?.loyalty : null;
		let desc = labels.get('total_with_loyalty') ? labels.get('total_with_loyalty') : null;
		if (desc && loyalty_code) {
			desc = desc.replace('%LOYALTY_CODE%', loyalty_code);
		}
		return desc;
	});
</script>

<style lang="less" scoped>
	.header{position: relative;}
	.cart-box{
		background: #fff; padding: 15px 24px 35px; border-radius: 12px;
		@media (max-width: @m){padding: 15px 15px 25px; border-radius: 0;}
	}
	.title{
		font-size: 24px; font-weight: bold; padding-bottom: 15px;
		@media (max-width: @m){font-size: 20px;}
	}
	.edit-cart{position: absolute; right: 0; top: 5px; font-size: 14px;}
	.cart-small{padding-bottom: 24px;}
	.total{display: flex; justify-content: space-between; flex-wrap: wrap;}
	.totals{padding-bottom: 20px; font-weight: bold; font-size: 14px;}
	.subtotal{
		border-top: 1px solid var(--gray3); padding-top: 15px; font-size: 22px; margin-top: 17px;
		@media (max-width: @m){font-size: 16px;}
	}
	.tax{width: 100%; font-size: 10px; color: var(--gray5); font-weight: normal; text-align: right;}
	.other-totals{display: flex; flex-direction: column; gap: 8px;}
	.wc-note{text-align: center; text-wrap: balance; padding: 0 20px 24px; font-weight: bold; font-size: 15px;}
	.btn{width: 100%;}
</style>
