<template>
	<div class="phone-country">
		<span v-if="selectedCountry" class="phone-country-selected" :class="{'active': phoneAutocomplete}">
			<input type="button" :id="field?.name + '_country'" :name="field?.name + '_country'" @blur="onBlurPhone" />
			<label :for="field?.name + '_country'" @click="onFocusPhone">
				<span class="num">(+{{ selectedCountry?.key.slice(2) }})</span>
				<span class="arrow">▼</span>
			</label>
		</span>
		<ul v-if="phoneAutocomplete" class="phone-country-autocomplete">
			<li v-for="option in field?.options_prefix" :key="option.key" @click="selectCountry(option)">
				<span class="num">+{{ option.key.slice(2) }}</span>
				<span class="title">{{ option.title }}</span>
			</li>
		</ul>
	</div>
	<div class="phone-extra">
		<input type="tel" :id="field?.name + '_extra'" :name="field?.name + '_extra'" v-model="extraValue" @input="onInput" :placeholder="labels.get('phone_example')" />
	</div>
</template>

<script setup>
	const config = useAppConfig();
	const props = defineProps(['field', 'input']);
	const emit = defineEmits(['selectCountryValue', 'phoneValueInput']);
	const extraValue = ref('');
	const labels = useLabels();

	//selected
	const selectedCountry = ref(null);
	onMounted(() => {
		const selectedOption = props.field?.options_prefix.find(prefix => props.field?.value?.startsWith(prefix.key));
		if (selectedOption) {
			extraValue.value = props.field?.value?.replace(selectedOption?.key, '');
			selectedCountry.value = selectedOption;
		} else {
			selectedCountry.value = props.field?.options_prefix.find(option => option.code === "si");
		}
	})

	watch(
		() => props.input,
		() => {
			const selectedOption = props.field?.options_prefix.find(item => item.code == 'si');
			if (selectedOption) {
				extraValue.value = '';
				selectedCountry.value = selectedOption;
			}
		}
	)

	//select country
	function selectCountry(value) {
		selectedCountry.value = value;
		phoneAutocomplete.value = false;

		const combinedValue = `${selectedCountry.value.key}${extraValue.value}`;
		if(combinedValue) {
			emit('selectCountryValue', combinedValue);
		}
	}

	//send value
	function onInput(event) {
		let value = event.target.value;
		// Remove the leading '0' from the input value
		if (value.length > 4 && value.startsWith('0')) {
			extraValue.value = value.replace(/^0/, '');
		}

		const combinedValue = `${selectedCountry.value.key}${extraValue.value}`;
		if(combinedValue) {
			emit('phoneValueInput', combinedValue);
		}
	}

	//phone autocomplete
	const phoneAutocomplete = ref(false);

	function onFocusPhone() {
		phoneAutocomplete.value = !phoneAutocomplete.value;
		if(phoneAutocomplete.value == false) {
			onBlurPhone();
		}
	}

	function onBlurPhone() {
		if(phoneAutocomplete.value == true) {
			setTimeout(() => {
				phoneAutocomplete.value = false;
			},200);
		}
	}
</script>

<style lang="less" scoped>
	.phone-country{
		display: block; width: 70px; height: 48px; font-size: 15px; line-height: 1; position: absolute; top: 0; left: 12px; z-index: 11; color: var(--gray5); display: flex; align-items: center;
	}
	.phone-country-selected{
		display: flex; align-items: center; position: relative; cursor: pointer; padding-top: 1px; width: 100%;
		input{width: 0; height: 0; padding: 0; border: none; cursor: pointer;}
		label{
			display: flex; align-items: center; padding: 0; flex-grow: 1; font-size: 16px; color: var(--gray5); line-height: 1; top: unset!important; left: unset!important; position: relative!important; cursor: pointer;
		}
		&.active .arrow{transform: rotate(180deg);}
	}
	.num{flex-grow: 1;}
	.arrow{font-size: 12px;}
	.phone-country-autocomplete{
		display: block; overflow: auto; min-width: 150px; padding: 10px; max-height: 250px; border-radius: 8px; background: #fff; box-shadow: 0px 8px 16px 0px #0000001F; font-size: 14px; position: absolute; top: 52px; left: -12px; z-index: 11111;
		&::-webkit-scrollbar {-webkit-appearance: none;width: 4px; background: #E0E8EE;}
		&::-webkit-scrollbar-thumb {border-radius: 0;background-color: #99AAB8;-webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);}
		&::-webkit-scrollbar-track {border: 1px solid #E0E8EE;}
		li{
			display: flex; align-items: center; padding: 7px 10px; border-radius: 2px; white-space: nowrap; border-bottom: 1px solid var(--borderColor); cursor: pointer;
			.num{min-width: 35px; margin-right: 6px;}
			&:last-child{border-bottom: none;}
			&:hover, &.active{background: var(--gray3); color: var(--textColor);}
		}
	}
	.phone-extra{
		input{padding-left: 90px;}
		input:focus + .sample{opacity: 0; font-size: 12px;}
		.sample{
			color: var(--textColor); position: absolute; top: 14px!important; left: 95px; .transition(opacity);
		}
	}
</style>
