<template>
	<slot :onContinueAsGuest="continueAsGuest" :onLoginApi="loginApi" :loading="loading" :urls="getAppUrls()" />
</template>

<script setup>
	const webshop = useWebshop();
	const {getAppUrl, getAppUrls} = useApiRoutes();
	const auth = useAuth();
	const props = defineProps({
		redirectUrl: {
			type: String,
			default: 'webshop_customer',
		},
		redirectLoginUrl: {
			type: String,
			default: 'webshop_login',
		},
		step: String,
	});
	const loading = ref(false);

	// set current user as guest and redirect to next step
	async function continueAsGuest() {
		loading.value = true;
		await webshop.continueAsGuest();
		const data = await auth.fetchIsLogin();
		if (data?.continue_as_guest_exist) {
			loading.value = false;
			return navigateTo(getAppUrl(props.redirectUrl));
		}
	}

	// redirect to login page for external login (bigbang keycloak)
	async function loginApi() {
		return navigateTo(getAppUrl('auth_login'));
	}
</script>
