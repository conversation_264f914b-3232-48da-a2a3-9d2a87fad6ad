<template>
	<WebshopCheckoutPanel :title="labels.get('customer_details')">
		<div class="content" v-if="customer">
			<div class="col">
				<p class="title"><BaseCmsLabel code="bill_address_pickup" /></p>
				<p v-if="customer.first_name || customer.last_name">
					<template v-if="customer.first_name">{{customer.first_name}}</template> <template v-if="customer.last_name">{{customer.last_name}}</template>
				</p>
				<p v-if="customer.address">
					<template v-if="customer.address.street">{{customer.address.street}}</template>
					<template v-if="customer.address.number">{{customer.address.number}}</template><template v-if="customer.address.zipcode">, {{customer.address.zipcode}}</template> <template v-if="customer.address.city">{{customer.address.city}}</template>
				</p>
				<p v-if="customer.phone">{{customer.phone}}</p>
			</div>
			<div class="col" v-if="customer.b_first_name || customer.b_last_name || customer.b_address.b_street || customer.b_address.b_number || customer.b_address.b_zipcode || customer.b_address.b_city || customer.b_phone">
				<p class="title">Adresa dostave računa</p>
				<p v-if="customer.b_first_name || customer.b_last_name">
					<template v-if="customer.b_first_name">{{customer.b_first_name}}</template> <template v-if="customer.b_last_name">{{customer.b_last_name}}</template>
				</p>
				<p v-if="customer.b_address">
					<template v-if="customer.b_address.b_street">{{customer.b_address.b_street}}</template>
					<template v-if="customer.b_address.b_number">{{customer.b_address.b_number}}</template><template v-if="customer.b_address.b_zipcode">, {{customer.b_address.b_zipcode}}</template> <template v-if="customer.b_address.b_city">{{customer.b_address.b_city}}</template>
				</p>
				<p v-if="customer.b_phone">{{customer.b_phone}}</p>
			</div>
		</div>
		<template #actions>
			<NuxtLink :to="getAppUrl('webshop_customer')">
				<BaseCmsLabel code="edit" />
			</NuxtLink>	
		</template>
	</WebshopCheckoutPanel>
</template>

<script setup>
	const labels = useLabels();
	const {getCartData} = useWebshop();
	const {getAppUrl} = useApiRoutes();
	const customer = computed(() => {
		return getCartData('customer');
	})
</script>

<style lang="less" scoped>
	.content{display: flex; font-size: 12px; color: var(--gray7);}
	.col{width: 50%;}
	.title{font-weight: bold; color: var(--textColor);}
	p{padding: 0 0 3px 0;}
</style>