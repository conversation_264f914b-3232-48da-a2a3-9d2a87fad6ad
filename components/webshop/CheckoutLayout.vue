<template>
	<div class="wc-layout">
		<div class="col col1">
			<slot name="col1" />
		</div>
		<div class="col col2">
			<div class="col2-cnt">
				<slot name="col2" />
			</div>
		</div>
	</div>
</template>

<script setup>

</script>

<style scoped lang="less">
	.wc-layout{
		display: flex; gap: 24px; padding: 30px 0;
		@media (max-width: @m){
			flex-direction: column; padding: 17px 0 20px; gap: 13px;
		}
	}
	.col2{
		width: 465px; padding-top: 60px;
		@media (max-width: @m){width: auto; padding-top: 0; margin: 0 calc((var(--wrapperMargin) * -1));}
	}
	.col1{flex: 1;}
</style>