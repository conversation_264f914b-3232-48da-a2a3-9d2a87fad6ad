<template>
	<div :class="['item', {'cart-item-error': !data.quantity || data?.errors?.length || unavailable == 0}]" :id="'position-' + data.item.code">
		<div class="cnt">
			<div class="header">
				<div class="image">
					<NuxtLink :to="data.item.url_without_domain">
						<!--<BaseUiImage loading="lazy" :data="data?.image_thumb" default="/images/no-image.jpg" :alt="data.item.title" />-->
						<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width240-height240']" default="/images/no-image.jpg" :alt="data.item.title" />
					</NuxtLink>
				</div>
				<div>
					<div class="title">
						<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
					</div>
					<div v-if="data.condition && data.condition != 'n'" class="info condition">
						<span v-html="labels.get('condition_' + data.condition)"></span>
					</div>
				</div>
			</div>
			<div class="actions">
				
				<!-- 
				Handle cart item errors
				- If item has price change, show price change message with accept button
				- If item is not available or has quantity errors (min/max), show error message with quantity
				-->
				<!--
				<div class="error-items">
					<div class="error-item">
						Lorem ipsum dolor sit, amet consectetur adipisicing el
						<div class="error-item-price">
							<span class="line-through">100,50€</span>
							80,50€
						</div>
						<div class="btn-error-confirm">
							<BaseCmsLabel code="cart_error_accept_new_price" />
						</div>
					</div>
				</div>
				-->
				
				<!-- FIXME vidjeti što s cijenama prema cart specifikaciji -->
				<div class="total" :class="{'uau-badge': data.selected_price && data.selected_price == 'uau' && (data.total == data.total_basic)}">
					<div :class="['current-price', {'red': data.discount_amount}]"><BaseUtilsFormatCurrency :wrap="true" :price="data.total" /></div>
					<div v-if="data.discount_amount" class="old-price line-through"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
				</div>

				<!-- FIXME promijeniti labele za wishlist i brisanje proizvoda. Sada piše Odstrani isto kao i za brisanje proizvoda -->
				<CatalogSetWishlists :item="data" mode="cart" />
				<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
					<div class="action remove" :class="{'loading': loading}" @click="onRemove">
						<UiLoader v-if="loading" class="inline" />
						<BaseCmsLabel code="remove" />
					</div>
				</BaseWebshopRemoveProduct>
				<BaseWebshopQty :quantity="data.quantity" :limit="data.available_quantity" :item="data" mode="cart" v-slot="{loading, onUpdate, status, quantity, onDecrement, onIncrement, onReset}">
					<div class="qty-container posr">
						<div class="qty" :class="{'loading': loading}">
							<span class="qty-btn qty-btn-dec" :class="{'disabled': quantity <= 1}" @click="onDecrement"><span>-</span></span>
							<input class="qty-input" type="text" :value="quantity" @keyup="onUpdate" @blur="onReset" />
							<span class="qty-btn qty-btn-inc" :class="{'disabled': quantity >= data.available_quantity}" @click="onIncrement"><span>+</span></span>
						</div>
						<UiLoader mode="dots" class="dark" v-if="loading" />
						<div :class="['qty-message', status]" v-if="status && !loading"><BaseCmsLabel :code="status" /></div>
					</div>
				</BaseWebshopQty>
				<div class="error-items" v-if="data?.errors?.length">
					<div v-for="error in data.errors" :key="error.label_name" :class="['error-item', {'unavailable': error.qty == 0}]">
						<BaseCmsLabel :code="error.label_name" />
						<template v-if="error.label_name == 'error_available_qty' || error.label_name == 'error_min_order_qty' || error.label_name == 'error_max_order_qty'">{{ error.qty }}</template>
						<template v-if="error.label_name == 'error_price_changed'">
							<div class="error-item-price" v-if="error.old_price && error.new_price">
								<span class="line-through"><BaseUtilsFormatCurrency :price="error.old_price" /></span>
								<BaseUtilsFormatCurrency :price="error.new_price" />
							</div>
							<div class="btn-error-confirm" @click="priceConfirm()">
								<span><BaseCmsLabel code="cart_error_accept_new_price" /></span>
							</div>
						</template>
					</div>
				</div>
			</div>
			<div class="extras" v-if="data.insurances?.available?.length || data.services?.available?.length">
				<!-- FIXME promijeniti labele za insurances i services naslove -->
				<!-- FIXME provjeriti flyout rwd kad se sredi na detaljima proizvoda -->
				<div class="extra insurances" :class="{'selected': data.insurances?.selected?.length}" v-if="data.insurances?.available?.length" @click="modal.open('flyout', {mode: 'insurance', footer: true, header: true, headerIcon: 'insurance', content: data})">
					<div class="extra-title extra-select"><BaseCmsLabel code="select_insurance" tag="div" /></div>
					<div class="extra-selected">
						<div v-if="data.insurances?.selected?.length" class="extra-selected-item">
							{{ data.insurances.selected[0].title }} 
							<span class="extra-selected-price"><BaseUtilsFormatCurrency :price="data.insurances.selected[0].price" /></span>
						</div>
						<div v-else class="extra-selected-item">{{ data.insurances.available[0].title }}</div>
					</div>
				</div>

				<div class="extra services" :class="{'selected': data.services?.selected?.length}" v-if="data.services?.available?.length" @click="modal.open('flyout', {mode: 'service', footer: true, header: true, headerIcon: 'services', content: data})">
					<div class="extra-title extra-title-service extra-select"><BaseCmsLabel code="select_services" tag="div" /></div>
					<div class="extra-selected">
						<template v-if="data.services?.selected?.length">
							<div class="extra-selected-item" v-for="service in data.services?.selected" :key="service.code">
								{{ service.title }} 
								<span class="extra-selected-price"><BaseUtilsFormatCurrency :price="service.price" /></span>
							</div>
						</template>
						<div v-else class="extra-selected-item">{{ data.services.available[0].title }}</div>
					</div>
				</div>

				<!-- FIXME testirati s beta podacima jer na mp nema takvog proizvoda (vanjske klime) -->
				<WebshopSpecialService :data="data" />
			</div>
			
			<div class="shippings" v-if="parcel.shipping && data.meta_data?.available_shippings?.length">
				<template v-for="shipping in data.meta_data.available_shippings" :key="shipping.id">
					<template v-if="shipping.code == 'osobno_preuzimanje'">
						<div :class="['shipping', shipping.code]" @click="modal.open('flyout', {mode: 'stores', header: true, footer: true, headerIcon: 'stores', content: props.data?.meta_data?.available_pickup_locations || []})">
							{{ shipping.title }}
							<span v-if="data.status == 5"
								>: predvidoma
								<strong>
									{{formatDateSpecial(shipping.min_date)}}
								</strong>
							</span>
							<span v-else-if="shipping.code != 'hitra_dostava'">
								predvidoma od {{formatDateSpecial(shipping.min_date)}}
							</span>
						</div>
					</template>
					<div v-else :class="['shipping', shipping.code]" @click="shippingFlyout(shipping, formatDateSpecial(shipping.min_date), data.meta_data)">
						<!--<div v-else :class="['shipping', shipping.code]" @click="flyoutShipping(shipping, formatDateSpecial(shipping.min_date), data.meta_data)">-->
						<template v-if="shipping.code == 'dostavna_sluzba'"><BaseCmsLabel code="item_delivery_standard_delivery" /></template>
						<template v-else-if="shipping.code == 'dostavna_sluzba_express'"><BaseCmsLabel code="item_delivery_express_delivery" /></template>
						<template v-else-if="shipping.code == 'bigbang_dostavna'"><BaseCmsLabel code="item_delivery_bigbang_delivery" /></template>
						<template v-else-if="shipping.code == 'bigbang_dostavna_xl'"><BaseCmsLabel code="item_delivery_bigbang_xxl_delivery" /></template>
						<template v-else-if="shipping.code == 'hitra_dostava'">
							<BaseCmsLabel code="item_delivery_premium_cart" />
							<span v-for="fast_shipping_title in shipping.fast_shipping_titles" :key="fast_shipping_title">&nbsp;{{ fast_shipping_title }}*</span>
						</template>
						<span v-if="data.status == 5">: predvidoma {{formatDateSpecial(shipping.min_date)}}</span>
						<span v-else-if="shipping.code != 'hitra_dostava'"> predvidoma od {{formatDateSpecial(shipping.min_date)}}</span>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>

<script setup>
	const {formatDate} = useText();
	const labels = useLabels();
	const props = defineProps(['data', 'parcel']);
	const webshop = useWebshop();
	const modal = useModal();
	const endpoints = useEndpoints();
	const {emit} = useEventBus();

	//errors
	const unavailable = computed(() => {
		const e = props.data.errors;
		if (e?.length) return props.data.errors[0].qty;
	});

	// Remap shipping options to match flyout format (cart and product have different format)
	function shippingFlyout(payload, date, meta) {
		let payloadId = '';
		let fastShippingTitles = [];

		if (payload.code == 'osobno_preuzimanje') payloadId = 'p';
		if (payload.code == 'dostavna_sluzba') payloadId = 's';
		if (payload.code == 'dostavna_sluzba_express') payloadId = 'e';
		if (payload.code == 'bigbang_dostavna') payloadId = 'bb';
		if (payload.code == 'bigbang_dostavna_xl') payloadId = 'bb_xxl';
		if (payload.code == 'hitra_dostava') {
			payloadId = 'bb_fast';
			if (payload.fast_shipping_titles) {
				payload.fast_shipping_titles.forEach(el => {
					fastShippingTitles.push(el);
				});
			}
		}

		const content = {
			id: payloadId,
			shipping_dates: [date],
			date: props.data.shipping_date,
			status: props.data.status,
			shipping_options: [{
				id: payloadId,
				fast_shipping_titles: fastShippingTitles
			}]
		};

		modal.open('flyout', {mode: 'shipping', header: true, headerIcon: 'shipping', content});
	}

	//confirm price
	async function priceConfirm() {
		const cartCode = props.data.shopping_cart_code;
		let itemNewPrice = {};
		if (props.data.errors) {
			props.data.errors.forEach(el => {
				itemNewPrice = el.new_price;
			});
		}

		await useApi(`${endpoints.get('_post_hapi_webshop_accept_new_product_price')}`, {
			method: 'POST',
			body: {'shopping_cart_code': cartCode, 'accept_price': itemNewPrice},
		}).then(async res => {
			await webshop.fetchCart()
		});
	}

	//date format
	function formatDateSpecial(value) {
		const d = new Date(value)
		const date = (d.getDate() < 9) ? '0' + d.getDate() : d.getDate()
		const day = d.getDay()
		const dayOfWeek = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote']
		const month = (d.getMonth() < 9) ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1)

		const dTomorrow = new Date()
		const dToday = new Date()
		let cday
		const tomorrow = dTomorrow
		const today = dToday
		tomorrow.setDate(dTomorrow.getDate() + 1)
		today.setDate(dToday.getDate())
		if (tomorrow.toDateString() === d.toDateString()) {
			cday = 'jutri'
		} else if (today.toDateString() === d.toDateString()) {
			cday = 'danes'
		} else {
			cday = dayOfWeek[day]
		}

		return cday + ', ' + date + '.' + month + '.'
	}
</script>

<style lang="less" scoped>
	.item{
		border-bottom: 1px solid var(--gray3); padding: 10px 19px 25px 150px; margin-bottom: clamp(10px, 2vw, 15px); position: relative;
		@media (max-width: @m){padding: 15px 0;}
		&:last-child{border-bottom: none; margin-bottom: 0;}
	}
	.cart-item-error{
		border: 1px solid var(--errorColor); border-radius: 8px;
		@media (max-width: @m){
			padding: 12px;
			.remove{grid-row: 3;}
			.error-items{
				grid-column: 1/3; font-size: 14px;
				&:before{top: -3px;}
			}
		}
		.actions{
			flex-wrap: wrap; row-gap: clamp(10px, 2vw, 25px);
			@media (max-width: @m){padding-bottom: 0;}
		}
		.extras, .shippings, .set-wishlist-cnt{display: none;}
		.image, .header, .qty-container, .total{opacity: .3;}
		.cnt .error-items{opacity: 1;}
		&:last-child{border: 1px solid var(--errorColor);}
		.action{color: var(--errorColor);}
	}
	.image{
		position: absolute; left: 0; top: 10px; width: 130px; height: 130px; display: flex; align-items: center; justify-content: center;
		:deep(img){
			max-width: 100%; max-height: 130px; display: block; width: auto; height: auto;
			@media (max-width: @m){max-height: 60px;}
		}
		@media (max-width: @m){position: relative; top: auto; flex: 0 0 60px; width: 60px; height: 60px;}
	}
	.cnt{flex: 1;}
	.title{
		font-size: clamp(16px, 2vw, 18px);
		a{
			text-decoration: none; color: var(--colorText);
			&:hover{color: var(--blueDark);}
		}
	}
	.header{
		padding: 0 0 25px;
		@media (max-width: @m){display: flex; gap: 15px; padding: 0 0 15px;}
	}
	.info{font-size: 13px; padding: 5px 0 0; color: var(--gray5);}
	.actions{
		display: flex; align-items: center; padding: 0 0 clamp(20px, 2vw, 25px); gap: 45px;
		@media (max-width: @t){
			display: grid; grid-template-columns: auto auto; gap: 7px;
		}
	}
	.action{font-size: clamp(13px, 1.5vw, 14px); text-decoration: underline; color: var(--blueDark); cursor: pointer; display: flex; align-items: center; gap: 7px;}
	.total{margin-right: auto; font-size: clamp(16px, 2vw, 22px); font-weight: bold; line-height: 1.3;}

	@media (max-width: @t){
		.set-wishlist-cnt{grid-column: 1; grid-row: 2; justify-content: start;}
		.action{grid-column: 2; grid-row: 2; justify-content: end;}
		.total{grid-column: 1; grid-row: 1;}
		.qty{grid-column: 2; grid-row: 2; justify-content: end;}
	}
	.old-price{font-weight: normal; font-size: 14px;}
	.qty{
		display: flex; align-items: center;
		&.loading{opacity: .3; pointer-events: none;}
	}
	.qty-container{
		:deep(.dots-loader){justify-content: center; position: absolute; bottom: -10px; left: 0; right: 0;}
	}
	.qty-btn{
		width: 28px; height: 28px; display: flex; background: var(--gray3); border-radius: 100px; flex-grow: 0; flex-shrink: 0; align-items: center; justify-content: center; cursor: pointer; font-size: 0;
		&.disabled{cursor: not-allowed; opacity: 0.5; pointer-events: none;}
		span{position: relative; width: 46%; height: 2px; background: #000; border-radius: 2px; display: block;}
		&.qty-btn-inc span:before{.pseudo(100%,2px); background: #000; border-radius: 2px; .rotate(90deg);}
	}
	.qty-input{width: 45px; height: 35px; border: none; padding: 0 7px; text-align: center; font-size: clamp(18px, 1.5vw, 20px); color: var(--colorText);}
	.qty-message{position: absolute; top: 36px; left: 0; right: 0; font-size: 11px; text-align: center; line-height: 1.2;}
	.shippings{display: flex; flex-direction: column; gap: 13px; font-size: clamp(14px, 1.5vw, 15px);}
	.shipping{
		position: relative; padding-left: 33px; cursor: pointer;
		&:before{.icon-truck(); font: 15px/1 var(--fonti); position: absolute; left: 0; top: 3px;}
		&.osobno_preuzimanje:before{.icon-store(); font-size: 17px; left: 3px;}
		&.hitra_dostava:before{.icon-clock2(); font-size: 19px; top: 1px; left: 3px;}
		&:after{.icon-arrow-right2(); font: 10px/1 var(--fonti); position: relative; margin-left: 10px; top: -1px;}
	}

	.extras{padding-bottom: clamp(20px, 2vw, 30px); display: flex; flex-direction: column; gap: clamp(12px, 2vw, 15px);}
	.extra{
		border: 1px solid var(--gray2); border-radius: 8px; padding: 14px 20px 14px 15px; cursor: pointer; font-size: clamp(14px, 1.5vw, 15px); position: relative; .transition(border-color);
		@media (max-width: @m){padding: 14px 30px 10px 15px;}
		&:after{.icon-arrow-right2(); font: 15px/1 var(--fonti); position: absolute; right: 15px; top: 50%; transform: translateY(-50%);}
		&:hover{border-color: var(--blueDark);}
		&.selected .extra-title:before{.icon-check(); color: #fff; display: flex; align-items: center; justify-content: center; font-size: 18px; width: 20px; height: 20px; background: var(--blueDark); border-radius: 3px;}
	}
	.extra-title{
		display: flex; align-items: center; font-weight: bold; position: relative; align-items: center; gap: 10px;
		&:before{.icon-insurance(); font: 23px/1 var(--fonti); color: var(--blueDark);}
	}
	.extra-selected{padding-top: 6px;}
	.extra-title-service{
		&:before{.icon-service(); font: 23px/1 var(--fonti); color: var(--blueDark);}
	}
	.extra-selected-item{padding: 1px 0;}
	.extra-selected-price{font-weight: bold; padding-left: 20px;}

	.error-items{
		position: relative; padding: 0 0 0 35px; font-weight: bold; margin-right: auto; width: 100%;
		&:before{.icon-close(); font: 23px/1 var(--fonti); position: absolute; left: 0; top: -1px; color: var(--errorColor);}
	}
	.btn-error-confirm{text-decoration: underline; cursor: pointer; color: var(--blueDark); font-weight: normal;}
	.error-item-price{
		font-weight: normal; padding: 2px 0;
		span{padding-right: 5px;}
	}
</style>