<template>
	<div class="special-service" v-if="specialService && specialService.slug == 'z-montazo'">
		<BaseCmsLabel code="selected_assembly" />: <strong v-if="specialService.title">{{specialService.title}}</strong>
	</div>	
</template>

<script setup>
	const webshop = useWebshop();
	const props = defineProps(['data']);

	const specialService = computed(() => {
		if(props.data.included_services) {
			const serviceKey = Object.keys(props.data.included_services)[0];
			const service = props.data.included_services[serviceKey];

			if(service && service.configurable_attributes) {
				// Get the first configurable attribute
				const attributeKey = Object.keys(service.configurable_attributes)[0];
				const attribute = service.configurable_attributes[attributeKey];
				return attribute;
			}
		}
	})
</script>

<style lang="less" scoped>
	.special-service{
		border: 1px solid var(--gray2); border-radius: 8px; padding: 14px 20px 14px 15px; font-size: clamp(14px, 1.5vw, 15px); position: relative;
	}
</style>