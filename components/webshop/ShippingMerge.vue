<template>
	<div v-if="data.optimize_delivery_time_enabled == true || data.express_delivery_enabled == true " class="wc-shipping-message">
		<div v-if="data.optimize_delivery_time_enabled == true" class="wc-shipping-message-item">
			<span class="title">{{ mergeParcelsTitleLabel }}</span>
			<span v-if="data.optimize_delivery_time_enabled == true && data.optimize_delivery_time == false" @click="separateParcels()" class="button special"><BaseCmsLabel code="separate_parcels" /></span>
			<span v-else @click="mergeParcels()" class="button"><BaseCmsLabel code="merge_parcels" /></span>
		</div>
		<div v-if="data.express_delivery_enabled == true" class="wc-shipping-message-item delivery">
			<span class="title"><BaseCmsLabel code="merge_express_delivery_title" /></span>
			<span v-if="data.express_delivery_enabled == true && data.express_delivery == false" @click="expressDelivery()" class="button special"><BaseCmsLabel code="separate_express_delivery" /></span>
			<span v-else @click="normalDelivery()" class="button"><BaseCmsLabel code="merge_express_delivery" /></span>
		</div>
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const endpoints = useEndpoints();
	const cartData = webshop.getCartData();
	const labels = useLabels();
	const props = defineProps(['data']);
	const { checkoutLoading } = useCheckout();

	async function separateParcels() {
		checkoutLoading.value = true;

		await useApi(`${endpoints.get('_post_hapi_customer_parcel_separate')}`, {
			method: 'POST',
		}).then(async (res) => {
			if(res.success) {
				await webshop.fetchCart();
				checkoutLoading.value = false;
			}
		});
	};

	async function mergeParcels() {
		checkoutLoading.value = true;

		await useApi(`${endpoints.get('_post_hapi_customer_parcel_merge')}`, {
			method: 'POST',
		}).then(async (res) => {
			if(res.success) {
				await webshop.fetchCart();
				checkoutLoading.value = false;
			}
		});
	};

	async function expressDelivery() {
		checkoutLoading.value = true;

		await useApi(`${endpoints.get('_post_hapi_customer_express_delivery')}`, {
			method: 'POST',
		}).then(async (res) => {
			if(res.success) {
				await webshop.fetchCart();
				checkoutLoading.value = false;
			}
		});
	};

	async function normalDelivery() {
		checkoutLoading.value = true;

		await useApi(`${endpoints.get('_post_hapi_customer_normal_delivery')}`, {
			method: 'POST',
		}).then(async (res) => {
			if(res.success) {
				await webshop.fetchCart();
				checkoutLoading.value = false;
			}
		});
	};

	const mergeParcelsTitleLabel = computed(() => {
		const parcelsNumber = cartData.cart?.parcels?.length;
		const mergeParcelsLabel = labels.get('merge_parcels_title');
		const mergeParcelsTitle = mergeParcelsLabel.replace("$p", parcelsNumber);
		return mergeParcelsTitle;
	})
</script>

<style lang="less" scoped>
	.wc-shipping-message{margin-bottom: 20px;}
	.wc-shipping-message-item{
		display: flex; align-items: center; justify-content: space-between; width: 100%; margin: 0 0 -1px; padding: 20px 30px; border: 1px solid var(--blue); font-size: 15px; font-weight: 600;
		.title{
			padding-left: 32px; position: relative;
		}
		.button{
			padding-right: 28px; color: var(--blue); text-decoration: underline; position: relative; cursor: pointer;
			&:hover{
				color: var(--blue); text-decoration: none;
			}
		}

		@media (max-width: @m){
			flex-direction: column; align-items: flex-end; justify-content: unset; padding: 15px; font-size: 14px;
			&.delivery .title:before{font-size: 16px;}
			.title{
				width: 100%; margin-bottom: 5px; padding-left: 28px;
				&:before{font-size: 18px;}
			}
			.button{
				padding-right: 25px;
				&:before{font-size: 18px; top: 2px;}
			}
		}
	}
</style>
