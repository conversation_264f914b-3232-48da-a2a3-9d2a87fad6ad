<template>
	<BaseUtilsAppUrls v-slot="{items: appUrls}">
		<div class="wc-step wc-step1" :class="props.class" v-if="step === 1">
			<NuxtLink :to="appUrls.webshop_customer">
				<span class="num">1</span>
				<BaseCmsLabel tag="span" code="step1" class="title" />
			</NuxtLink>
		</div>
		<div class="wc-step wc-step2" v-if="step === 2" :class="props.class">
			<NuxtLink :to="appUrls.webshop_shipping">
				<span class="num">2</span>
				<BaseCmsLabel tag="span" code="step2" class="title" />
			</NuxtLink>
		</div>
		<div class="wc-step wc-step3" v-if="step === 3" :class="props.class">
			<NuxtLink :to="appUrls.webshop_review_order">
				<span class="num">3</span>
				<BaseCmsLabel tag="span" code="step3" class="title" />
			</NuxtLink>
		</div>
	</BaseUtilsAppUrls>
</template>

<script setup>
	const props = defineProps({
		step: Number,
		disabled: Boolean,
		active: Boolean,
		class: String,
	});
</script>

<style lang="less" scoped>
	.wc-step{
		font-size: 24px; font-weight: 600; padding: 10px 0; line-height: 1.3;
		a{display: inline-flex; vertical-align: top; gap: 10px; text-decoration: none; color: var(--gray6);}
		@media (max-width: @m){font-size: 20px;}
	}
	.wc-step1{padding-top: 0;}
	.num{
		width: 24px; height: 24px; border-radius: 100px; margin-top: 3px; background: var(--gray6); color: #fff; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0; flex-grow: 0;
		@media (max-width: @m){margin-top: 1px;}
	}
	.active{
		a{color: var(--textColor);}
		.num{background: var(--blueDark);}
	}
	.disabled, .active{
		pointer-events: none;
	}
	.completed{
		a{color: var(--textColor);}
		.num{
			background: var(--blueDark); font-size: 0;
			&:after{.icon-check(); font: 16px/1 var(--fonti); font-weight: 600; color: #fff;}
		}
	}
</style>