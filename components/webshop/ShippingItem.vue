<template>
	<div class="wc-package-product">
		<div class="wc-package-product-image"><BaseUiImage :data="data.item?.image_thumbs?.['width110-height110']" default="/images/no-image.jpg" /></div>
		<div class="wc-package-product-title" v-if="data.item?.title">{{ data.item.title }}</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data']);
</script>

<style lang="less" scoped>
	.wc-package-product{
		border-bottom: 1px solid var(--gray3); padding: 10px 0; font-size: 12px; display: flex; gap: 10px; align-items: center;
		&:last-child{border-bottom: none;}
	}
	.wc-package-product-image{
		width: 50px; height: 50px; flex-shrink: 0; display: flex; align-items: center; justify-content: center;
		:deep(img){width: 100%; height: 100%; object-fit: contain;}
	}
</style>
