<template>
	<div class="wc-locations" v-if="locations">
		<div class="wc-locations-title"><BaseCmsLabel code="step_shipping_choose_location" /></div>
		<div class="wc-location-items">
			<div class="wc-location-group" v-for="(key, value) in locations" :key="key">
				<div class="wc-location-group-title">
					<BaseCmsLabel v-if="value == 'true'" code="store_available" tag="div" />
					<BaseCmsLabel v-else code="store_empty_stock" tag="div" class="wc-location-group-title-empty" />
				</div>
				<div class="wc-location-group-items" :class="{'active': showAll[value]}">
					<div class="wc-location" v-for="location in key" :key="location.id">
						<input type="radio" name="location" :id="location.id" :value="location.id" v-model="selectedLocation" @click="updateShipping(location)" />
						<label :for="location.id">
							{{ location.title }}
							<div class="wc-location-detail-item" v-if="getLocationDistance(location.id)">
								<div class="wc-location-detail-title">Udaljenost</div>
								<div>{{ getLocationDistance(location.id).distance }} ({{ getLocationDistance(location.id).duration }})</div>
							</div>
						</label>
						<template v-if="location.id == selectedLocation">	
							<BaseLocationPoint v-slot="{item}" :id="location.id" :seo="false">
								<div class="wc-location-detail">
									<div class="wc-location-detail-item" v-if="item.address">
										<div class="wc-location-detail-title">Adresa</div>
										<div v-html="item.address" />
									</div>
									<div class="wc-location-detail-item" v-if="item.business_hour">
										<div class="wc-location-detail-title">Radno vrijeme</div>
										<div v-html="item.business_hour" />	
									</div>
									<BaseLocationGoogleMap pin="/images/pin.svg" :locations="[item]" api-key="dev" :id="'map'+location.id" :info-window="false" />
								</div>
							</BaseLocationPoint>
						</template>
					</div>
					<button type="button" v-if="key?.length > 3 && !showAll[value]" class="btn btn-outline show-all" @click="showAll[value] = !showAll[value]">Prikaži sve trgovine</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const props = defineProps(['parcel']);
	const {checkoutLoading} = useCheckout();
	const selectedLocation = ref(props.parcel?.shipping?.pickup_location?.selected?.id || null);
	const showAll = ref({});
	const userCoordinates = ref(null);
	const locationDistances = ref({});
	const {getInfo} = useInfo();
	
	// Get Google Maps API key
	const mapsApiKey = computed(() => {
		const key = getInfo('__maps_googleapis_key');
		return key || '';
	});

	// Get user's current location
	function getUserLocation() {
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				position => {
					userCoordinates.value = {
						lat: position.coords.latitude,
						lng: position.coords.longitude
					};
					console.log('User coordinates', userCoordinates.value);
					
					// Calculate distances for all locations once we have user coordinates
					calculateAllDistances();
				},
				error => {
					console.warn('Geolocation error:', error.code);
				}
			);
		}
	}

	// Calculate road distances for all locations
	async function calculateAllDistances() {
		if (!userCoordinates.value || !locations.value) return;
		
		// Load Google Maps API if not already loaded
		if (!window.google?.maps?.DistanceMatrixService) {
			await loadGoogleMapsAPI();
		}
		
		// Flatten locations from all groups
		const allLocations = [];
		Object.values(locations.value).forEach(group => {
			group.forEach(location => {
				if (location.gmap?.lat && location.gmap?.lon) {
					allLocations.push(location);
				}
			});
		});
		
		if (allLocations.length === 0) return;
		
		// Create destinations array for Distance Matrix API
		const destinations = allLocations.map(location => {
			return { lat: parseFloat(location.gmap.lat), lng: parseFloat(location.gmap.lon) };
		});
		
		// Use Distance Matrix API to get road distances
		const service = new google.maps.DistanceMatrixService();
		service.getDistanceMatrix(
			{
				origins: [userCoordinates.value],
				destinations: destinations,
				travelMode: 'DRIVING',
				unitSystem: google.maps.UnitSystem.METRIC
			},
			(response, status) => {
				if (status === 'OK' && response.rows[0]?.elements) {
					// Store distances for each location
					response.rows[0].elements.forEach((result, index) => {
						if (result.status === 'OK') {
							locationDistances.value[allLocations[index].id] = {
								distance: result.distance.text,
								duration: result.duration.text
							};
						}
					});
				}
			}
		);
	}

	// Load Google Maps API
	async function loadGoogleMapsAPI() {
		return new Promise((resolve, reject) => {
			if (window.google?.maps?.DistanceMatrixService) {
				resolve();
				return;
			}
			
			const script = document.createElement('script');
			script.src = `https://maps.googleapis.com/maps/api/js?key=${mapsApiKey.value}&libraries=places`;
			script.async = true;
			script.defer = true;
			script.onload = resolve;
			script.onerror = reject;
			document.head.appendChild(script);
		});
	}

	// Get distance for a specific location
	function getLocationDistance(locationId) {
		return locationDistances.value[locationId] || null;
	}

	// Get user location on component mount
	onMounted(() => {
		getUserLocation();
	});

	const locations = computed(() => {
		const locationData = props.parcel.shipping.pickup_location.available;
		const locationsGroup = locationData.reduce(function (r, a) {
			r[a.has_stock] = r[a.has_stock] || [];
			r[a.has_stock].push(a);
			return r;
		}, Object.create(null));

		// Create a new ordered object with "true" first, then "false"
		const orderedLocationsGroup = {};
		
		// Add "true" group first if it exists
		if (locationsGroup["true"]) {
			orderedLocationsGroup["true"] = locationsGroup["true"];
		}
		
		// Add "false" group second if it exists
		if (locationsGroup["false"]) {
			orderedLocationsGroup["false"] = locationsGroup["false"];
		}

		return orderedLocationsGroup;
	})


	const {currentlySelectedShipping} = inject('webshopPackageData', {currentlySelectedShipping: null});
	async function updateShipping(location) {
		checkoutLoading.value = true;
		const cartCodes = props.parcel.items.map(el => el.shopping_cart_code);
		const shippingId = currentlySelectedShipping.value?.id;
		if(!shippingId || !location.id || !cartCodes.length) {	
			return console.error('Missing shipping ID, cart codes or selected location');
		}
		
		await webshop.updateShipping({
			shipping_id: shippingId,
			shopping_cart_codes: cartCodes,
			shipping_data: {
				location_point_id: location.id,
			}
		});
		checkoutLoading.value = false;
	}	
</script>

<style scoped lang="less">
	.wc-locations{padding: 40px 100px 0;}
	.wc-locations-title{font-size: 15px; font-weight: bold; padding: 0 0 12px;}
	.wc-location-group-title{font-weight: bold; padding: 0 0 12px; font-size: 15px; font-weight: normal;}
	.wc-location-group-items{
		display: flex; flex-direction: column; gap: 12px;
		.wc-location:nth-child(n+4){display: none;}
		&.active .wc-location{display: block;}
	}
	.wc-location-items{display: flex; flex-direction: column; gap: 25px;}
	input[type=radio]+label{
		font-weight: bold; font-size: 15px;border: 1px solid var(--gray2); padding: 12px; border-radius: 8px; padding: 13px 0 13px 40px;
		&:before{left: 12px; top: 13px;}
	}
	input[type=radio]:checked+label{border-color: var(--blueDark);}
	.wc-location-detail{padding: 24px 12px; font-size: 12px; line-height: 1.5;}
	.wc-location-detail-title{font-size: 16px; font-weight: bold; padding: 0 0 10px;}
	.wc-location-detail-item{padding: 0 0 12px;}
	.map{height: 200px; margin-top: 15px;}
	.show-all{min-width: 260px; align-self: flex-start;}
</style>
