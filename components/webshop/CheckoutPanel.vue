<template>
	<div class="panel">
		<div class="panel-title" v-if="title">
			{{ title }}
		</div>
		<div class="actions" v-if="$slots.actions">
			<slot name="actions" />
		</div>
		<slot />
	</div>
</template>

<script setup>
	const props = defineProps({
		title: String,
	});
</script>

<style lang="less" scoped>
	.panel{
		background: #fff; padding: 16px; border-radius: 12px; position: relative;
		@media (max-width: @m){padding: 15px; border-radius: 0; margin: 0 calc((var(--wrapperMargin) * -1));}
	}
	.panel-title{font-size: 18px; padding: 0 0 12px 0; font-weight: bold;}
	.actions{position: absolute; top: 18px; right: 15px; font-size: 14px;}
</style>