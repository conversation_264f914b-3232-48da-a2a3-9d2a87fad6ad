<template>
	<div class="item">
		<div class="row">
			<div class="image">
				<!--<BaseUiImage loading="lazy" :data="data?.image_thumb" default="/images/no-image.jpg" :alt="data.item.title" />-->
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width240-height240']" default="/images/no-image.jpg" :alt="data.item.title" />
			</div>
			<div class="cnt">
				<div class="title">{{ data.item.title }}</div>
				<div class="price">
					<div :class="['current-price', {'red': data.discount_amount}]"><BaseUtilsFormatCurrency :wrap="true" :price="data.total" /></div>
					<div v-if="data.discount_amount" class="old-price line-through"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
				</div>
			</div>
		</div>
		<div class="extras" v-if="data.insurances?.available?.length || data.services?.available?.length">
			<div class="extra insurances" v-if="data.insurances?.selected?.length">
				{{ data.insurances.selected[0].title }} 
				<span class="extra-price"><BaseUtilsFormatCurrency :price="data.insurances.selected[0].price" /></span>
			</div>

			<div class="extra services" v-if="data.services?.selected?.length">
				<div class="extra-item" v-for="service in data.services?.selected" :key="service.code">
					{{ service.title }} 
					<span class="extra-price"><BaseUtilsFormatCurrency :price="service.price" /></span>
				</div>
			</div>

			<!-- FIXME testirati s beta podacima jer na mp nema takvog proizvoda (vanjske klime) -->
			<WebshopSpecialService :data="data" />
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['data']);
</script>

<style lang="less" scoped>
	.item{
		border-bottom: 1px solid var(--gray3); padding: 12px 0; font-size: 12px;
		@media (max-width: @m){font-size: 14px;}
	}
	.row{
		display: flex; gap: 24px;
		@media (max-width: @m){gap: 12px;}
	}
	.image{
		width: 60px; height: 60px; flex-grow: 0; flex-shrink: 0; display: flex; align-items: center; justify-content: center;
		:deep(img){max-height: 60px; width: auto;}
	}
	.title{padding-bottom: 12px;}
	.price{
		font-size: 22px; font-weight: bold; display: flex; align-items: center; gap: 12px;
		@media (max-width: @m){font-size: 16px;}
	}
	.old-price{
		font-size: 14px; font-weight: normal;
		@media (max-width: @m){font-size: 11px;}
	}
	.extras{
		padding-top: 7px; display: flex; flex-direction: column; gap: 5px;
		@media (max-width: @m){padding-top: 10px;}
	}
	.extra{
		position: relative; padding-left: 20px;
		&:before{.icon-insurance(); font: 15px/1 var(--fonti); position: absolute; left: 0; top: 0;}
		&.services:before{.icon-service();}
	}
	.extra-price{font-weight: bold; padding-left: 5px;}
	.extra-item{padding-bottom: 4px;}
</style>