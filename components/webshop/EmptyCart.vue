<template>
	<div class="empty-cart">
		<div class="wrapper empty-cart-wrapper">
			<div class="empty-cart-title" v-interpolation>
				<BaseCmsLabel code="empty_shopping_cart" tag="div" />
				<NuxtLink v-if="!isLoggedIn()" class="btn" :to="getAppUrl('auth_login')"><BaseCmsLabel code="login" /></NuxtLink>
			</div>

			<div class="popular-categories">
				<div class="popular-categories-title"><BaseCmsLabel code="popular_categories" />:</div>
				<CatalogCategories />
			</div>
		</div>
	</div>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const {isLoggedIn} = useAuth();
</script>

<style scoped lang="less">
	.empty-cart{padding: clamp(30px, 5vw, 60px) 0 0;}
	@media (max-width: @m){
		.empty-cart-wrapper{margin: 0;}
	}
	:deep(h2){font-size: clamp(24px, 3vw, 42px); font-weight: bold; padding: 0 0 15px;}
	.empty-cart-title{
		padding-bottom: 10px; text-align: center; text-wrap: balance;
		@media (max-width: @m){padding: 0 var(--wrapperMargin);}
	}
	.btn{min-width: clamp(200px, 30vw, 300px);}
	.popular-categories{padding: clamp(50px, 5vw, 85px) 0 0;}
	.popular-categories-title{font-size: clamp(20px, 3vw, 28px); font-weight: bold; padding: 0 15px clamp(15px, 3vw, 25px); text-align: center; text-wrap: pretty;}
	:deep(.categories){margin: 0;}

	.cw{padding: clamp(50px, 10vw, 90px) 0 0; position: relative;}
	:deep(.swiper-container){display: flex;}
	:deep(.swiper){overflow: initial; width: 100%;} 	
</style>