<template>
	<BaseWebshopCart v-slot="{counter, cartUrl}">
		<NuxtLink :to="cartUrl" class="ww">
			<span class="text"><BaseCmsLabel code="header_btn_cart" /></span>
			<ClientOnly>
				<span class="counter" v-show="counter > 0">{{ counter }}</span>
			</ClientOnly>
		</NuxtLink>
	</BaseWebshopCart>
</template>

<style lang="less" scoped>
	.ww{
		display: flex; align-items: center; justify-content: center; width: 45px; height: 45px; font-size: 0; line-height: 0; text-decoration: none; position: relative;
		&:before{.icon-cart(); font: 22px/1 var(--fonti); color: var(--white);}

		@media (max-width: @t){
			width: 35px; height: 35px;
			&::before{font-size: 19px;}
		}
	}
	.counter{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 18px; height: 18px; background: var(--turquoise); border-radius: 100%; font-size: 11px; line-height: 16px; font-weight: 600; text-align: center; color: var(--blueDark); position: absolute; top: 0; right: 0;
		
		@media (max-width: @t){width: 16px; height: 16px; font-size: 10px; line-height: 15px;}
	}
</style>