<template>
	<div class="wc-package-item" :class="{'field-error': !meta.valid || (value == 'osobno_preuzimanje' && !selectedLocation), 'shipping-disabled': parcel.shipping && parcel.shipping.selected?.disable_change_shipping}">
		<input :name="'shipping-' + parcel.number" :id="'field-shipping-' + parcel.number + '-' + data.id" :value="data.code" type="radio" @click="data.code != 'osobno_preuzimanje' && updateShippingItem(); currentlySelectedShipping = data" v-model="value" />
		<label :for="'field-shipping-' + parcel.number + '-' + data.id">
			<span class="title">{{ data.title }}</span>
			<span class="info">
				<template v-if="data.preorder_package == true">predvidoma <BaseUtilsFormatDate :date="data.min_date" /></template>
				<template v-else-if="data.code == 'hitra_dostava'"><BaseCmsLabel code="wc_shipping_hitra_dostava_info" /></template>
				<template v-else>predvidoma od {{ formatDateLong(data.min_date) }} do {{ formatDateLong(data.max_date) }}</template>
			</span>
			<div class="price-cnt">
				<template v-if="data.shipping_price > 0">
					<template v-if="data.discount_shipping_price != null && data.discount_shipping_price != data.shipping_price">
						<span class="red" v-if="data.discount_shipping_price > 0"><BaseUtilsFormatCurrency :price="data.discount_shipping_price" /></span>
						<span v-else class="green"><BaseCmsLabel code="free" /></span>
					</template>
					<template v-else>
						<span class="price"><BaseUtilsFormatCurrency :price="data.shipping_price" /></span>
					</template>
				</template>
				<template v-else>
					<span class="price green"><BaseCmsLabel code="free" /></span>
				</template>
			</div>
		</label>
	</div>
</template>

<script setup>
	import {useField} from 'vee-validate';

	const webshop = useWebshop();
	const labels = useLabels();
	const emit = defineEmits(['update']);
	const props = defineProps(['data', 'parcel']);
	const { checkoutLoading, selectedShipping } = useCheckout();
	const {currentlySelectedShipping} = inject('webshopPackageData', {currentlySelectedShipping: null});

	//format date long
	function formatDateLong(value) {
		const d = new Date(value)
		const date = (d.getDate() < 9) ? '0' + d.getDate() : d.getDate()
		const day = d.getDay()
		const dayOfWeek = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote']
		const month = (d.getMonth() < 9) ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1)

		const dTomorrow = new Date()
		const dToday = new Date()
		let cday
		const tomorrow = dTomorrow
		const today = dToday
		tomorrow.setDate(dTomorrow.getDate() + 1)
		today.setDate(dToday.getDate())
		if (tomorrow.toDateString() === d.toDateString()) {
			cday = 'jutri'
		} else if (today.toDateString() === d.toDateString()) {
			cday = 'danes'
		} else {
			cday = dayOfWeek[day]
		}

		return cday + ', ' + date + '.' + month + '.'
	}

	// process input
	const {handleChange, value, meta, validate} = useField(
		'shipping-' + props.parcel.number,
		value => {
			if (!value || (value && value == 'osobno_preuzimanje' && !selectedLocation.value)) {
				return false;
			}
			return true;
		},
		{initialValue: props.parcel.shipping.selected ? props.parcel.shipping.selected.code : null}
	);

	// revalidate on shipping location update
	const selectedLocation = computed(() => {
		return props.parcel.shipping.pickup_location.selected ? props.parcel.shipping.pickup_location.selected : null;
	});

	//items shipping
	const itemsFastShipping = computed(() => {
		let value = false;

		if (props.parcel.items) {
			value = props.parcel.items.every(el => {
				return el.meta_data.available_shippings.some(shipping => shipping.code === 'hitra_dostava');
			});
		}

		return value;
	});

	watch(
		() => props.parcel.shipping,
		() => {
			value.value = props.parcel.shipping.selected ? props.parcel.shipping.selected.code : null;
			validate();
			selectedShipping.value = props.parcel.shipping?.selected
		},
		{
			deep: true,
		}
	);

	// update shipping
	const shoppingCartCodes = computed(() => props.parcel.items.map(el => el.shopping_cart_code));
	const updateShippingItem = async () => {
		checkoutLoading.value = true;

		await webshop.updateShipping({
			shipping_id: props.data.id,
			shopping_cart_codes: shoppingCartCodes.value,
			shipping_data: {}
		});

		selectedShipping.value = props.data;

		checkoutLoading.value = false;
	};
</script>

<style lang="less" scoped>
	.wc-package-item{
		display: flex;
		&.shipping-disabled{
			pointer-events: none;
			input[type='radio'] + label {padding-left: 20px;}
			input[type='radio'] + label:before{content: none;}
			.location-btn{display: none;}
		}
		&.loading{
			pointer-events: none;
			&:before{content: ""; width: 40px; height: 40px; background: rgba(255, 255, 255, 0.3) url(assets/images/loader.svg) no-repeat center; background-size: 40px; position: absolute; top: 0; bottom: 0; left: 0; right: 0; z-index: 1;}
		}
		input[type='radio'] + label:before {
			top: 15px; left: 15px;
			@media (max-width: @m) {
				left: 15px; top: 16px;
			}
		}
		input[type='radio'] + label {
			padding: 15px 25px 15px 47px; border: 1px solid var(--gray2); border-radius: 8px;
			.transition(all);
			@media (max-width: @m) {
				padding: 15px 15px 15px 50px;
			}
		}
		input[type='radio']:checked + label {border-color: var(--blueDark);}
		.title{display: block; font-size: 15px; font-weight: 600; .transition(color); padding-right: 90px;}
		.info, .location{display: block; padding-top: 5px; font-size: 12px; color: var(--gray7);}
		.info {
			@media (max-width: @m) {
				padding-right: 90px;
			}
		}
		.price-cnt{position: absolute; top: 15px; right: 20px; font-size: 15px; font-weight: 600; text-align: right;}
		.price { display: block; }
		.price-new { display: block; padding-top: 2px; }
		.location {
			display: inline-flex; justify-content: space-between;
			@media (max-width: @m) {
				display: flex;
				padding-top: 8px;
			}
		}
		.location-btn { flex-grow: 1; padding: 0 13px 0 20px; font-weight: 600; text-align: right; position: relative; }

		&.field-error {
			.location {
				padding-top: 10px;
				font-size: 15px;
				position: relative;
			}
			.location-btn {
				font-size: 14px;
				text-decoration: underline;
				&:hover {
					text-decoration: none;
				}
			}
		}
	}
</style>
