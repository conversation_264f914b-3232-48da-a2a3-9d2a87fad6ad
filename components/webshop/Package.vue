<template>
	<div class="wc-package">
		<div class="wc-package-header">
			<div class="wc-package-title"><BaseCmsLabel code="package" /> {{ parcel.number }} od {{ data.parcels.length }}</div>
			<div class="wc-package-seller">
				<BaseCmsLabel code="seller_item_title" />
				<template v-if="parcel.parcel_name">&nbsp;{{ parcel.parcel_name }}</template>
			</div>
		</div>
		<div class="wc-package-info">
			<div class="wc-package-products" v-if="parcel.items?.length">
				<WebshopShippingItem v-for="item in parcel.items" :key="item.id" :data="item" />
			</div>
		</div>

		<div v-if="parcel.shipping" class="wc-package-items-box" :class="{'loading': checkoutLoading}">
			<div class="wc-package-items-title">
				<template v-if="parcel.shipping.selected?.disable_change_shipping"><BaseCmsLabel code="step_shipping_choose_preorder" /></template>
				<template v-else><BaseCmsLabel code="step_shipping_choose" /></template>
			</div>
			<div class="wc-package-items">
				<div v-if="checkoutLoading || creation" class="wc-package-items-loader"></div>
				<template v-if="parcel?.shipping?.available?.length">
					<WebshopShippingOption v-for="shipping in parcel.shipping.available" :key="shipping.id" :data="shipping" :parcel="parcel" />
				</template>
			</div>
			<WebshopShippingLocations v-if="currentlySelectedShipping?.code == 'osobno_preuzimanje'" :parcel="parcel" />
		</div>
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const props = defineProps(['mode', 'thankyouData', 'parcel']);
	const {creation, checkoutLoading} = useCheckout();
	const currentlySelectedShipping = ref(null);

	onMounted(() => {
		currentlySelectedShipping.value = props.parcel?.shipping?.selected;
	})
	
	const data = computed(() => {
		if (props.mode == 'thankyou') {
			return props.thankyouData;
		} else {
			return webshop.getCartData();
		}
	});

	function formatDate(value) {
		const d = new Date(value)
		const date = (d.getDate() < 9) ? '0' + d.getDate() : d.getDate()
		const day = d.getDay()
		const dayOfWeek = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote']
		const month = (d.getMonth() < 9) ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1)

		const dTomorrow = new Date()
		const dToday = new Date()
		let cday
		const tomorrow = dTomorrow
		const today = dToday
		tomorrow.setDate(dTomorrow.getDate() + 1)
		today.setDate(dToday.getDate())
		if (tomorrow.toDateString() === d.toDateString()) {
			cday = 'jutri'
		} else if (today.toDateString() === d.toDateString()) {
			cday = 'danes'
		} else {
			cday = dayOfWeek[day]
		}

		return cday + ', ' + date + '.' + month + '.'
	}

	provide ('webshopPackageData', {currentlySelectedShipping});
</script>

<style lang="less" scoped>
	.wc-package {
		background: #fff; padding: 16px 16px 36px; border-radius: 12px;
		@media (max-width: @m){padding: 15px; border-radius: 0; margin: 0 calc((var(--wrapperMargin) * -1));}
	}
	.wc-package-products{padding-bottom: 25px;}
	.wc-package-header{display: flex; align-items: center; justify-content: space-between; padding-bottom: 10px;}
	.wc-package-title{flex-shrink: 0; font-size: 18px; font-weight: bold;}
	.wc-package-seller{ font-size: 12px; color: var(--gray7); }
	.wc-package-items-title{text-align: center; font-size: 18px; font-weight: bold; padding: 0 0 24px;}
	.wc-package-items{padding: 0 100px; display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;}
	.wc-package-items-loader{
		display: flex; align-items: center; justify-content: center; background: rgba(255, 255, 255, 0.3); position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 11;
		span{width: 60px; height: 60px; background: url(assets/images/loader.svg) no-repeat center; background-size: 60px; z-index: 11;}
	}
</style>
