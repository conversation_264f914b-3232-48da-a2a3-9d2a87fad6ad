<template>
	<BaseCmsPage v-slot="{page}">
		<div class="wrapper">
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
			<BaseLocationPoints v-slot="{places}">
				<div class="header">
					<div class="header-cnt">
						<h1>{{ page.seo_h1 }}</h1>
						<div class="content cms-content" v-html="page.content" v-if="page.content" />
						<div class="places">
							<div class="btn btn-outline small-radius place" :class="{'active': activePlace?.id == item.id}" v-for="item in places" :key="item.id" @click="activePlace = item">
								{{ item.title }}
							</div>
						</div>
					</div>
				</div>
			</BaseLocationPoints>
			<LazyCmsLocations v-model="activeLocation" :active-place="activePlace" hydrate-on-visible :header="false" class="locations2" />
			<LazyLocationActiveLocation v-if="activeLocation" :location="activeLocation" />	
			<LazyLocationOrders hydrate-on-visible />
		</div>
	</BaseCmsPage>
</template>

<script setup>
	const activeLocation = ref(null);
	const activePlace = ref(null);
</script>

<style lang="less" scoped>
	.header{
		background: #fff; border-radius: var(--borderRadius); padding: 40px 40px 50px; text-align: center; color: #383838; margin-bottom: 20px;
		@media (max-width: @m){
			padding: 20px 15px 30px; border-radius: 0; margin: 0 calc(var(--wrapperMargin) * -1);
		}
	}
	h1{font-weight: bold; padding: 0 0 20px;}
	.header-cnt{max-width: 800px; margin: auto;}
	.content{padding-bottom: 20px;}
	.bc{padding-bottom: 14px;}

	.places{display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; margin-top: 15px;}
	.place{
		font-weight: normal; font-size: 18px; padding: 0 20px;
		@media (max-width: @m){
			font-size: 14px;
		}
		&.active{background: var(--blueDark); color: var(--white);}
	}
	.wrapper{
		padding-bottom: 80px;
		@media (max-width: @m){padding-bottom: 40px;}
	}
</style>
