<template>
	<BaseCmsPage v-slot="{page}">
		<h1 style="display: none" v-if="page?.seo_title">{{ page.seo_title }}</h1>
		<CmsHeroSlider />
		<CmsHomepagePromoFeatured />

		<div class="wrapper">
			<CmsBenefits />
			<ClientOnly>
				<CatalogCategories />
			</ClientOnly>
		</div>

		<ClientOnly>
			<CatalogHomepageLists />
			<CmsHomepageSpecialOffers />
			<CmsHomepagePromo />
			<CatalogHomepageSpecialOffers />
		</ClientOnly>
	</BaseCmsPage>
</template>

<style lang="less">	
	.promo-read-more{
		background: var(--gray2); font-size: 18px; font-weight: bold; border-radius: var(--borderRadius); display: flex; flex-direction: column; gap: 15px; align-items: center; justify-content: center; text-decoration: none; min-height: 225px; color: var(--textColor); transition: background 0.3s, color 0.3s;
		&:hover{background: var(--blue); color: #fff; text-decoration: none;}
		&:before{.icon-frame(); font: 55px/1 var(--fonti); display: block;}
	}
</style>

