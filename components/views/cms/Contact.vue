<template>
	<BaseCmsPage v-slot="{page}">
		<div class="wrapper">
			<CmsBreadcrumbs class="bc-contact" :items="page?.breadcrumbs || []" />
			
			<CmsCardLarge class="contact-card">
				<template #header>
					<div class="contact-image" v-if="page?.main_image_thumbs?.['width1440-height530-crop1']">
						<BaseUiImage :data="page?.main_image_thumbs?.['width1440-height530-crop1']" />
					</div>
				</template>
				<template #content>
					<h1>{{ page?.title }}</h1>
					<div v-if="page?.content" class="contact-description" v-html="page?.content" v-interpolation />
				</template>
			</CmsCardLarge>

			<BaseCmsRotator :fetch="{code: 'contacts', response_fields: ['id','title','element_title_small','element_tel','element_mail','element_content_small']}" v-slot="{items}">
				<div class="cards" v-if="items?.length">
					<CmsContactCard v-for="item in items" :key="item.id">
						<template #header>
							{{item.title}}
							<span v-if="item.element_title_small">{{item.element_title_small}}</span>
						</template>
						<template #content>
							<p class="phone" v-if="item.element_tel">{{item.element_tel}}</p>
							<p class="email" v-if="item.element_mail"><a :href="`mailto:${item.element_mail}`">{{item.element_mail}}</a></p>
							<p class="hours" v-if="item.element_content_small">{{item.element_content_small}}</p>
						</template>
					</CmsContactCard>
				</div>
			</BaseCmsRotator>

			<LazyCmsLocations hydrate-on-visible />
		</div>

		
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.contact-card{
		:deep(p){padding: 0 0 4px;}
	}
	.cards{
		display: flex; flex-wrap: wrap; gap: 20px; margin-top: 20px;
		@media (max-width: 768px){
			flex-direction: column;
		}
		@media (max-width: @m){
			gap: 13px; margin-top: 13px;
		}
	}
	.wrapper{
		padding-bottom: 60px;
		@media (max-width: @m){
			padding-bottom: 30px;
		}
	}
</style>