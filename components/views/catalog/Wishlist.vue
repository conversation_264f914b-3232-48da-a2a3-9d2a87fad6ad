<template>
	<BaseCmsPage v-slot="{page}">
		<div class="wrapper">
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
			<ClientOnly>
				<BaseCatalogWishlist thumb-preset="catalogEntry" v-slot="{items}">
					<div class="header">
						<h1 v-html="page?.seo_h1"></h1>
						<!-- FIXME share wishliste kupca. Provjeriti s BE -->
						<CmsShare :share-label="true" v-if="!isLoggedIn && items?.length" class="wishlist-share" />
					</div>
					
					<!--
					FIXME obavijesti o sniženoj cijeni proizvoda koji su na wishlisti. Provjeriti s BE
					<div class="notify">
						<div class="notify-field">
							<FormToggle id="wishlist-toggle" />
							<label for="wishlist-toggle">Obavijesti me e-mailom o sniženju cijena</label>
						</div>
						<div class="notify-message">Obavijestit ćemo vas na <strong><EMAIL></strong> o svim sniženjima cijena.</div>
					</div>
					-->
					
					<template v-if="items?.length">
						<div class="info" v-if="!isLoggedIn">
							<p><strong>Nemojte izgubiti proizvode s liste želja</strong></p>
							<p><NuxtLink :to="getAppUrl('auth_login')">Prijavi se ili registriraj</NuxtLink> kako bi u bilo kojem trenutku vidjeli svoju listu želja i izbjegli da ih izgubite kada napustite stranicu.</p>
						</div>
						
						<div class="wishlist-items">
							<template v-for="item in items" :key="item.id">
								<CatalogIndexEntry :item="item" class="cp-wishlist" />
							</template>
						</div>
					</template>
					<div v-else>
						<div class="no-items">
							<BaseCmsLabel code="no_wishlists" />
						</div>
					</div>
				</BaseCatalogWishlist>
				<template #fallback>
					<UiLoader />
				</template>
			</ClientOnly>
		</div>
	</BaseCmsPage>

	<ClientOnly>
		<Teleport to="body">
			<CatalogCompare />
		</Teleport>
	</ClientOnly>	
</template>

<script setup>
	const auth = useAuth();
	const {getAppUrl} = useApiRoutes();
	const isLoggedIn = computed(() => auth.isLoggedIn());
</script>

<style lang="less" scoped>
	.wrapper{
		padding: 25px 0 50px; position: relative;
		@media (max-width: @m){padding: 0 0 15px;}
	}
	.bc{
		display: none;
		@media (max-width: @m){display: block; margin-bottom: 20px;}
	}
	.header{
		display: flex; align-items: center; justify-content: space-between; padding-bottom: 45px;
		@media (max-width: @m){padding: 0;}
	}
	.wishlist-items{
		display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 20px;
		@media (max-width: @m){grid-template-columns: repeat(2, 1fr); gap: 10px;}
	}
	h1{
		font-size: clamp(16px, 3vw, 24px); padding: 0;
		@media (max-width: @m){display: none;}
	}
	.cp-wishlist{
		:deep(.set-wishlist) {
			&.active:before{.icon-bin(); color: var(--gray5);}
		}
	}
	.info{
		margin: 0 0 25px; background: #fff; border-radius: var(--borderRadius); padding: 20px; font-size: 12px; color: var(--gray5);
		@media (max-width: @m){margin: 0 0 35px;}
		strong{font-size: clamp(16px, 2vw, 18px); color: var(--textColor);}
		p{padding: 0 0 10px;
			&:last-of-type{padding-bottom: 0;}
		}
	}
	.notify{padding-bottom: 20px;}
	.notify-field{
		display: flex; font-size: 15px; gap: 10px; align-items: center;
		@media (max-width: @m){font-size: 14px;}
		label{cursor: pointer; padding: 0;}
	}
	.notify-message{font-size: 12px; display: inline-block; background: #fff; color: var(--textColor); padding: 10px 15px; border-radius: var(--borderRadius); margin-top: 10px;}
	.wishlist-share{
		color: var(--blueDark); font-size: 14px;
		@media (max-width: @m){
			position: absolute; top: 0; right: 0;
			:deep(.share-tooltip){
				top: 100%;
				&:before{right: 15px;}
			}
		}
		:deep(.share-btn){
			width: auto; height: auto; background: none; box-shadow: none; display: flex; gap: 10px;
			@media (max-width: @m){
				width: 30px; height: 50px; display: flex; align-items: center; justify-content: center; margin: 0;
				span{display: none;}
			}
			&:before{color: var(--blueDark);
				@media (max-width: @m){color: var(--textColor);}
			}
		}
	}
</style>
