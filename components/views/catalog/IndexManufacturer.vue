<template>
	<BaseCmsPage v-slot="{page}" @load="onPageLoad">
		<div class="wrapper">
			<h1 style="position: fixed; top: -600px;" v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
		</div>

		<ClientOnly>
			<BaseCatalogManufacturers :fetch="{special: 1, limit: 10, response_fields: ['id','title','url_without_domain','main_image_upload_path']}" v-slot="{items}">
				<div class="m-special-brands" v-if="items?.length">
					<div class="wrapper">
						<CmsSectionHeader class="m-special-brands-header">
							<BaseCmsLabel code="featured_manufacturers" />
						</CmsSectionHeader>
					</div>
					<div class="posr m-special-brands-slider">
						<UiSwiper name="m-special-brands" :options="{
							slidesPerView: 4.5,
							slidesPerGroup: 4,
							spaceBetween: 20,
						}">
							<BaseUiSwiperSlide v-for="manufacturer in items" :key="manufacturer.id">
								<NuxtLink :to="manufacturer.url_without_domain" class="m-special-brand">
									<BaseUiImage v-if="manufacturer.main_image_upload_path" :src="manufacturer.main_image_upload_path" loading="lazy" :alt="manufacturer.title" />
									<span v-else>{{ manufacturer.title }}</span>
								</NuxtLink>
							</BaseUiSwiperSlide>
						</UiSwiper>
					</div>
				</div>
			</BaseCatalogManufacturers>
		</ClientOnly>

		<BaseCatalogManufacturers :fetch="{hierarchy_by: 'alphabet', limit: 1000, sort: 'title'}" v-slot="{items, onSearch}">
			<div class="wrapper">
				<div class="m-alphabet" v-if="items?.length">
					<div v-for="manufacturer in items" :key="manufacturer.alphabet" @click="scrollTo(`#section-${(manufacturer.alphabet == '*') ? 'all' : manufacturer.alphabet}`, {offset: 90})" class="m-alphabet-item">
						{{ manufacturer.alphabet }}
					</div>
				</div>

				<div class="m-search">
					<input type="text" :placeholder="labels.get('search_brands')" class="m-search-input" @input="onSearch">
				</div>

				<div class="m-items">
					<template v-if="items?.length">
						<div v-for="manufacturer in items" :key="manufacturer.alphabet" :id="manufacturer.alphabet">
							<div class="m-column" :id="`section-${(manufacturer.alphabet == '*') ? 'all' : manufacturer.alphabet}`">
								<div class="m-letter" @click="onToggle">
									<span>{{ manufacturer.alphabet }}</span>
								</div>
								<ul class="m-list">
									<li v-for="item in manufacturer.items" :key="item.id">
										<NuxtLink :to="item.url_without_domain">
											{{ item.title }}
										</NuxtLink>
									</li>
								</ul>
							</div>
						</div>
					</template>
					<template v-else>
						<BaseCmsLabel code="no_manufacturers" />
					</template>
				</div>
			</div>
		</BaseCatalogManufacturers>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {scrollTo} = useDom();
	
	//gtm
	const gtm = useGtmBB();

	function onPageLoad(data) {
		if(!data) return;
		gtm.pageView({
			url: data?.url || '',
			title: data?.title || '',
		});
	}
</script>

<style lang="less" scoped>
	.m-special-brands{margin-bottom: 50px;}
	.m-special-brands-header{margin-bottom: 20px;}
	:deep(.swiper){overflow: initial;}
	.m-special-brand{
		display: flex; background: #fff; border-radius: var(--borderRadius); height: 140px; justify-content: center; align-items: center; padding: 20px;
		&:deep(img){max-height: 70px; max-width: 150px; width: auto; height: auto; filter: grayscale(1);}
	}

	.m-alphabet{display: flex; gap: 8px;}
	.m-alphabet-item{
		display: flex; width: 40px; height: 40px; align-items: center; justify-content: center; border-radius: 100px; background: #fff; border: 1px solid var(--gray2); color: var(--gray5); font-size: 15px; line-height: 1; cursor: pointer; transition: color 0.3s ease, border-color 0.3s ease;
		&:hover{color: var(--blueDark); border-color: var(--blueDark);}
	}

	.m-items{background: #fff; border-radius: var(--borderRadius); padding: 25px; margin-bottom: 70px;}
	.m-column{display: flex; margin-bottom: 15px; font-size: 18px;}
	.m-letter{flex: 0 0 200px; border-right: 1px solid var(--gray2); font-weight: bold; text-transform: uppercase;}
	.m-list{
		list-style: none; padding: 0 50px; column-count: 4; flex-grow: 1;
		li{padding: 2px 0; margin: 0;}
		a{
			text-decoration: none; color: var(--gray5);
			&:hover{color: var(--blueDark);}
		}
	}
	.m-search{
		margin: 20px 0; position: relative;
		input{height: 40px; border: 0; border-radius: 100px; padding: 0 20px; font-size: 15px;}
		&:after{.icon-search(); height: 100%; display: flex; align-items: center; justify-content: center; font: 20px/1 var(--fonti); color: var(--gray5); position: absolute; right: 20px; top: 0;}
	}
</style>
