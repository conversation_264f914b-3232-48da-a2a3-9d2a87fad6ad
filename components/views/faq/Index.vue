<template>
	<BaseCmsPage :fetch-slug-segments="1" v-slot="{page}">
		<BaseFaqCategory v-slot="{item: category, contentType, searchTerm}">
			<div class="wrapper">
				<CmsBreadcrumbs v-if="category.breadcrumbs" :items="category.breadcrumbs" :max-depth="2" />
				<div class="faq-header">
					<h1 class="title">{{ page?.seo_h1 }}</h1>
					<div v-if="page?.content" class="cnt" v-html="page?.content" />
					<ClientOnly>
						<BaseFaqSearchForm v-slot="{onSubmit, onUpdateValue, searchTerm}" @submit="scrollTo('#questions', {offset: 100})">
							<form class="faq-form" @submit="onSubmit">
								<input class="faq-input" name="search_q" type="text" placeholder="Iskanje po bazi pogostih vprašanj" @keyup="onUpdateValue" />
								<button class="btn btn-lightBlue faq-btn" :disabled="!searchTerm" type="submit" />
							</form>
						</BaseFaqSearchForm>
						<template #fallback>
							<UiLoader />
						</template>
					</ClientOnly>
				</div>

				<BaseFaqCategories :fetch="{response_fields: ['id', 'position_h', 'url_without_domain', 'title', 'main_image_thumbs']}" v-slot="{items}" :hierarchy="true">
					<div v-if="items?.length" class="faq-categories">
						<CmsCard v-for="item in items" :key="item.id" :item="item">
							<template #image>
								<BaseUiImage :data="item.main_image_thumbs?.['width600-height340-crop1']" default="/images/no-image.jpg" />
							</template>
							<template #content>
								<div class="title">
									<NuxtLink :to="`${item.url_without_domain}`" @click="scrollToQuestions()">{{ item.title }}</NuxtLink>
								</div>
								<ul v-if="item.children?.length" class="list">
									<li v-for="childItem in item.children" :key="childItem.id">
										<NuxtLink :to="`${childItem.url_without_domain}`" @click="scrollToQuestions()">{{ childItem.title }}</NuxtLink>
									</li>
								</ul>
							</template>
						</CmsCard>
					</div>
				</BaseFaqCategories>

				<BaseFaqQuestions
					:fetch="{
						special: (!category.level || category.level < 2) ? 1 : 0,
						...(searchTerm ? {search_q: searchTerm} : {category_position: category?.position_h})
					}"
					:watch-fetch="true"
					v-slot="{items, loading}">
					<div class="faq-questions" id="questions">
						<div class="faq-questions-header">
							{{ contentType == 'search' ? searchTerm : category?.seo_h1 }}
						</div>
						<template v-if="items?.length && !loading">
						<BaseUiAccordion class="accordion">
							<BaseUiAccordionPanel v-for="(item, index) in items" :key="item.id" :active="index == 0" :id="item.id" v-slot="{onToggle, active}">
								<div class="accordion-item" :class="{'active': active}">
									<div class="fp-title" @click="onToggle">{{ item.title }}<span class="toggle-icon" :class="{'active': active}"></span></div>
									<div class="fp-cnt cms-content" v-html="item.content" v-interpolation />
								</div>
							</BaseUiAccordionPanel>
						</BaseUiAccordion>
						</template>
						<div v-else-if="loading" class="faq-questions-loading">
							<UiLoader />
						</div>
						<div v-else class="faq-no-results">
							<BaseCmsLabel code="no_faq" />
						</div>
					</div>

				</BaseFaqQuestions>
			</div>
		</BaseFaqCategory>
	</BaseCmsPage>
</template>

<script setup>
	const {scrollTo} = useDom();
	function scrollToQuestions() {
		setTimeout(() => {
			scrollTo('#questions', {offset: 100});
		}, 1000);
	}	
</script>

<style lang="less" scoped>
	.faq-header{
		text-align: center; padding: 20px 0 75px; max-width: 730px; margin: auto; font-size: 28px;
		@media (max-width: @m){text-align: left; padding: 20px 0 25px;}
	}
	h1{
		font-size: clamp(30px, 4vw, 42px); font-weight: bold; padding: 0 0 15px;
		@media (max-width: @m){max-width: 300px;}
	}
	.cnt{
		font-size: clamp(12px, 2vw, 28px);
		@media (max-width: @m){
			color: var(--gray5);
			:deep(p){padding: 0 0 5px;}
		}
	}
	.faq-categories{
		display: grid; grid-template-columns: repeat(auto-fill, minmax(30%, 1fr)); gap: var(--elementGap); padding-bottom: 75px;
		@media (max-width: @ms){
			grid-template-columns: 100%; margin: 0 calc(var(--wrapperMargin) * -1); gap: 15px; padding-bottom: 15px;
		}	
	}

	.faq-form{
		position: relative; margin: 10px 0 0;
		input{height: 44px; border: 0; border-radius: 100px; padding: 0 80px 0 25px; font-size: 15px;}
		button{
			position: absolute; top: 0; right: 0; height: 100%; width: 70px; background: none; padding: 0; margin: 0; font-size: 0; display: flex; align-items: center; justify-content: center; gap: 0;
			&:before{.icon-search(); font: 20px/1 var(--fonti); color: var(--gray5);}
		}
	}

	.faq-questions{
		width: 950px; margin: auto; background: #fff; margin-bottom: 60px; border-radius: var(--borderRadius);
		@media (max-width: @t){width: auto;}
		@media (max-width: @m){margin: 0 calc(var(--wrapperMargin) * -1) 30px; border-radius: 0;}
	}
	.faq-questions-header{padding: 20px 30px 20px; text-align: center; font-size: 28px; line-height: 1.3; font-weight: bold; border-bottom: 1px solid var(--gray2);}
	.accordion-item{
		border-bottom: 1px solid var(--gray2);
		&:last-child{border-bottom: 0;}
		&.active{
			.fp-cnt{display: block;}
		}
	}
	.fp-title{
		font-size: 18px; font-weight: bold; cursor: pointer; padding: 25px; display: flex; justify-content: space-between; gap: 20px;
		.toggle-icon{margin-top: 6px;}
	}
	.fp-cnt{
		display: none; padding: 0 25px 25px;
		:deep(*:last-child){padding-bottom: 0;}
	}
	.faq-questions-loading{display: flex; justify-content: center; padding: 20px;}
	.faq-no-results{text-align: center; padding: 20px;}
</style>
