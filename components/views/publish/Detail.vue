<template>
	<BasePublishDetail v-slot="{item}">
		<div class="wrapper">
			<CmsBreadcrumbs :items="item.breadcrumbs" />

			<CmsCardLarge>
				<template #header>
					<BaseUiImage :data="item.main_image_thumbs?.['width1440-height530-crop1']" default="/images/no-image-1440.jpg" />
				</template>
				<template #content>
					<div class="info" v-if="item.datetime_published">
						<span class="item date"><BaseUtilsFormatDate :date="item.datetime_published" format="DD MMM, YYYY" /></span>
					</div>
					<h1>{{ item.seo_h1 }}</h1>
					<div class="pd-short-desc intro-text" v-if="item.short_description" v-html="item.short_description" />
				</template>
			</CmsCardLarge>

			<div class="wrapper2" v-if="item.content">
				<CmsContent>
					<div class="cms-content" v-html="item.content" v-interpolation />
					<ul class="pd-documents" v-if="item.documents?.length">
						<li v-for="document in item.documents" :key="document.id">
							<a target="_blank" :href="document.url" :title="document.description">{{ document.title }}</a>
						</li>
					</ul>
				</CmsContent>
			</div>
		</div>
	</BasePublishDetail>
</template>

<style lang="less" scoped>
	.info{
		display: flex; justify-content: center; gap: 20px; font-size: 12px; color: var(--gray5); padding: 0 0 20px;
		@media (max-width: @m){justify-content: left;}
		.item{
			position: relative; display: flex; align-items: center; gap: 5px;
			&:before{.icon-date(); font: 15px/1 var(--fonti); margin-top: -1px;}
		}
		.date{text-transform: capitalize;}
	}	
	:deep(.card){
		.content{
			max-width: 700px; margin: auto;
			@media (max-width: @m){max-width: none;}
		}
	}
</style>
