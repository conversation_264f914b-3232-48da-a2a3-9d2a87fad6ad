<template>
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<div class="wrapper page-content">
			<CmsBreadcrumbs :items="item.breadcrumbs" />
			<CmsCardLarge class="narrow-content">
				<template #header>
					<BaseUiImage v-if="item.main_image_upload_path" :data="item.main_image_thumbs?.['width1440-height530-crop1']" default="/images/no-image.jpg" />
				</template>
				<template #content>
					<h1>{{ item.seo_h1 }}</h1>
					<div v-if="item.short_description" v-html="item.short_description" v-interpolation />
				</template>
			</CmsCardLarge>

			<div class="wrapper2">
				<CmsContent v-if="item.content || item?.images?.length" :item="item">
					<div v-if="item.content" v-html="item.content" />
					<ClientOnly>
						<BaseUiImages v-if="item?.images?.length" :images="item.images" v-slot="{items: images}" thumb-preset="publishDetailExtra">
							<div class="pd-thumbs" v-if="images">
								<a v-for="file in images.slice(1)" :key="file.id" rel="gallery" :title="file.title">
									<BaseUiImage loading="lazy" :data="file.url_thumb" :alt="file.description" />
								</a>
							</div>
						</BaseUiImages>
					</ClientOnly>
				</CmsContent>

				<BasePublishCategory :fetch="{code: ['services']}" :root-category="true" v-slot="{item: category, rootCategory}" :include-subcategories="true">
					<div class="services-title"><BaseCmsLabel code="all_services" /></div>
					<PublishServicesAccordion :category="rootCategory" />
				</BasePublishCategory>
			</div>
			<LazyCmsLocations hydrate-on-visible />
		</div>
	</BasePublishDetail>
</template>

<style lang="less" scoped>
	.services-title{
		font-size: clamp(24px, 4vw, 28px); font-weight: bold; padding: 25px; text-align: center;
		@media (max-width: @m){
			padding: 20px 0 10px; text-align: left;
		}
	}
</style>
