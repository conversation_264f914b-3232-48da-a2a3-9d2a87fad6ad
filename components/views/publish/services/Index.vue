<template>
	<BasePublishCategory v-slot="{item: category, rootCategory}" :root-category="true" :include-subcategories="true" :seo="true">
		<div class="wrapper page-content">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />

			<CmsCardLarge class="narrow-content">
				<template #header>
					<BaseUiImage v-if="category.main_image_upload_path" :data="category.main_image_thumbs?.['width1440-height530-crop1']" default="/images/no-image.jpg" />
				</template>
				<template #content>
					<h1>{{ category.seo_h1 }}</h1>
					<div v-if="category.short_description" v-html="category.short_description" v-interpolation />
				</template>
			</CmsCardLarge>

			<template v-if="category.level == 2">
				<div class="wrapper2">
					<CmsContent v-if="category.content">
						<div v-html="category.content" />
					</CmsContent>

					<!-- Subcategories -->
					<BasePublishPostsWidget :fetch="{category_code_only: [category.code], response_fields: ['id', 'title', 'url_without_domain', 'main_image_thumbs']}" v-slot="{items}">
						<div class="special-services-wrapper" v-if="items?.length">
							<div class="services-title"><BaseCmsLabel code="special_services" /></div>
							<div class="special-services">
								<NuxtLink :to="post.url_without_domain" class="service-item"  v-for="post in items" :key="post.id">
									<div class="service-item-title">
										{{ post.title }}
									</div>
									<BaseUiImage loading="lazy" :data="post.main_image_thumbs?.['width480-height220-crop1']" default="/images/no-image.jpg" />
								</NuxtLink>
							</div>
						</div>
					</BasePublishPostsWidget>
				</div>
			</template>

			
			<!-- Show service categories as cards on root level or as accordion if level is 2 -->
			<template v-if="rootCategory?.children?.length">
				<template v-if="category.level == 1">
					<div class="cards-wrapper services">
						<CmsCard v-for="subcategory in rootCategory.children" :key="subcategory.id">
							<template #image>
								<BaseUiImage loading="lazy" :data="subcategory.main_image_thumbs?.['width500-height260-crop1']" default="/images/no-image.jpg" />
							</template>
							<template #content>
								<div class="title"><NuxtLink :to="subcategory.url_without_domain">{{ subcategory.seo_h1 }}</NuxtLink></div>
								<div v-if="subcategory.short_description" v-html="subcategory.short_description" v-interpolation />
								<BasePublishPostsWidget :fetch="{category_code_only: [subcategory.code], response_fields: ['id', 'title', 'url_without_domain']}" v-slot="{items}">
									<ul v-if="items?.length">
										<li v-for="post in items" :key="post.id">
											<NuxtLink :to="post.url_without_domain">{{ post.title }}</NuxtLink>
										</li>
									</ul>
								</BasePublishPostsWidget>
							</template>
						</CmsCard>
					</div>
				</template>
				<template v-else>
					<div class="wrapper2">
						<div class="services-title"><BaseCmsLabel code="all_services" /></div>
						<PublishServicesAccordion :category="rootCategory" />
					</div>
				</template>
			</template>
			<LazyCmsLocations hydrate-on-visible />
		</div>
	</BasePublishCategory>
</template>

<style lang="less" scoped>
	.services{
		padding: 20px 0 0;
		@media (max-width: @m){padding: var(--wrapperMargin) 0 0;}
	}
	
	.wrapper2{width: 950px; margin: auto;}
	.services-title{
		font-size: clamp(24px, 4vw, 28px); font-weight: bold; padding: 25px; text-align: center;
		@media (max-width: @m){
			padding: 20px 0 10px; text-align: left;
		}
	}

	.special-services-wrapper{
		padding: 20px 0 40px 0;
		@media (max-width: @m){padding: 0;}
	}
	.special-services{
		display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--elementGap);
		@media (max-width: @m){grid-template-columns: repeat(1, 1fr);}
	}
	.service-item{
		border-radius: var(--borderRadius); overflow: hidden; position: relative; color: #fff; text-decoration: none;
		:deep(img){display: block; object-fit: cover; width: 100%; height: auto;}
	}
	.service-item-title{
		position: absolute; top: 25px; left: 25px; z-index: 10; font-size: 24px; font-weight: bold; width: 55%; text-wrap: balance;
		@media (max-width: @m){
			top: 15px; left: 20px; width: 65%;
		}
	}
</style>