<template>
	<BasePublishCategory v-slot="{item: category}" :seo="true">
		<div class="wrapper page-content">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
			<h1>{{ category.seo_h1 }}</h1>
			<BasePublishPosts v-slot="{items: posts, loading}">
				<div class="p-items" :class="{'items-loading': loading}" v-if="posts?.length" id="items">
					<PublishIndexEntry v-for="post in posts" :key="post.id" :item="post" :short-description="true" />
				</div>
				<div v-else class="p-empty"><BaseCmsLabel code="no_publish" /></div>
				<UiPagination class="p-pagination-items" scroll-to-element="items" :scroll-to-offset="150" />
			</BasePublishPosts>
		</div>
	</BasePublishCategory>
</template>

<style lang="less" scoped>
	.p-items{display: grid; grid-template-columns: repeat(auto-fill, minmax(330px, 1fr)); gap: var(--elementGap);}
	@media (max-width: @m){
		h1{display: none;}
	}
	@media (max-width: @ms){
		.p-items{grid-template-columns: 100%;}
	}
</style>
