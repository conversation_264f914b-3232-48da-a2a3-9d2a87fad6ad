<template>
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<div class="wrapper page-content">
			<CmsCardLarge :class="{'no-content': !item.short_description}">
				<template #header>
					<BaseUiImage :data="item.main_image_thumbs?.['width1480-height540-crop1']" default="/images/no-image-1440.jpg" :alt="item.title" />
				</template>
				<template #content>
					<div class="info">
						<NuxtLink class="item category" :to="item.category_url_without_domain" v-if="item.category_title">{{ item.category_title }}</NuxtLink>
						<div class="item date" v-if="item.datetime_published"><BaseUtilsFormatDate :date="item.datetime_published" format="DD MMM, YYYY" /></div>
					</div>
					<h1>{{ item.seo_h1 }}</h1>
					<div class="pd-short-desc intro-text" v-if="item.short_description" v-html="item.short_description" />
				</template>
			</CmsCardLarge>

			<LazyCmsCountdown :end="item.datetime_expire" v-if="item.datetime_expire" />
			<CmsBenefits class="no-spacing" />

			<div class="wrapper2" v-if="item.content" v-interpolation>
				<CmsContent class="no-spacing">
					<div v-html="item.content" />
					<ul class="documents" v-if="item.documents?.length">
						<li v-for="document in item.documents" :key="document.id">
							<a target="_blank" :href="document.url" :title="document.description">{{ document.title }}</a>
						</li>
					</ul>
				</CmsContent>
			</div>

			<BaseCatalogProductsWidget thumb-preset="catalogEntry" :fetch="{related_publish_id: item.id, only_available: true, limit: 200}" v-slot="{items}">
				<div v-if="items?.length" class="c-items">
					<CatalogIndexEntry v-for="item in items" :key="item.id" :item="item" />
				</div>
			</BaseCatalogProductsWidget>

			<LazyCmsLocations class="no-spacing" hydrate-on-visible />
		</div>
	</BasePublishDetail>
</template>

<style scoped lang="less">
	.page-content{
		padding-top: 25px; display: flex; flex-direction: column; gap: var(--elementGap);
		@media (max-width: @m){padding-top: 0;}
	}
	h1{font-size: clamp(24px, 3vw, 28px); font-weight: bold;}
	.info{
		display: flex; justify-content: center; gap: 20px; font-size: 12px; color: var(--gray5); padding: 0 0 20px;
		@media (max-width: @m){justify-content: left;}
		a{text-decoration: none; color: var(--gray5); position: relative;}
		.item{
			position: relative; display: flex; align-items: center; gap: 5px;
			&:before{.icon-date(); font: 15px/1 var(--fonti); margin-top: -1px;}
		}
		.date{text-transform: capitalize;}
		.category:before{.icon-layers();}
	}
	:deep(.card){
		.content{
			max-width: 700px; margin: auto;
			@media (max-width: @m){max-width: none;}
		}
	}
</style>