<template>
	<BasePublishCategory :root-category="true" v-slot="{item: category, rootCategory}" :include-subcategories="true" :seo="true">
		<div class="wrapper">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
		</div>
		<PublishPromotionCategories :category="rootCategory" />
		
		<div class="wrapper page-content">
			<BasePublishPostsWidget :fetch="{list_code: 'special_promotion', category_code: category.code, limit: 3, extra_fields: ['short_description'], response_fields: ['id','title','main_image_upload_path','main_image_thumbs','category_title','datetime_published','url_without_domain','short_description','external_url']}" thumb-preset="promotion" v-slot="{items}">
				<template v-if="items?.length">	
					<div class="title promo-special-title"><BaseCmsLabel code="special_promotions" /> </div>
					<div class="promo-special">
						<div class="promo-special-col promo-special-col1">
							<PublishPromotionIndexEntry v-if="items[0]" :key="items[0].id" :item="items[0]" class="promo-special-item promo-special-item-1" />
						</div>
						<div class="promo-special-col promo-special-col2" v-if="items?.length > 1">
							<PublishPromotionIndexEntry v-if="items[1]" :key="items[1].id" :item="items[1]" class="promo-special-item promo-special-item-2" />
							<PublishPromotionIndexEntry v-if="items[2]" :key="items[2].id" :item="items[2]" class="promo-special-item promo-special-item-3" />
						</div>
					</div>
				</template>
			</BasePublishPostsWidget>

			<!-- FIXME postaviti broj promocija po stranici -->
			<BasePublishPosts :fetch="{extra_fields: ['short_description'], response_fields: ['id','title','main_image_upload_path','main_image_thumbs','category_title','datetime_published','url_without_domain','short_description','external_url']}" thumb-preset="promotion" v-slot="{items, pagination, loading}">
				<template v-if="items?.length">
					<div class="header">
						<div class="title"><BaseCmsLabel code="other_promotions" /> </div>
						<div class="counter" v-if="pagination?.items?.total"><BaseCmsLabel code="active_promotions" :replace="[{'%COUNTER%': pagination.items.total}]" /></div>
					</div>
					<div class="items" :class="{'items-loading': loading}" id="items">
						<PublishPromotionIndexEntry v-for="post in items" :key="post.id" :item="post" />
					</div>
					<UiPagination class="promotions-pagination" scroll-to-element="items" :scroll-to-offset="150" />
				</template>
				<template v-else>
					<div class="p-empty"><BaseCmsLabel code="no_promotions" /></div>
				</template>
			</BasePublishPosts>

			<LazyCmsLocations class="promotions-locations" hydrate-on-visible />
		</div>
	</BasePublishCategory>
</template>

<style lang="less" scoped>
	.header{
		display: flex; justify-content: space-between; align-items: center; padding: 0 0 25px 0;
		@media (max-width: @m){padding: 0 0 15px;}
	}
	@media (max-width: @m){
		.counter{display: none;}
	}
	.title{font-size: clamp(24px, 3vw, 28px); font-weight: bold;}
	.promo-special{
		display: flex; gap: 25px; padding-bottom: 25px;
		@media (max-width: @m){flex-direction: column; gap: var(--wrapperMargin);}
	}
	.promo-special-col1{
		width: calc(50% - 10px); flex-grow: 0; flex-shrink: 0;
		@media (max-width: @m){width: auto;}
	}
	.promo-special-col2{
		display: flex; flex-direction: column; gap: 25px;
		@media (max-width: @m){gap: var(--wrapperMargin);}
	}
	.promo-special-title{
		padding: 0 0 20px;
		@media (max-width: @m){padding: 0 0 15px;}
	}
	.items{
		display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: clamp(15px, 2vw, 25px); margin-bottom: 25px;
		@media (max-width: @ms){grid-template-columns: auto;}
	}
	.promotions-pagination{margin: clamp(30px, 4vw, 50px) 0;}
	.p-empty{padding: 30px 0; text-align: center;}
</style>
