<template>
	<BaseCmsPage>
		<ClientOnly>
			<BaseWebshopCart v-slot="{cart, parcels}">
				<div class="wrapper">
					<WebshopCheckoutLayout>
						<template #col1>
							<WebshopSteps :step="1" class="completed" />
							<WebshopSelectedCustomer />
							
							<WebshopSteps class="active" :step="2" />
							<form @submit.prevent="onSubmit" v-if="cart">
								<div class="wc-form wc-form-shipping floating-labels">
									<div class="wc-form-col1">
										<!--
										<ClientOnly>
											<WebshopHelloBar />
										</ClientOnly>
										-->

										<div v-if="shippingLeanpayError" class="unavailable-item special">
											<div class="title"><BaseCmsLabel code="leanpay_shipping_note" tag="strong" /></div>
											<div class="btn-hidde" @click="hiddeErrorMessage()">
												<span><BaseCmsLabel code="hidde_error" /></span>
											</div>
										</div>

										<WebshopShippingMerge :data="cart" />
										<div class="wc-packages">
											<template v-if="parcels?.length">
												<template v-for="parcel in parcels" :key="parcel.number">
													<WebshopPackage :parcel="parcel" />
												</template>
											</template>
										</div>
									</div>
								</div>
							</form>
							<WebshopSteps :step="3" class="disabled" />
						</template>
						<template #col2>
							<WebshopCheckoutSidebar :forms-meta="meta" />
							<!--
							<WebshopWidgetFixedbar :meta="meta" customer="true" />
							-->
						</template>
					</WebshopCheckoutLayout>
				</div>
			</BaseWebshopCart>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	import {useForm} from 'vee-validate';

	const webshop = useWebshop();
	const auth = useAuth();
	const apiRoutes = useApiRoutes();
	const cartData = computed(() => webshop.getCartData());
	const { shippingLeanpayError, formValid, selectedShipping } = useCheckout();

	// watch if form is valid
	const { meta } = useForm();
	watch(
		() => meta,
		() => formValid.value = (meta.value.valid) ? 1 : 0,
		{deep: true}
	)

	// redirect to cart page if there are errors in cart
	if (cartData.value?.cart?.errors?.cart?.length) {
		setTimeout(async () => {
			await navigateTo(apiRoutes.getAppUrl('webshop_shopping_cart'));
		}, 1000);
	}

	// redirect to cart page if there are warnings in cart
	if (cartData.value?.cart?.warnings?.cart?.length) {
		setTimeout(async () => {
			await navigateTo(apiRoutes.getAppUrl('webshop_shopping_cart'));
		}, 1000);
	}

	//hidde error message
	function hiddeErrorMessage() {
		shippingLeanpayError.value = 0;
	};

	//on submit
	async function onSubmit() {
		await navigateTo(apiRoutes.getAppUrl('webshop_review_order'));
	}
</script>


<style lang="less" scoped>
	:deep(.wc-step){padding: 30px 0;}
	:deep(.wc-step1){padding-top: 0;}
	:deep(.wc-step3){padding-bottom: 0;}
	.wc-packages {
		flex-grow: 1; display: flex; flex-direction: column; gap: 18px;
		position: relative;
		&.special{
			margin-top: 25px; padding-top: 30px; border-top: 1px solid var(--borderColor);
			@media (max-width: @m){padding-top: 25px;}
		}
	}
</style>