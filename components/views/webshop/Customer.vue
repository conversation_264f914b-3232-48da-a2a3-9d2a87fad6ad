<template>
	<BaseCmsPage>
		<ClientOnly>
			<BaseWebshopCart v-slot="{cart}">
				<div class="wrapper">
					<BaseForm @submit="onSubmit" v-slot="{meta}" data-autofocus>
						<WebshopCheckoutLayout>
							<template #col1>
								<WebshopSteps class="active" :step="1" :active="true" />
								<WebshopCheckoutPanel :title="labels.get('bill_address_pickup')">
									<div class="wc-form wc-form-customer floating-labels-checkout" v-if="cart">
										<div class="wc-form-col1">
											<!--
											FIXME: Hello bar
											<ClientOnly>
												<WebshopHelloBar />
											</ClientOnly>
											-->
											<BaseAuthUser v-slot="{isLoggedIn, user, urls}">
												<div v-if="paymentError" class="global-warning unavailable-item special">
													<div class="title">
														<template v-if="user?.user_id"><BaseCmsLabel code="error_login_use_missing_data" tag="strong" /></template>
														<template v-else>
															<template v-if="paymentError?.length">
																<template v-if="paymentError[0]?.error == 'error_address_must_contain_number' && paymentError[0]?.user_logged_in">
																	<NuxtLink :to="urls.auth_edit"><BaseCmsLabel :code="paymentError[0].error" tag="strong" /></NuxtLink>
																</template>
																<BaseCmsLabel v-else :code="paymentError[0]?.error" tag="strong" />
															</template>
														</template>
													</div>
													<div class="btn-hidde" @click="hiddeErrorMessage()">
														<BaseCmsLabel code="hidde_error" tag="span" />
													</div>
												</div>

												<template v-if="user?.user_id && userError?.length">
													<div class="global-warning unavailable-item special" v-for="error in userError" :key="error.label_name">
														<div class="title"><BaseCmsLabel :code="error.label_name" tag="strong" /></div>
													</div>
												</template>

												<template v-if="cart.warnings?.cart">
													<div v-for="warning in cart.warnings.cart" :key="warning.label_name" class="global-warning unavailable-item special">
														<div class="title">
															<BaseCmsLabel :code="warning.label_name" tag="strong" />
														</div>
														<NuxtLink class="btns" :to="urls.webshop_shopping_cart"><BaseCmsLabel code="edit" /></NuxtLink>
													</div>
												</template>
												<template v-if="cart.errors?.cart">
													<div v-for="error in cart.warnings.errors" :key="error.label_name" class="global-warning unavailable-item special">
														<div class="title"><BaseCmsLabel :code="error.label_name" /></div>
														<NuxtLink class="btns" :to="urls.webshop_shopping_cart"><BaseCmsLabel code="edit" /></NuxtLink>
													</div>
												</template>
												
												<div v-if="isLoggedIn" class="wc-user-info">
													<div class="wc-user-info-item wc-user-info-item-email" v-if="user?.email">
														{{ user.email }}
													</div>
													<div v-if="user?.first_name" class="wc-user-info-item">
														{{ user.first_name }}
														<template v-if="user?.last_name"> {{ user.last_name }}</template>
													</div>
													<div v-if="user?.address" class="wc-user-info-item">
														{{ user.address }}
														<template v-if="user?.zipcode"><br />{{ user.zipcode }}</template>
														<template v-if="user?.city"><br />{{ user.city }}</template>
													</div>
													<div v-if="user?.phone" class="wc-user-info-item">{{ user.phone }}</div>
													<NuxtLink :to="urls.auth_edit" class="wc-edit-profile"><BaseCmsLabel code="checkout_edit_profile" /></NuxtLink>
												</div>

												<div class="wc-fields wc-customer-fields">
													<template v-for="field in customerFields" :key="field.name">
														<BaseFormField v-if="field.name != 'f_another_country' && field.type == 'checkbox' && field.opens_fields" :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
															<div class="box-field" :class="['box-field-'+field.name, {'active': field.name == 'b_same_as_shipping' ? !value : value}]">
																<div class="checkbox-field" :class="['input-field-'+field.name, {'field-error': errorMessage && isTouched, 'field-success': !errorMessage && value, 'floated-label': floatingLabel}]">
																	<BaseFormInput />
																	<label @click="clearExtraBPhoneValue(field)" :for="field.name"><BaseCmsLabel :code="field.name" /></label>
																</div>

																<template v-if="field.opens_fields[0]">
																	<div class="subfields">
																		<div class="wc-fields" :class="'wc-fields-' + field.name">
																			<template v-for="subfield in customerFields" :key="subfield.name">
																				<BaseFormField :item="subfield" v-slot="{errorMessage, value, isTouched, floatingLabel}">
																					<FormCompanyOibField v-if="subfield.related_field == field.name && subfield.name == 'b_company_oib'" :field="subfield" :fields="customerFields" />
																					<div
																						v-else-if="subfield.related_field == field.name && subfield.type != 'checkbox'"
																						class="input-field field"
																						:class="['input-field-'+subfield.name, {'field-error': errorMessage && isTouched, 'field-success': !errorMessage && value, 'floated-label': floatingLabel || subfield?.name == 'b_phone'}]">
																						<template v-if="subfield?.name == 'b_phone' && subfield?.options_prefix">
																							<WebshopPhoneCountry :field="subfield" :input="extraBPhoneValue" @phoneValueInput="phoneBValueInput" @selectCountryValue="selectBCountryValue" />
																							<BaseFormInput type="hidden" :value="subfield.value" :placeholder="labels.get(subfield.name)" />
																						</template>
																						<template v-else>
																							<BaseFormInput :placeholder="labels.get(subfield.name)" />
																						</template>
																						<span class="error" v-show="errorMessage" v-html="errorMessage" />
																					</div>
																				</BaseFormField>
																			</template>
																		</div>
																	</div>
																</template>
															</div>
														</BaseFormField>
														<template v-if="!user">
															<BaseFormField v-if="field?.name == 'f_another_country' && field?.type == 'checkbox' && field?.opens_fields" :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
																<div class="box-field box-another-country" :class="{'active': field.name == 'f_another_country' ? value : !value}">
																	<div class="input-field-another-country" :class="['input-field-'+field.name, {'field-error': errorMessage && isTouched, 'field-success': !errorMessage && value, 'floated-label': floatingLabel}]">
																		<BaseFormInput :placeholder="labels.get(field.name)" />
																		<label @click="anotherCountryToggle(!value)" class="label" :class="{'active': value}" :for="field.name">
																			<BaseCmsLabel :code='field.name + "_unactive"' />
																		</label>
																		<div v-if="value" class="global-warning warning"><BaseCmsLabel code="f_another_country_note" /></div>
																	</div>
																	<template v-if="field?.opens_fields[0]">
																		<div class="subfields-another-country">
																			<template v-for="subfield in customerFields" :key="subfield.name">
																				<BaseFormField :item="subfield" v-slot="{errorMessage, value, isTouched, floatingLabel}">
																					<div
																						v-if="subfield.related_field == field.name && subfield.type != 'checkbox'"
																						class="input-field field"
																						:class="['input-field-'+subfield.name, {'field-error': errorMessage && isTouched, 'field-value': value, 'field-success': !errorMessage && value, 'floated-label': floatingLabel || subfield?.name == 'b_phone'}]">
																						<BaseFormInput :placeholder="labels.get(subfield.name)" />
																						<span class="error" v-show="errorMessage" v-html="errorMessage" />
																					</div>
																				</BaseFormField>
																			</template>
																		</div>
																	</template>
																</div>
															</BaseFormField>
															<BaseFormField v-if="!field.opens_fields && !field.related_field" :item="field" v-slot="{errorMessage, value, isTouched, floatingLabel}">
																<div
																	class="input-field field"
																	:class="['input-field-'+field.name, {'disabled-another-country': field.name == 'address' ? anotherCountry : false || field.name == 'location' ? anotherCountry : false, 'field-error': errorMessage && isTouched, 'field-success': !errorMessage && value, 'floated-label': floatingLabel || field?.name == 'phone'}]">
																	<template v-if="field?.name == 'phone' && field?.options_prefix">
																		<WebshopPhoneCountry :field="field" @phoneValueInput="phoneValueInput" @selectCountryValue="selectCountryValue" />
																		<BaseFormInput type="hidden" :value="field.value" :placeholder="labels.get(field.name)" />
																	</template>
																	<template v-else-if="anotherCountry == true && (field?.name == 'city' || field?.name == 'zipcode' || field?.name == 'address' || field?.name == 'location')">
																		<div class="input-field-another-country-disabled"></div>
																	</template>
																	<template v-else>
																		<BaseFormInput :placeholder="labels.get(field.name)" />
																	</template>
																	<span class="error" v-show="errorMessage" v-html="errorMessage" />
																</div>
															</BaseFormField>
														</template>
													</template>
												</div>
											</BaseAuthUser>
										</div>
									</div>
								</WebshopCheckoutPanel>
								<div class="btn-continue-container">
									<button type="submit" class="btn btn-continue" :class="{'btn-disabled': !meta?.valid || !formValid || cart?.warnings?.cart?.length}">
										<UiLoader class="dots" v-if="checkoutLoading || creation" />
										<BaseCmsLabel v-else code="goto_step2_button" />
									</button>
								</div>

								<WebshopSteps :step="2" :disabled="true" />
								<WebshopSteps :step="3" :disabled="true" />
							</template>
							<template #col2>
								<WebshopCheckoutSidebar :forms-meta="meta" />
								<!--
								<WebshopWidgetFixedbar :meta="meta" customer="true" />
								-->
							</template>
						</WebshopCheckoutLayout>
					</BaseForm>
				</div>
			</BaseWebshopCart>
			<template #fallback>
				<UiLoader class="spacing" />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const endpoints = useEndpoints();
	const webshop = useWebshop();
	const apiRoutes = useApiRoutes();
	const auth = useAuth();
	const cartData = computed(() => webshop.getCartData());
	const dom = useDom();
	const apiErrors = ref([]);
	const { customerFields, formSubmitCount, paymentError, checkoutLoading, formValid, creation } = useCheckout();
	let userError = ref([]);
	const isLoggedIn = computed(() => auth.isLoggedIn());
	const labels = useLabels();

	// fetch form fields
	let focusTimeout = null;
	let cartErrorTimeout = null;
	let cartWarningTimeout = null;
	let submitTimeout = null;
	onMounted(async() => {
		await nextTick();

		if(!customerFields.value?.length) {
			await useApi(`${endpoints.get('_get_hapi_customer_fields')}?type=webshop.customer&change_fields=first_name:max_length=20,last_name:max_length=30,b_first_name:max_length=20,b_last_name:max_length=30,phone:type=tel,b_phone:type=tel,b_company_oib:type=tel`)
			.then(res => {
				const fields = res.data?.length ? res.data : [];
				if(fields.length) {
					customerFields.value = fields.map(field => {
						if (field.name == 'b_location') {
							field.related_field = 'b_same_as_shipping';
						}
						return field;
					});
				}
			});
		}

		const fAnotherCountryValue = (customerFields.value?.find(obj => obj.name === "f_another_country") || {}).value;
		if (fAnotherCountryValue) {
			anotherCountry.value = true;
		}

		//user phone error
		if (isLoggedIn.value && customerFields.value?.length) {
			customerFields.value.forEach(el => {
				if (el.errors != null) {
					el.errors.forEach(el2 => {
						if (el2 != null) {
							userError.value.push({'label_name': el2.label_name});
						}
					});
				}
			});
		}

		// redirect to cart page if there are errors in cart
		if (cartData.cart?.errors?.cart?.length) {
			cartErrorTimeout = setTimeout(async () => {
				await navigateTo(apiRoutes.getAppUrl('webshop_shopping_cart'));
			}, 1000);
		}

		// redirect to cart page if there are warnings in cart
		if (cartData.cart?.warnings?.cart?.length) {
			cartWarningTimeout = setTimeout(async () => {
				await navigateTo(apiRoutes.getAppUrl('webshop_shopping_cart'));
			}, 1000);
		}

		// set focus on first field in form. "data-autofocus" attribute needs to be set on form element
		focusTimeout = setTimeout(() => {
			dom.setFieldFocus();
		}, 500);

		if (userError.value.length) {
			formValid.value = 0;
		}
	});

	//phone value
	const phoneValue = ref(null);
	function phoneValueInput(combinedValue) {
		const phoneFieldIndex = customerFields.value.findIndex(field => field.name === 'phone');
		if (phoneFieldIndex !== -1) {
			customerFields.value[phoneFieldIndex].value = combinedValue;
			phoneValue.value = combinedValue
		}
	}
	function phoneBValueInput(combinedValue) {
		const phoneBFieldIndex = customerFields.value.findIndex(field => field.name === 'b_phone');
		if (phoneBFieldIndex !== -1) {
			customerFields.value[phoneBFieldIndex].value = combinedValue;
		}
	}
	function selectCountryValue(combinedValue) {
		const phoneFieldSelectIndex = customerFields.value.findIndex(field => field.name === 'phone');
		if (phoneFieldSelectIndex !== -1) {
			customerFields.value[phoneFieldSelectIndex].value = combinedValue;
		}
	}
	function selectBCountryValue(combinedValue) {
		const phoneFieldBSelectIndex = customerFields.value.findIndex(field => field.name === 'b_phone');
		if (phoneFieldBSelectIndex !== -1) {
			customerFields.value[phoneFieldBSelectIndex].value = combinedValue;
		}
	}

	//clear extra b_phone value
	const extraBPhoneValue = ref(false);
	function clearExtraBPhoneValue(item) {
		if(item?.name == 'b_same_as_shipping') {
			extraBPhoneValue.value = true;
		}
	}

	//another country active
	const anotherCountry = ref(false);
	function anotherCountryToggle(value) {
		anotherCountry.value = value;
	}

	//on submit
	async function onSubmit({values, actions}) {
		if(submitTimeout) clearTimeout(submitTimeout);
		checkoutLoading.value = true;
		let userData = (isLoggedIn.value) ? {} : values;

		if(isLoggedIn.value) {
			if(customerFields.value?.length){
				customerFields.value.forEach(el => {
					userData[el.name] = el.value;
				});

				for (const key in values) {
					const field = customerFields.value.find((field) => field.name === key);
					if (field && field.value !== values[key]) {
						userData[key] = values[key];
					}
				}
			}

			if (userData?.b_location && userData?.b_location?.trim() !== "") {
				const parts = userData.b_location.split(' ');
				userData.b_city = parts.slice(1).join(' ');
			}
		}

		if (!isLoggedIn.value && userData.location && userData.location.trim() !== "") {
			const parts = userData.location.split(' ');
			userData.city = parts.slice(1).join(' ');
		}

		if (!isLoggedIn.value && userData.b_location && userData.b_location.trim() !== "") {
			const parts = userData.b_location.split(' ');
			userData.b_city = parts.slice(1).join(' ');
		}

		if (!isLoggedIn.value && userData.f_another_country ) {
			userData.location = '';
			userData.city = '';
			userData.zipcode = '';
			userData.address = '';
			userData.f_location = userData.f_zipcode + ' ' + userData.f_city;
		}

		userData['_change_fields'] = 'first_name:max_length=20,last_name:max_length=30,b_first_name:max_length=20,b_last_name:max_length=30,phone:type=tel,b_phone:type=tel,b_company_oib:type=tel';

		await webshop.submitCustomerData(userData)
		.then(async res => {
			submitTimeout = setTimeout(async () => {
				if (res.success) {
					await webshop.setParcelDefaultShipping().then(async res => {
						await webshop.fetchCart();
						navigateTo(apiRoutes.getAppUrl('webshop_shipping'));
					});

					await useApi(`${endpoints.get('_get_hapi_customer_fields')}?type=webshop.customer&change_fields=first_name:max_length=20,last_name:max_length=30,b_first_name:max_length=20,b_last_name:max_length=30,phone:type=tel,b_phone:type=tel,b_company_oib:type=tel`)
					.then(res => {
						const fields = res.data?.length ? res.data : [];
						if(fields.length) {
							customerFields.value = fields.map(field => {
								if (field.name == 'b_location') {
									field.related_field = 'b_same_as_shipping';
								}
								return field;
							});

							// Reorder the array based on the specified condition
							const locationIndex = customerFields.value.findIndex(field => field.name === 'location');
							const countryIndex = customerFields.value.findIndex(field => field.name === 'f_another_country');

							if (locationIndex !== -1 && countryIndex !== -1) {
								const countryField = customerFields.value.splice(countryIndex, 1)[0];
								// Insert 'f_country' after 'b_location'
								customerFields.value.splice(locationIndex + 1, 0, countryField);
							}
						}
					});
					checkoutLoading.value = false;
				} else {
					paymentError.value = res.data?.errors?.length ? res.data.errors : null;
					checkoutLoading.value = false;
				}
			}, 500);
		});
	};

	//hidde error message
	function hiddeErrorMessage() {
		paymentError.value = null;
	}

	onBeforeUnmount(() => {
		clearTimeout(cartErrorTimeout);
		clearTimeout(cartWarningTimeout);
		clearTimeout(focusTimeout);
		clearTimeout(submitTimeout);
	});
</script>

<style lang="less" scoped>
	:deep(.panel-title){font-size: 15px;}
	.wc-fields{display: flex; gap: 12px; flex-wrap: wrap;}
	.wc-fields-b_r1{display: block;}
	:deep(.wc-step1){padding-bottom: 30px;
		@media (max-width: @m){padding-bottom: 20px;}
	}
	.field{
		position: relative; width: calc(~"50% - 6px");
		@media (max-width: @t){width: 100%;}
	}
	:deep(.location-autocomplete-container){
		display: block; width: 100%; padding: 10px; border: none!important; border-radius: 8px; box-shadow: 0px 8px 16px 0px #0000001F; font-size: 14px; position: absolute; top: 52px!important; z-index: 11111!important;
		ul{
			max-height: 165px;
			&::-webkit-scrollbar {-webkit-appearance: none; width: 4px; background: var(--gray3); border-radius: 100px;}
			&::-webkit-scrollbar-thumb {border-radius: 0; background-color: var(--blue); border-radius: 100px;}
			&::-webkit-scrollbar-track {border-radius: 100px;}
		}
		li{
			padding: 6px 10px!important; cursor: pointer; color: var(--gray5); border-radius: 2px;
			&:hover, &.active{background: var(--gray3); color: var(--textColor);}
		}
	}

	.input-field-zipcode, .input-field-city, .input-field-b_zipcode, .input-field-b_city{display: none!important;}
	.wc-user-info{padding: 0 0 20px; color: var(--gray7); font-size: 15px;}
	.wc-user-info-title{color: var(--textColor); padding: 0 0 10px;}
	.wc-user-info-item-email{padding-bottom: 35px; color: var(--textColor);}
	.wc-edit-profile{
		font-size: 15px; position: relative; margin-top: 35px; display: inline-block;
		&:before{.pseudo(1px,13px); background: var(--borderColor); top: 3px; left: 0;}
		&:hover{color: var(--blue); text-decoration: none;}
	}

	.box-another-country.active{
		.subfields-another-country{display: flex;}
		.input-field-another-country{margin-bottom: 20px; text-align: left;}
	}
	.subfields-another-country{display: none; justify-content: space-between; flex-wrap: wrap; gap: 12px;}
	.input-field-another-country-disabled{display: flex; align-items: center; justify-content: center; height: 48px; padding: 0 20px; background: var(--gray3); pointer-events: none; border-radius: 8px;}
	.input-field-another-country{
		.label-special{display: block; flex-grow: 1; margin-top: 8px; text-align: right; color: #7891A9; text-decoration: none; cursor: default;}
		.warning{margin: 15px 0 0;}
	}

	.disabled-another-country{
		pointer-events: none;
		&:before{content: none!important;}
		input{font-size: 0; background: rgba(215,228,239,0.25);}
		label{font-size: 16px!important; top: 16px!important;}
	}

	.box-field{
		width: 100%;
		input[type=checkbox] + label{font-size: 12px; color: var(--textColor); padding-top: 3px;}
		&.active{
			.subfields{display: block; padding: 12px 0;}
		}
		.input-field-b_same_as_shipping{
			input[type=checkbox]:checked + label:before{background: #fff; border-color: var(--textColor); color: #fff;}
			input[type=checkbox] + label:before{border-color: var(--textColor); background: var(--textColor);}
		}
	}
	.box-field-b_same_as_shipping{margin-top: 10px;}
	.box-field-b_r1{order: 9;}
	.box-another-country{order: 10;}
	.subfields{display: none;}
	.btn-continue-container{
		padding-top: 24px; padding-bottom: 10px; text-align: right;
		@media (max-width: @m){padding-bottom: 15px;}
	}
	.btn-continue{
		min-width: 300px;
		@media (max-width: @m){width: 100%;}
	}
</style>
