<template>
	<div>
		<Body class="base-view-order" />
		<Title v-if="orderNumber()">{{ labels.get('order', 'Narudžba') }}: {{ orderNumber() }}</Title>
		<Teleport to="body">
			<div v-if="loading" class="loading">
				<BaseThemeUiLoading />
			</div>
			<template v-else>
				<template v-if="order?.label_name == 'error_invalid_code'">
					<p>Šifra narudžbe nije ispravna</p>
				</template>
				<template v-else>
					<div class="base-order-wrapper">
						<div class="base-order-header">
							<div class="base-order-header-col1">
								<div class="base-order-title" v-if="orderNumber()"><BaseCmsLabel code="order" default="Narudžba" />: {{ orderNumber() }}</div>
								<div v-if="order?.cart?.order_id">ID: {{ order.cart?.order_id }}</div>
								<div v-if="order?.cart?.internal_number"><BaseCmsLabel code="order_internal_number" default="Br. narudžbe kupca" />: {{ order.cart?.internal_number }}</div>
								<div v-if="order?.cart?.date_created"><BaseCmsLabel code="date" default="Datum" />: {{ order.cart?.date_created }}</div>
								<div class="base-order-status" v-if="order?.cart?.status">Status: {{ order.cart?.status?.title || order.cart?.status }}</div>
							</div>
							<div class="base-order-header-col2">
								<BaseCmsLabel code="order_logo" tag="div" default="" />
							</div>
						</div>

						<div class="base-order-customer">
							<div class="base-order-customer-col base-order-customer-col1">
								<div>
									<strong><BaseCmsLabel code="shipping_details" default="Podaci za dostavu" /></strong>
								</div>
								<div>
									<template v-if="order?.customer?.first_name">{{ order.customer.first_name }}</template> <template v-if="order?.customer?.last_name">{{ order.customer.last_name }}</template>
								</div>
								<div v-if="order?.customer?.address">{{ order.customer.address.street }}, {{ order.customer.address.zipcode }} {{ order.customer.address.city }}</div>
								<div v-if="order?.customer?.address?.country_name">{{ order.customer.address.country_name }}</div>
								<div v-if="order?.customer?.phone">T: {{ order.customer.phone }}</div>
								<div v-if="order?.customer?.email">E: {{ order.customer.email }}</div>

								<!-- IF user has loyalty card and barcode is set -->
								<template v-if="order?.cart?.loyalty?.active && order?.loyalty_barcode">
									<div class="base-order-loyalty-barcode">
										<br />
										<BaseCmsLabel code="loyalty_card" tag="div" />
										<img :src="order.loyalty_barcode" alt="Loyalty barcode" width="200" />
										<div>{{ order.cart.loyalty.code }}</div>
									</div>
								</template>
							</div>
							<div class="base-order-customer-col base-order-customer-col2" v-if="order?.customer?.b_company_oib">
								<div>
									<strong><BaseCmsLabel code="company_information" default="Podaci o tvrtki" /></strong>
								</div>
								<div v-if="order?.customer?.b_company_name">{{ order.customer.b_company_name }}</div>
								<div v-if="order?.customer?.b_company_oib">{{ order.customer.b_company_oib }}</div>
								<div v-if="order?.customer?.b_company_address">{{ order.customer.b_company_address }}</div>
								<div v-if="order?.customer?.b_company_city">
									<span v-if="order?.customer?.b_company_zipcode">{{ order.customer.b_company_zipcode }}</span>
									{{ order.customer.b_company_city }}
								</div>
							</div>
							<div class="base-order-customer-col base-order-customer-col3" v-if="order?.customer?.b_first_name">
								<div>
									<strong><BaseCmsLabel code="bill_address" default="Adresa za slanje računa" /></strong>
								</div>
								<div>
									<template v-if="order?.customer?.b_first_name">{{ order.customer.b_first_name }}</template> <template v-if="order?.customer?.b_last_name">{{ order.customer.b_last_name }}</template>
								</div>
								<div v-if="order?.customer?.b_address">{{ order.customer.b_address.b_street }}, {{ order.customer.b_address.b_zipcode }} {{ order.customer.b_address.b_city }}</div>
								<div v-if="order?.customer?.b_address?.b_country_name">{{ order.customer.b_address.b_country_name }}</div>
								<div v-if="order?.customer?.b_phone">T: {{ order.customer.b_phone }}</div>
								<div v-if="order?.customer?.b_email">E: {{ order.customer.b_email }}</div>
							</div>
						</div>

						<template v-if="order?.parcels?.length">
							<template v-for="parcel in order.parcels" :key="parcel.number">
								<div class="base-order-table">
									<div class="base-order-table-row base-order-table-head-row">
										<div class="base-order-table-col base-order-table-col-no"></div>
										<div class="base-order-table-col base-order-table-col-image"></div>
										<div class="base-order-table-col base-order-table-col-title"><BaseCmsLabel code="product" default="Proizvod" /></div>
										<div class="base-order-table-col base-order-table-col-price" v-if="!order?.customer?.user_disabled_price"><BaseCmsLabel code="price" default="Cijena" /></div>
										<div class="base-order-table-col base-order-table-col-qty"><BaseCmsLabel code="quantity" default="Količina" /></div>
										<div class="base-order-table-col base-order-table-col-total" v-if="!order?.customer?.user_disabled_price"><BaseCmsLabel code="total" default="Ukupno" /></div>
									</div>
									<div class="base-order-table-row" v-for="(item, index) in parcel.items" :key="item.id">
										<div class="base-order-table-col base-order-table-col-no">{{ index + 1 }}</div>
										<div class="base-order-table-col base-order-table-col-image">
											<BaseUiImage :src="item.item.image" width="150" v-if="item.item.image" />
											<svg style="width: 60px" v-else version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 256 256" enable-background="new 0 0 256 256" xml:space="preserve">
												<path
													fill="#cccccc"
													d="M142.2,169.7l30.5-30.5l10.4,10.4c2.5-1.5,5.2-2.7,8-3.7l-17.4-17.4l-1,1l-1-1l-11.4,11.4l-52.5-52.5l-1,1l-1-1l-71.7,71.7l5.8,5.8l66.8-66.8l47.7,47.7l-18.2,18.2L142.2,169.7z M18.4,201.7V37.2h197.6v98.7h8.4V28.8H10v180.8h160.5c-1.9,0-3.5,0-4.8-7.9L18.4,201.7L18.4,201.7z M181.6,58.8h-16.1v16.3h16.1V58.8z M204.8,144.9c-22.7,0-41.2,18.4-41.2,41.2c0,22.7,18.4,41.2,41.2,41.2c22.7,0,41.2-18.4,41.2-41.2C246,163.3,227.6,144.9,204.8,144.9z M171.9,186.1c0-18.2,14.7-32.9,32.9-32.9c8.8,0,16.7,3.5,22.6,9.1l-46.4,46.5C175.4,202.8,171.9,194.8,171.9,186.1z M204.8,219c-6.4,0-12.3-1.9-17.4-5.1l45.2-45.3c3.2,5.1,5.1,11,5.1,17.4C237.8,204.2,223,219,204.8,219z" />
											</svg>
										</div>
										<div class="base-order-table-col base-order-table-col-title">
											<div>
												<strong>{{ item.item.title }}</strong>
											</div>
											<div class="base-order-attrs"><BaseCmsLabel code="code" default="Šifra" />: {{ item.item.code }}</div>
											<div v-if="item.internal_code2 && user?.staff" class="base-order-attrs"><BaseCmsLabel code="supplier_code" default="Šifra vanjskog dobavljača" />: {{ item.internal_code2 }}</div>
											<div v-if="config.viewOrder.itemEanCode && item.ean_code" class="base-order-attrs">EAN: {{ item.ean_code }}</div>
											<template v-if="item.item.attributes?.length">
												<div class="base-order-attrs">
													<template v-for="attribute in item.item.attributes" :key="attribute.id"> {{ attribute.attribute_title }}: {{ attribute.attribute_item_title }}<br /> </template>
												</div>
											</template>
											<div class="base-order-attrs" v-if="warehouses(item)"><BaseCmsLabel code="warehouse_availability" default="Dostupnost" />: {{ warehouses(item) }}</div>
											<div class="base-order-ean-code" v-if="config.viewOrder.itemEanCode && item.ean_code_image"><BaseUiImage :src="item.ean_code_image" width="120" /></div>
											<div v-if="item.always_displayed_attributes?.length" class="base-order-attrs">
												<template v-for="attribute in item.always_displayed_attributes" :key="attribute.id">
													<BaseCmsLabel v-if="labels.get('always_displayed_' + attribute.code)" :code="'always_displayed_' + attribute.code" tag="div" />
													<template v-else>{{ attribute.code }}</template>
												</template>
											</div>
										</div>
										<div class="base-order-table-col base-order-table-col-price" v-if="!order?.customer?.user_disabled_price">
											<template v-if="item.discount_amount">
												<span class="base-order-price-discount">
													<del><BaseUtilsFormatCurrency :price="item.gross_amount" /></del> <span v-if="item.discount_percentage">-{{ Math.round(item.discount_percentage * 100) }}%</span><br />
												</span>
												<BaseUtilsFormatCurrency :price="item.unit_price" />
											</template>
											<template v-else>
												<BaseUtilsFormatCurrency :price="item.unit_price" />
											</template>
											<div class="base-order-small base-order-light" v-if="item.tax_percentage"><BaseCmsLabel code="tax" default="PDV" />: {{ item.tax_percentage * 100 }}%</div>
										</div>
										<div class="base-order-table-col base-order-table-col-qty">
											{{ item.quantity }}
											<div class="base-order-available-qty" v-if="item.available_quantity && user?.staff">
												dostupno <span>{{ item.available_quantity }}</span>
											</div>
										</div>
										<div class="base-order-table-col base-order-table-col-total" v-if="!order?.customer?.user_disabled_price">
											<span class="base-order-price-discount" v-if="item.discount_amount">
												<del><BaseUtilsFormatCurrency :price="item.gross_amount * item.quantity" /></del> <span v-if="item.discount_percentage">-{{ Math.round(item.discount_percentage * 100) }}%</span><br />
											</span>
											<BaseUtilsFormatCurrency :price="item.total" />
										</div>
									</div>
								</div>

								<div class="base-order-totals" v-if="!order?.customer?.user_disabled_price">
									<div class="base-order-totals-row">
										<span class="base-order-totals-label"><BaseCmsLabel code="total_minus_tax" default="Osnovica za izračun PDV-a" />:</span>
										<span class="base-order-totals-value"><BaseUtilsFormatCurrency :price="order.total.total_items_basic" /></span>
									</div>
									<div class="base-order-totals-row">
										<span class="base-order-totals-label">
											<BaseCmsLabel code="tax" default="PDV" /> <template v-if="order.total.tax">({{ order.total.tax }}%)</template>:
										</span>
										<span class="base-order-totals-value"><BaseUtilsFormatCurrency :price="order.total.total_items_tax" /></span>
									</div>
									<template v-if="order?.total?.extra_items">
										<div class="base-order-totals-row" v-for="extraItem in order?.total?.extra_items" :key="extraItem.type">
											<span class="base-order-totals-label"><BaseCmsLabel :code="extraItem.type" default="Dostava" />:</span>
											<span class="base-order-totals-value"><BaseUtilsFormatCurrency :price="extraItem.total" /></span>
										</div>
									</template>
									<div class="base-order-totals-row base-order-totals-row-total">
										<span class="base-order-totals-label"><BaseCmsLabel code="total_to_pay" default="Sveukupno" />:</span>
										<span class="base-order-totals-value"><BaseUtilsFormatCurrency :price="order.total.total" /></span>
									</div>
								</div>

								<div v-if="order.cart?.payments?.selected?.length">
									<strong><BaseCmsLabel code="order_payment" default="Plaćanje" /></strong><br /><span>{{ order.cart.payments.selected[0].title }}</span>
								</div>

								<div v-if="order.cart?.payment_url && order.cart?.payments?.selected?.[0]?.is_credit_card == '1'">
									<br />
									<strong><BaseCmsLabel code="payment_url" default="Link za online plaćanje kreditnom karticom" /></strong>
									<br />
									<div>
										<a target="_blank" :href="order.cart.payment_url">{{ absolute(order.cart.payment_url) }}</a>
									</div>
								</div>

								<div v-if="parcel?.shipping?.selected?.title">
									<br />
									<strong><BaseCmsLabel code="order_shipping" default="Dostava" /></strong><br />
									{{ parcel.shipping.selected.title }}<br />
									<div v-if="parcel?.shipping?.pickup_location?.selected" class="base-order-pickup-location">
										{{ parcel.shipping.pickup_location.selected.title }}
										<div v-if="parcel.shipping.pickup_location.selected.address" v-html="parcel.shipping.pickup_location.selected.address" />
										<div v-if="parcel.shipping.pickup_location.selected.business_hour" v-html="parcel.shipping.pickup_location.selected.business_hour" />
										<div v-if="parcel.shipping.pickup_location.selected.contact" v-html="parcel.shipping.pickup_location.selected.contact" />
									</div>
								</div>

								<div v-if="parcel?.shipping?.selected?.parcel_locker_info">
									<br />
									<strong><BaseCmsLabel code="selected_locker" default="Odabrani paketomat" /></strong><br />
									<template v-if="parcel.shipping.selected.parcel_locker_info.parcel_locker_id">{{ parcel.shipping.selected.parcel_locker_info.parcel_locker_id }}<br /></template>
									<template v-if="parcel.shipping.selected.parcel_locker_info.parcel_locker_title">{{ parcel.shipping.selected.parcel_locker_info.parcel_locker_title }}<br /></template>
									<template v-if="parcel.shipping.selected.parcel_locker_info.parcel_locker_address">
										{{ parcel.shipping.selected.parcel_locker_info.parcel_locker_address }}, {{ parcel.shipping.selected.parcel_locker_info.parcel_locker_zipcode }} {{ parcel.shipping.selected.parcel_locker_info.parcel_locker_city }}<br />
									</template>
								</div>

								<div v-if="order?.cart?.tracking_url">
									<br />
									<strong><BaseCmsLabel :code="isValidUrl(order.cart.tracking_url) ? 'tracking_url' : 'tracking_code'" :default="isValidUrl(order.cart.tracking_url) ? 'Prati pošiljku na' : 'Broj praćenja pošiljke'" /></strong><br />
									<a v-if="isValidUrl(order.cart.tracking_url)" target="_blank" :href="order.cart.tracking_url">{{ order.cart.tracking_url }}</a>
									<span v-else>{{ order.cart.tracking_url }}</span>
								</div>

								<div v-if="order?.customer?.notes_for_client">
									<br />
									<strong><BaseCmsLabel code="notes_for_client" default="Napomena za kupca" /></strong><br />
									<div v-html="order.customer.notes_for_client" />
								</div>

								<div v-if="order?.customer?.message">
									<br />
									<strong><BaseCmsLabel code="order_message" default="Poruka" /></strong><br />
									<div>{{ order.customer.message }}</div>
								</div>
							</template>
						</template>

						<template v-if="config.viewOrder.orderPaymentTransfer && order?.cart?.payment_transfer_data">
							<div class="base-order-payment-transfer">
								<br />
								<BaseWebshopPaymentTransfer :data="order.cart.payment_transfer_data" />
								<br />
							</div>
						</template>

						<div class="base-order-footer" v-if="labels.get('order_view_footer')" v-html="labels.get('order_view_footer')" />

						<div class="base-order-print">
							<a href="javascript:print();">
								<svg id="_x31__px" enable-background="new 0 0 24 24" height="512" viewBox="0 0 24 24" width="512" xmlns="http://www.w3.org/2000/svg">
									<path
										d="m21.5 18h-3c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h3c.827 0 1.5-.673 1.5-1.5v-7c0-.827-.673-1.5-1.5-1.5h-19c-.827 0-1.5.673-1.5 1.5v7c0 .827.673 1.5 1.5 1.5h3c.276 0 .5.224.5.5s-.224.5-.5.5h-3c-1.379 0-2.5-1.122-2.5-2.5v-7c0-1.378 1.121-2.5 2.5-2.5h19c1.379 0 2.5 1.122 2.5 2.5v7c0 1.378-1.121 2.5-2.5 2.5z" />
									<path d="m14.5 21h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z" />
									<path d="m14.5 19h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z" />
									<path d="m10.5 17h-2c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h2c.276 0 .5.224.5.5s-.224.5-.5.5z" />
									<path d="m18.5 7c-.276 0-.5-.224-.5-.5v-4c0-.827-.673-1.5-1.5-1.5h-9c-.827 0-1.5.673-1.5 1.5v4c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-4c0-1.378 1.121-2.5 2.5-2.5h9c1.379 0 2.5 1.122 2.5 2.5v4c0 .276-.224.5-.5.5z" />
									<path d="m16.5 24h-9c-1.379 0-2.5-1.122-2.5-2.5v-8c0-.276.224-.5.5-.5h13c.276 0 .5.224.5.5v8c0 1.378-1.121 2.5-2.5 2.5zm-10.5-10v7.5c0 .827.673 1.5 1.5 1.5h9c.827 0 1.5-.673 1.5-1.5v-7.5z" />
								</svg>
								<span>{{ labels.get('print_order', 'Ispiši narudžbu') }}</span>
							</a>
						</div>

						<div class="base-order-footer-labels" v-if="orderFooterLabels?.length">
							<br />
							<div v-for="footerLabel in orderFooterLabels" :key="footerLabel.code">
								<div v-html="footerLabel.value"></div>
								<br />
							</div>
						</div>
					</div>
				</template>
			</template>
		</Teleport>
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const {user} = useAuth();
	const route = useRoute();
	const labels = useLabels();
	const config = useAppConfig();
	const endpoints = useEndpoints();
	const {formatDate} = useText();
	const {getLastUrlSegment, absolute} = useUrl();

	const order = ref(null);
	const loading = ref(true);
	const imagesLoaded = ref(false);

	// Order footer labels
	const orderFooterLabels = computed(() => {
		if (!config.viewOrder.footerLabels) return [];
		if (!user.value?.staff) return [];

		const allLabels = labels.labels || {};
		const footerLabels = [];

		// Filter labels that start with 'view_order_label'
		Object.keys(allLabels).forEach(labelCode => {
			if (labelCode.startsWith('view_order_label')) {
				let labelValue = allLabels[labelCode];
				if (labelCode === 'view_order_label_1') {
					const currentUser = user.value ? `${user.value.first_name || ''} ${user.value.last_name || ''}`.trim() : '';
					const currentDateTime = formatDate('current', 'DD.MM.YYYY. HH:mm');
					labelValue = labelValue.replace(/%CURRENT_USER%/g, currentUser).replace(/%CURRENT_DATETIME%/g, currentDateTime);
				}
				footerLabels.push({
					code: labelCode,
					value: labelValue,
				});
			}
		});

		return footerLabels;
	});

	// Check if string is valid url
	function isValidUrl(str) {
		try {
			new URL(str);
			return true;
		} catch (error) {
			return false;
		}
	}

	function warehouses(item) {
		const {enabled, permission} = config.viewOrder?.warehouse || {};

		// exit if warehouse is not enabled
		if (!enabled) return null;

		// exit if user does not have permission
		const hasPermission = permission ? user.value?.[permission] : true;
		if (!hasPermission) return null;

		let w = item.meta_data?.warehouses_stats;
		if (!w) return null;

		// return all warehouses if includeEmpty is set to true
		if (config.viewOrder?.warehouse?.includeEmpty) return w;

		// return only warehouses with items
		return w
			.split(',')
			.filter(item => {
				const warehouse = item.split(':');
				if (!warehouse[1]) return false;
				if (parseInt(warehouse[1]) > 0) return warehouse;
			})
			.join(',');
	}

	// Format order number based on config.viewOrder.orderNumber
	function orderNumber() {
		const template = config.viewOrder.orderNumber;
		const data = order.value?.cart;
		const regex = /%([^%]+)%/g;
		return template.replace(regex, (match, key) => data?.[key] || '');
	}

	function preloadImages() {
		const images = [...document.querySelectorAll('img')];

		if (images.length === 0) {
			imagesLoaded.value = true;
			return Promise.resolve();
		}

		const promises = images.map(
			image =>
				new Promise(resolve => {
					if (image.complete && image.naturalHeight !== 0) {
						resolve();
					} else {
						image.onload = resolve;
						image.onerror = resolve;
					}
				})
		);

		return Promise.all(promises).then(() => {
			imagesLoaded.value = true;
		});
	}

	onMounted(async () => {
		const orderParams = getLastUrlSegment(route.path).split('-');
		const orderData = await webshop.fetchOrder({
			code: `${orderParams[0]}-${orderParams[1]}`,
		});
		order.value = orderData?.data ? orderData.data : null;
		//console.log(order.value);

		// loyalty barcode
		if (order.value?.cart?.loyalty?.code) {
			const loyaltyBarCode = await useApi(endpoints.get('_post_hapi_misc_barcode'), {
				method: 'POST',
				body: {
					code: 9901000185647 || order.value?.cart?.loyalty?.code,
				},
			});
			if (loyaltyBarCode?.data?.barcode_path) {
				order.value.loyalty_barcode = absolute(loyaltyBarCode.data.barcode_path);
			}
		}

		// if route contains mode=pdf, wait until content is fully loaded and then print
		if (route.query.mode == 'pdf') {
			loading.value = false;
			await nextTick(() => {
				preloadImages().then(() => {
					if (imagesLoaded.value) {
						// print only if window print function exists
						if (typeof window?.print === 'function') {
							window.print();
						}
					}
				});
			});
		}

		loading.value = false;
	});
</script>

<style>
	.base-view-order #__nuxt {
		display: none;
	}
	.base-view-order {
		background: #fff;
	}
	.base-view-order img {
		max-width: 100%;
		height: auto;
	}
</style>

<style scoped lang="less">
	.loading {
		@media print {
			display: none;
		}
	}
	.base-order-wrapper {
		font: 12px/1.5 Arial, sans-serif;
		max-width: 730px;
		margin: auto;
		background: #fff;
		color: #000;
		padding: 20px;
		@media (max-width: 700px) {
			padding: 5%;
		}
		@media print {
			max-width: 100%;
			padding: 0;
		}
	}
	.base-order-title {
		font-size: 20px;
		font-weight: bold;
		@media (max-width: 700px) {
			font-size: 16px;
		}
	}
	.base-order-status {
		font-size: 14px;
		font-weight: bold;
	}

	.base-order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.base-order-header-col2 {
		text-align: right;
		font-size: 11px;
	}
	.base-order-header-col2 :deep(p) {
		padding: 0 0 10px;
		font-size: 11px !important;
	}
	.base-order-header-col2 :deep(img) {
		max-width: 200px;
		height: auto;
		display: block;
		@media (max-width: 700px) {
			max-width: 120px;
		}
	}

	.base-order-customer {
		display: flex;
		gap: 5%;
		padding: 20px 0 30px;
		@media (max-width: 700px) {
			flex-direction: column;
			padding: 20px 0;
			gap: 10px;
		}
	}
	.base-order-customer-col {
		padding: 0 0 0 5%;
		border-left: 1px solid #e8e8e8;
		@media (max-width: 700px) {
			padding: 0;
			border: 0;
		}
	}
	.base-order-customer-col1 {
		padding-left: 0;
		border: 0;
	}

	@media (max-width: 700px) {
		.base-order-table {
			border-top: 1px solid #e8e8e8;
		}
	}
	.base-order-table-head-row {
		background: #f3f3f3;
		font-weight: bold;
		border-radius: 3px;
		@media (max-width: 700px) {
			display: none !important;
		}
	}

	.base-order-attrs {
		font-size: 11px;
		padding-top: 1px;
		& > div {
			padding: 3px 0;
		}
	}
	.base-order-table-row {
		display: flex;
		border-bottom: 1px solid #e8e8e8;
		position: relative;
		@media (max-width: 700px) {
			display: block;
			padding: 10px 0 10px 90px;
		}
	}
	.base-order-table-col {
		padding: 10px;
		flex-grow: 0;
		flex-shrink: 0;
		@media (max-width: 700px) {
			padding: 0 0 5px;
		}
	}
	.base-order-table-col-no {
		width: 50px;
		text-align: center;
		@media (max-width: 700px) {
			display: none;
		}
	}
	.base-order-table-col-image {
		width: 130px;
		display: flex;
		justify-content: center;
		@media (max-width: 700px) {
			position: absolute;
			top: 10px;
			left: 0;
			width: 75px;
		}
	}
	.base-order-table-col-image :deep(img) {
		width: auto;
		height: auto;
		max-width: 100%;
		max-height: 110px;
	}
	.base-order-table-col-title {
		flex-grow: 1;
		flex-shrink: 1;
	}
	.base-order-table-col-total {
		width: 110px;
		text-align: right;
		@media (max-width: 700px) {
			width: 100%;
			text-align: left;
		}
	}
	.base-order-table-col-qty {
		width: 80px;
		text-align: center;
		@media (max-width: 700px) {
			width: 100%;
			text-align: left;
		}
	}
	.base-order-table-col-price {
		width: 110px;
		text-align: right;
		@media (max-width: 700px) {
			width: 100%;
			text-align: left;
		}
	}
	del {
		text-decoration: line-through;
	}
	.base-order-price-discount {
		font-size: 10px;
		display: block;
	}
	.base-order-small {
		font-size: 10px;
	}
	.base-order-light {
		color: #8e8e8e;
	}

	.base-order-totals {
		padding: 20px 10px;
		text-align: right;
		line-height: 1.6;
		@media (max-width: 700px) {
			text-align: left;
			padding: 10px 0 20px;
		}
	}
	.base-order-totals-row {
		display: flex;
	}
	.base-order-totals-label {
		flex-grow: 1;
	}
	.base-order-totals-value {
		width: 187px;
		@media (max-width: 700px) {
			width: auto;
		}
	}
	.base-order-totals-row-total {
		font-weight: bold;
		font-size: 14px;
	}
	.base-order-pickup-location {
		padding-top: 5px;
	}

	.base-order-print {
		display: flex;
		margin-top: 10px;
		padding-right: 10px;
		justify-content: flex-end;
		@media print {
			display: none;
		}
	}
	.base-order-print a {
		display: flex;
		align-items: center;
		text-decoration: none;
		color: #000;
	}
	.base-order-print svg {
		width: 20px;
		height: auto;
		margin-right: 6px;
	}
	.base-order-footer {
		padding: 20px 0 0;
	}
	.base-order-ean-code {
		margin-top: 5px;
	}
	.base-order-available-qty {
		font-size: 10px;
		line-height: 1.3;
		padding: 2px 0 0;
		color: #8e8e8e;
		span {
			display: block;
		}
	}
	.base-order-loyalty-barcode {
		text-align: center;
		width: 200px;
		font-size: 10px;
	}
	.base-order-loyalty-barcode img {
		display: block;
		margin-bottom: 2px;
	}
</style>
