<template>
	<BaseCmsPage>
		<WebshopCheckout step="login" v-slot="{onContinueAsGuest, onLoginApi, urls}">
			<ClientOnly>
				<div class="wrapper">
					<div class="wc-login">
						<div class="wc-login-box">
							<div class="wc-login-row wc-login-row1">
								<h1 class="wc-title"><BaseCmsLabel code="already_register" /></h1>
								<BaseCmsLabel code="login_to_buy" class="wc-info" tag="div" />
								<button @click="onLoginApi" class="btn">
									<span><BaseCmsLabel code="login_to_buy_button" /></span>
								</button>
							</div>
						</div>
						<div class="wc-login-box">
							<div class="wc-login-row wc-login-row2">
								<div class="wc-title"><BaseCmsLabel code="guest_checkout" /></div>
								<BaseCmsLabel code="guest_checkout_info" tag="div" class="wc-info" />
								<button class="btn" @click="onContinueAsGuest">
									<span><BaseCmsLabel code="continue_without_signup" /></span>
								</button>
							</div>
							<BaseCmsLabel v-if="labels.get('checkout_login_note')" code="checkout_login_note" tag="div" class="wc-login-note" />
						</div>
						<div v-if="labels.get('checkout_login_back')" class="wc-login-row wc-login-row-back">
							<NuxtLink :to="urls.webshop_shopping_cart" class="wc-login-btn-back">
								<span><BaseCmsLabel code="checkout_login_back" /></span>
							</NuxtLink>
						</div>
					</div>
				</div>
				<template #fallback>
					<BaseThemeUiLoading />
				</template>
			</ClientOnly>
		</WebshopCheckout>
	</BaseCmsPage>
</template>

<script setup>
	const webshop = useWebshop();
	const apiRoutes = useApiRoutes();
	const labels = useLabels();
	const gtm = useGtmBB();
	const { getAppUrl } = useApiRoutes();

	const handleBackButton = () => {
		return navigateTo(getAppUrl('webshop_shopping_cart'))
	};

	onMounted(async () => {
		window.addEventListener('popstate', handleBackButton);
	});

	onBeforeUnmount(() => {
		window.removeEventListener('popstate', handleBackButton);
	});
</script>

<style lang="less" scoped>
	.wrapper{padding: 30px;}
	.wc-login{display: flex; flex-direction: column; gap: 12px;}
	.wc-login-box{background: #fff; width: 550px; margin: 0 auto;padding: 24px;border-radius: 12px; padding: 18px;}
	.wc-title{font-size: 24px; padding: 0 0 24px; text-wrap: pretty; font-weight: 600;}
	:deep(.btn){width: 100%;}
	.wc-info{padding: 0 0 24px; text-wrap: balance;}
	.wc-login-note{padding: 24px 0 0; text-wrap: balance;}
	.wc-login-row-back{text-align: center; padding: 10px 0 0;}
</style>
