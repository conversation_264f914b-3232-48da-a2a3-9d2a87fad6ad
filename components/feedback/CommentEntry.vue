<template>
	<article class="comment">
		<div class="comment-header">
			<span class="comment-rate">
				<span v-for="i in 5" :key="i" class="icon-star-empty" :class="{'icon-star': i <= item.rate}"></span>
			</span>
			<span class="comment-date"><BaseUtilsFormatDate :date="item.datetime_created" format="DD.MM.YYYY." /></span>
		</div>

		<div class="comment-message">{{ item.message }}</div>
		<span class="comment-username" :class="{'comment-username-manager': item.manager > 0}">{{ item.display_name }}</span>

		<BaseFeedbackCommentReview :comment="item" v-slot="{status, rateUp, positiveReviews, loading}">
			<div class="comment-review" :class="{'disabled': !item.can_reviews}">
				<template v-if="lang.get('code') == 'hr'">Koris<PERSON></template><template v-else><PERSON><PERSON><PERSON></template>?
				<div class="review-btn" :class="{'active': positiveReviews > 0}" @click="!loading && rateUp()">
					<UiLoader v-if="loading" mode="dots" class="dark" />
					<span v-else>Da ({{ positiveReviews }})</span>
				</div>
				<UiTooltip v-if="status" class="message right"><BaseCmsLabel :code="status" /></UiTooltip>
			</div>
		</BaseFeedbackCommentReview>
		
		<!-- FIXME potvrđena kupnja
		<span class="comment-purchase-confirmed"><BaseCmsLabel code='comment_purchase_confirmed' /></span>
		-->
	</article>
</template>

<script setup>
	const props = defineProps(['item']);
	const lang = useLang();
</script>

<style lang="less" scoped>
	.comment{
		padding: 25px 0 30px; border-top: 1px solid var(--gray2); position: relative;
		@media (max-width: @t){padding: 20px 0 25px;}
	}
	.comment-header{display: flex; align-items: center; padding: 0 0 15px;}
	.comment-rate{
		display: flex; align-items: center; gap: 3px;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 15px; height: 15px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(15px,15px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.icon-star){
			width: 16px; height: 16px;
			&:after{width: 16px; height: 16px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}
	}
	.comment-purchase-confirmed{margin-left: 8px; font-size: 12px; font-weight: 600; color: #1FB549;}
	.comment-date{flex-grow: 1; font-size: 14px; color: var(--gray5); text-align: right;}
	.comment-message{
		max-width: 75%;
		@media (max-width: @t){max-width: 100%;}
	}
	.comment-message, .comment-username{display: block; font-size: 14px; color: var(--gray7);}
	.comment-username{padding-top: 16px;}

	.comment-review{
		display: flex; align-items: center; position: absolute; bottom: 22px; right: 0; gap: 10px; font-size: 14px; color: var(--gray7);
		@media (max-width: @t){font-size: 12px;}
		&.disabled{opacity: 0.6; pointer-events: none;}
	}
	.message{
		:deep(.right){right: 25px;}
	}
	.review-btn{
		display: flex; align-items: center; justify-content: center; height: 40px; color: var(--blueDark); border-radius: 100px; padding: 0 10px; min-width: 85px; text-align: center; color: var(--black); position: relative; cursor: pointer; border: 1px solid var(--blueDark);
		@media (max-width: @t){height: 35px;}
	}
	.comment-source{margin-top: 16px; font-size: 10px; font-weight: 600; color: var(--gray5);}
</style>