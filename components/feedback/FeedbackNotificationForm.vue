<template>
	<div class="cd-unavailable">
		<BaseFeedbackNotificationForm v-slot="{fields, status, loading}">
			<template v-if="!status?.success">
				<div class="cd-unavailable-title red">
					<BaseCmsLabel v-if="itemStatus == 7 && labels.get('not_available_special')" code="not_available_special" tag="span" />
					<BaseCmsLabel v-else-if="itemStatus == 5 && labels.get('not_available_preorder')" code="not_available_preorder" tag="span" />
					<BaseCmsLabel v-else-if="labels.get('not_available')" code="not_available" tag="span" />
				</div>
				<div class="cd-unavailable-note">
					<BaseCmsLabel v-if="itemStatus == 7 && labels.get('not_available_note_special')" code="not_available_note_special" tag="span" />
					<BaseCmsLabel v-else-if="itemStatus == 5 && labels.get('not_available_note_preorder')" code="not_available_note_preorder" tag="span" />
					<BaseCmsLabel v-else-if="labels.get('not_available_note')" code="not_available_note" tag="span" />
				</div>
				<div class="cd-notifyme-form">
					<div class="form-notifyme cd-form-notifyme">
						<div class="field-container">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
								<div :hidden="item.type == 'hidden'">
									<BaseFormInput :placeholder="labels.get('enter_email')" />
									<span class="error" v-show="errorMessage" v-html="errorMessage" />
								</div>
							</BaseFormField>
							<button class="btn btn-green btn-notifyme-form" type="submit" @click="onSubmit" :disabled="loading">{{ labels.get('notifyme') }}</button>
						</div>
					</div>
				</div>
			</template>
			<div class="notifyme-success" v-show="status?.success">
				<div v-html="labels.get('notifyme_catalog_ty')" />
			</div>
		</BaseFeedbackNotificationForm>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['itemStatus']);
</script>

<style lang="less" scoped>
	.cd-unavailable{padding: 0 0 24px;}
	.field-container{position: relative;}
	.cd-unavailable-note{padding: 0 0 10px;}
	:deep(input){padding-right: 165px;}
	.cd-unavailable-title{
		padding-bottom: 10px;
		&:empty{display: none;}
	}
	button{position: absolute; top: 0; right: 0; border-radius: 0 8px 8px 0; padding: 0 10px; width: 150px;}
</style>
