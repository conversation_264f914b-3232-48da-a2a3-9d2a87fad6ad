<template>
	<span class="stars" v-html="props.stars" />
</template>

<script setup>
	const props = defineProps(['stars']);
</script>

<style scoped lang="less">
	.stars{
		display: flex; align-items: center;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 18px; height: 18px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(18px,18px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.active){
			width: 19px; height: 19px;
			&:after{width: 19px; height: 19px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}

		@media (max-width: @m){
			:deep(.icon-star-empty){
				width: 15px; height: 15px; margin-right: 1px;
				&:after{width: 15px; height: 15px;}
				
			}
			:deep(.active){
				width: 16px; height: 16px;
				&:after{width: 16px; height: 16px;}
			}
		}
		&.small{
			:deep(.icon-star-empty){
				width: 14px; height: 14px;
				&:after{width: 14px; height: 14px;}
			}
			:deep(.active){
				width: 15px; height: 15px;
				&:after{width: 15px; height: 15px;}
			}
		}
	}
</style>