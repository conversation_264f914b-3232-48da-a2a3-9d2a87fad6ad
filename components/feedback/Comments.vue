<template>
	<div class="comments">
		<div class="wrapper">
			<span class="close" @click="$emit('close')">X</span>
			<div class="cols">
				<div class="col col1">
					<BaseFeedbackRatesWidget :data="item.feedback_rate_widget" v-slot="{stars, rate}">
						<div class="average-rate">
							<div v-if="rate > 0" class="average-rate-value">{{ rate }}</div>
							<div class="average-rate-cnt">
								<FeedbackStars :stars="stars" class="small" />
								<div><BaseCmsLabel code="review_based" /> ({{ item.feedback_rate_widget.rates_votes }})</div>
							</div>
						</div>
						
						<div v-if="rate > 0 && ratesChart?.length" class="chart-items">
							<div class="chart-item" v-for="item in ratesChart" :key="item.rate">
								<div class="chart-rate">{{ item.rate }}</div>
								<div class="chart-bar"><span class="cd-chart-progress-bar" :style="{width: calculateWidth(item.counter)}"></span></div>
								<div class="chart-qty">{{ item.counter }}</div>
							</div>
						</div>
					</BaseFeedbackRatesWidget>
					<button class="btn btn-outline btn-write-review" @click="modal.open('flyout', {mode: 'comments', header: true, headerIcon: 'star', content: item})"><BaseCmsLabel code="comments_show_form" /></button>
				</div>
				<div class="col col2">
					<div class="header">
						<div class="title"><BaseCmsLabel code="tab_comments" /></div>
					</div>
					<div class="comments-list">
						<template v-if="item.feedback_comment_widget?.items?.length">
							<FeedbackCommentEntry v-for="item in item.feedback_comment_widget.items" :key="item.id" :item="item" />
						</template>
						<div v-else class="no-comments"><BaseCmsLabel code="no_comments" /></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="comments-backdrop" />
</template>

<script setup>
	const props = defineProps({
		item: Object,
	});
	const modal = useModal();

	// rates chart
	const ratesChart = reactive([{ rate: 1, counter: 0 },{ rate: 2, counter: 0 },{ rate: 3, counter: 0 },{ rate: 4, counter: 0 },{ rate: 5, counter: 0 },]);
	
	const ratingsData = props.item?.feedback_comment_widget?.items;
	Object.values(ratingsData).forEach((rating) => {
		const rateIndex = Number(rating.rate) - 1;
		ratesChart[rateIndex].counter++;
	});
	const totalVotes = computed(() => Object.keys(ratingsData).length);
	const calculateWidth = (count) => {
		return totalVotes.value ? `${((count / totalVotes.value) * 100).toFixed(0)}%` : '0%';
	};
</script>

<style scoped lang="less">
	.comments{
		position: fixed; bottom: 0; left: 0; right: 0; background: #fff; z-index: 100; max-height: 75dvh;
		@media (max-width: @t){border-radius: 12px 12px 0 0; max-height: 83dvh;}
	}
	.cols{
		display: flex; gap: 40px;
		@media (max-width: @t){
			display: block; overflow: auto; max-height: 65dvh; overscroll-behavior: contain; padding-right: 15px; margin-top: 15px;
			&::-webkit-scrollbar{width: 5px;}
			&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 100px;}
			&::-webkit-scrollbar-track{background: var(--gray2); border-radius: 100px; margin-top: 10px; margin-bottom: 15px;}
		}
	}
	.col1{
		flex: 0 0 460px; padding: 40px 0;
		@media (max-width: @t){padding: 0 0 25px;}
	}
	.col2{
		padding: 40px 0 0;
		@media (max-width: @t){padding: 0;}
	}
	.comments-backdrop{position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,.5); z-index: 99;}
	.average-rate{
		text-align: center; font-size: 12px; border-bottom: 1px solid var(--gray2); padding-bottom: 25px; margin: 10px 0 25px 0;
		@media (max-width: @t){display: flex; text-align: left; align-items: center; gap: 12px; margin: 10px 0 15px; padding-bottom: 15px;}
	}
	.average-rate-value{
		font-size: 42px; font-weight: bold; line-height: 1; padding: 0 0 20px;
		@media (max-width: @t){padding: 0;}
	}
	:deep(.stars){
		margin: auto; display: inline-flex; margin-bottom: 7px;
		@media (max-width: @t){margin: 0;}
	}
	.chart-items{
		padding-bottom: 25px; display: flex; flex-direction: column-reverse;
		@media (max-width: @t){padding: 0;}
	}
	.chart-item{display: flex; padding: 4px 0; display: flex; align-items: center; gap: 7px; font-size: 12px; color: var(--gray7);}
	.chart-rate{
		display: flex; align-items: center; gap: 7px;
		&:after{.icon-star(); font: 12px/1 var(--fonti); color: var(--textColor);}
	}
	.chart-bar{flex-grow: 1; background: var(--gray2); border-radius: 100px; height: 6px;}
	.cd-chart-progress-bar{display: block; height: 100%; background: var(--blueDark); border-radius: 100px; width: 30%;}
	.btn-write-review{
		width: 100%;
		@media (max-width: @t){position: absolute; top: 75px; left: 0; right: 0;}
	}
	
	.wrapper{
		position: relative;
		@media (max-width: @t){padding: 120px 0 0;}
	}
	.close{
		position: absolute; top: 40px; right: 50px; width: 35px; height: 35px; font-size: 0; display: flex; align-items: center; justify-content: center; cursor: pointer; z-index: 100;
		@media (max-width: @t){top: 15px; right: 0;}
		&:before{
			.icon-x(); font: 23px/1 var(--fonti); color: var(--textColor);
			@media (max-width: @t){font-size: 20px;}
		}
	}
	.header{position: relative; z-index: 55; background: #fff; margin-right: 50px;}
	.title{
		font-size: 28px; font-weight: bold; padding: 0 0 25px;
		@media (max-width: @t){font-size: 24px;}
	}
	.comments-list{
		overflow: auto; height: calc(~"75dvh - 55px"); margin-top: -60px; padding-top: 70px; padding-right: 50px; overscroll-behavior: contain;
		@media (max-width: @t){padding-right: 0; height: auto; overflow: visible; padding-top: 0; margin-top: 0;}
		&::-webkit-scrollbar{width: 5px;}
		&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 100px;}
		&::-webkit-scrollbar-track{background: var(--gray2); border-radius: 100px;}
	}
</style>