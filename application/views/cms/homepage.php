<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-homepage<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
	<?php $benefits = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'benefits', 'limit' => 6)); ?>
	<?php if($benefits): ?>
		<div class="benefits <?php $this->block('benefits_class'); ?><?php $this->endblock('benefits_class'); ?>">
			<div class="wrapper wrapper-benefits">
				<?php foreach ($benefits as $benefit_item): ?>
					<div class="benefit">
						<?php if($benefit_item['link']): ?><a href="<?php echo $benefit_item['link']; ?>"<?php if($benefit_item['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php endif; ?>
							<?php if(!empty($benefit_item['image'])): ?>
								<div class="benefit-img">
									<img loading="lazy" data-css="lazyload" width="30" height="30" data-original="<?php echo Utils::file_url($benefit_item['image']); ?>" src="/media/images/no-image-50.webp" alt="<?php echo $benefit_item['title']; ?>"/>
								</div>
							<?php endif; ?>
							<?php if($benefit_item['title']): ?>
								<div class="benefit-title"><?php echo $benefit_item['title']; ?></div>
							<?php endif; ?>
						<?php if($benefit_item['link']): ?></a><?php endif; ?>
					</div>
				<?php endforeach; ?>
			</div>
		</div>
	<?php endif; ?>
		
	<?php $hp_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_rotator', 'limit' => 4]); ?>
	<?php if(!empty($hp_rotator)): ?>
		<div class="hp-rotator wrapper">
			<?php $this->block('hp_intro'); ?>
				<div class="hp-slider single-item">
					<?php $hp_i = 1; ?>
					<?php foreach($hp_rotator as $hp_rotator_item): ?>
						<?php $hp_rotator_item_title = str_replace('"', "'", Text::meta($hp_rotator_item['title'])); ?>
						<?php if($hp_rotator_item['link']): ?><a class="hp-rotator-slide hp-rotator-slide-link slick-slide-<?php echo $hp_rotator_item['id']; ?>" href="<?php echo $hp_rotator_item['link']; ?>" <?php if($hp_rotator_item['link_target_blank']): ?> target="_blank"<?php endif; ?>  data-tracking_gtm_promo_click="<?php echo $hp_rotator_item['id']; ?>|<?php echo $hp_rotator_item_title; ?>|<?php echo $hp_rotator_item['image']; ?>|home - rotator - <?php echo $hp_i; ?>"><?php else: ?><div class="hp-rotator-slide slick-slide-<?php echo $hp_rotator_item['id']; ?>"><?php endif; ?>
							<?php if(!empty($hp_rotator_item['image'])): ?>
								<picture class="hp-rotator-img" data-tracking_gtm_promo_view="<?php echo $hp_rotator_item['id']; ?>|<?php echo $hp_rotator_item_title; ?>|<?php echo $hp_rotator_item['image']; ?>|home - rotator - <?php echo $hp_i; ?>">
									<?php if(!empty($hp_rotator_item['image_4'])): ?>
										<source srcset="<?php echo Thumb::generate($hp_rotator_item['image_4'], 760, 397, true, 'thumb', TRUE, '/media/images/no-image-hero-m.webp'); ?>" media="(max-width: 760px)">	
									<?php endif; ?>
									<img <?php echo Thumb::generate($hp_rotator_item['image'], array('width' => 1480, 'height' => 540, 'crop' => true, 'default_image' => '/media/images/no-image-hero.webp', 'placeholder' => '/media/images/no-image-hero.webp', 'srcset' => '2960c 2x')); ?> alt="<?php echo $hp_rotator_item_title; ?>" />
								</picture>
							<?php endif; ?>
						<?php if($hp_rotator_item['link']): ?></a><?php else: ?></div><?php endif; ?>
						<?php $hp_i++; ?>
					<?php endforeach; ?>
				</div>

				<?php if(count($hp_rotator) > 1): ?>
					<div class="hp-slider-nav progressBarContainer">
						<?php $t = 0; ?>
						<?php foreach($hp_rotator as $hp_rotator_item): ?>
							<?php $hp_rotator_item_title = str_replace('"', "'", Text::meta($hp_rotator_item['title'])); ?>
							<a href="javascript:void(0);" data-slide-index="<?php echo $t; ?>" class="hp-nav-slide" data-variationids="<?php echo (isset($hp_rotator_item['variations_ids']) ? $hp_rotator_item['variations_ids'] : ''); ?>">
								<?php /* ?>
									<?php if(!empty($hp_rotator_item['image_3'])): ?>
										<div class="hp-rotator-nav-img">
											<img <?php echo Thumb::generate($hp_rotator_item['image_3'], array('width' => 98, 'height' => 98, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'placeholder' => '/media/images/no-image-50.jpg', 'srcset' => '200c 2x')); ?> alt="<?php echo $hp_rotator_item_title; ?>" />
										</div>
									<?php endif; ?>
									<?php if(!empty($hp_rotator_item['element_title_small']) OR !empty($hp_rotator_item['element_content_small'])): ?>
										<div class="hp-rotator-nav-cnt">
											<?php if(!empty($hp_rotator_item['element_title_small'])): ?>
												<div class="hp-rotator-nav-title2"><?php echo $hp_rotator_item['element_title_small']; ?></div>
											<?php endif; ?>
											<?php if(!empty($hp_rotator_item['element_content_small'])): ?>
												<div class="hp-rotator-nav-content-small"><?php echo $hp_rotator_item['element_content_small']; ?></div>
											<?php endif; ?>
										</div>
									<?php endif; ?>
								<?php */ ?>
								<span data-slick-index="<?php echo $t; ?>" class="progressBar"></span>
							</a>
							<?php $t++; ?>
							<?php if($t == 4) break; ?>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			<?php $this->endblock('hp_intro'); ?>
		</div>
	<?php endif; ?>

	<?php echo View::factory('cms/widget/featured_promo'); ?>

	<?php $speciallists = Widget_Catalog::speciallists(['lang' => $info['lang'], 'homepage' => true, 'sort' => 'homepage_position', 'limit' => 5, 'cache_lifetime' => 30]); ?>
	<?php if (count($speciallists)): ?>
		<div class="cw wrapper">
			<?php $speciallists_items = []; ?>
			<?php $i = 0; ?>
			<?php foreach ($speciallists as $speciallist): ?>
				<?php  $speciallists_items[$speciallist['code']] = Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $speciallist['code'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 20, 'user_use_loyalty' => !empty($user->loyalty_code))); ?>
				<?php $i++; ?>
			<?php endforeach; ?>
			<?php $i = 0; ?>
			<?php foreach ($speciallists as $speciallist): ?>
				<?php if(!empty($speciallists_items[$speciallist['code']])): ?>
					<div class="cw-section">
						<div class="cw-header hp-section-header">
							<div class="cw-title hp-title"><?php echo $speciallist['title']; ?></div>
							<a class="btn btn-arrow hp-btn-d" href="<?php echo (!empty($speciallist['external_url'])) ? $speciallist['external_url'] : $speciallist['url']; ?>"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
						</div>
						<div class="cw-tabs-content swipe-slider blazy-container">
							<?php echo View::factory('catalog/index_entry', ['items' => $speciallists_items[$speciallist['code']], 'mode' => 'slider']); ?>
						</div>
					</div>
				<?php endif; ?>
				<?php $i++; ?>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>

	<?php $this->block('brands'); ?>
		<?php $menu_sis = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'shop_in_shop', 'level_range' => '1.1']); ?>
		<?php $manufacturer_url = Utils::app_absolute_url($info['lang'], 'catalog', 'manufacturer'); ?>
		<?php if(!empty($menu_sis)): ?>
			<div class="hp-brands wrapper">
				<div class="hp-brands-header hp-section-header">
					<div class="hp-brands-title hp-title"><?php echo Arr::get($cmslabel, 'homepage_brands_title'); ?></div>
					<a class="btn btn-arrow" href="<?php echo $manufacturer_url; ?>"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
				</div>
				<div class="hp-brands-slider swipe-slider blazy-container">
					<?php foreach ($menu_sis as $menu_sis_item): ?>
						<?php if(!empty($menu_sis_item['image'])): ?>
							<a class="hp-brands-slide" href="<?php echo $menu_sis_item['url']; ?>">
								<?php $fileExtension = pathinfo($menu_sis_item['image'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img loading="lazy" src="<?php echo Utils::file_url($menu_sis_item['image']); ?>" alt="<?php echo $menu_sis_item['title']; ?>">
								<?php else: ?>
									<img loading="lazy" data-lazy="<?php echo Thumb::generate($menu_sis_item['image'], 155, 60, false, 'thumb', TRUE, '/media/images/no-image-100.webp'); ?>" <?php echo Thumb::generate($menu_sis_item['image'], array('width' => 155, 'height' => 60, 'default_image' => '/media/images/no-image-50.webp', 'placeholder' => '/media/images/no-image-50.webp', 'srcset' => '350r 2x')); ?> alt="<?php echo Text::meta($menu_sis_item['title']); ?>" />
								<?php endif; ?>
							</a>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			</div>
		<?php endif; ?>
	<?php $this->endblock('brands'); ?>

	<?php echo View::factory('cms/widget/featured_promo', ['mode' => 'extra']); ?>

	<?php $hp_promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_promo', 'limit' => 8]); ?>
	<?php $promotions = Widget_Publish::category($info['lang'], 'promotions'); ?>
	<?php if(!empty($hp_promo) OR !empty($promotions)): ?>
		<div class="hp-promo hp-bg-grey">
			<div class="wrapper">
				<?php $this->block('homepage_promo'); ?>
					<?php echo View::factory('cms/widget/promo'); ?>
				<?php $this->endblock('homepage_promo'); ?>
			</div>
		</div>
	<?php endif; ?>

	<?php $special_offers_list = Widget_Catalog::speciallist($info['lang'], 'special_offers', true); ?>
	<?php if ($special_offers_list): ?>
		<?php $special_offers_item = Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $special_offers_list['code'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 9)); ?>
		<?php if($special_offers_item): ?>
			<?php $special_offers_list_title = str_replace('"', "'", Text::meta($special_offers_list['title'])); ?>
			<div class="hp-so">
				<div class="wrapper">
					<div class="hp-so-header">
						<?php if($special_offers_list['title']): ?>
							<div class="hp-so-title hp-title"><?php echo $special_offers_list['title']; ?></div>
						<?php endif; ?>
						<a href="<?php echo (!empty($special_offers_list['external_url'])) ? $special_offers_list['external_url'] : $special_offers_list['url']; ?>" class="btn btn-arrow"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
					</div>
					<div class="hp-so-cnt">
						<?php if($special_offers_list['main_image']): ?>
							<div class="hp-so-promo">
								<a class="lloader" href="<?php echo (!empty($special_offers_list['external_url'])) ? $special_offers_list['external_url'] : $special_offers_list['url']; ?>" data-tracking_gtm_promo_click="<?php echo $special_offers_list['id']; ?>|<?php echo $special_offers_list_title; ?>|<?php echo $special_offers_list['main_image']; ?>|home - special offer">
									<picture data-tracking_gtm_promo_view="<?php echo $special_offers_list['id']; ?>|<?php echo $special_offers_list_title; ?>|<?php echo $special_offers_list['main_image']; ?>|home - special offer">
										<?php if(!empty($special_offers_list['main_image_2'])): ?>
											<source srcset="<?php echo Thumb::generate($special_offers_list['main_image_2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
										<?php endif; ?>
										<img loading="lazy" <?php echo Thumb::generate($special_offers_list['main_image'], array('width' => 355, 'height' => 595, 'crop' => TRUE, 'default_image' => '/media/images/no-image-100.webp', 'placeholder' => '/media/images/no-image-100.webp', 'srcset' => '710c 2x')); ?><?php echo Thumb::generate($special_offers_list['main_image'], array('width' => 355, 'height' => 595, 'crop' => TRUE, 'html_tag' => TRUE, 'srcset' => '710c 2x')); ?> alt="<?php echo $special_offers_list['title']; ?>" />
									</picture>
								</a>
							</div>
						<?php endif; ?>
						<div class="hp-so-items swipe-slider blazy-container">
							<?php echo View::factory('catalog/index_entry', ['items' => $special_offers_item, 'mode' => 'slider']); ?>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	
	<?php $hp_info_cards = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_cards_info', 'limit' => 3]); ?>
	<?php if($hp_info_cards): ?>
		<div class="hp-info-cards hp-bg-grey">
			<div class="wrapper">
				<div class="hp-cards-section">
					<?php $hpic_i = 1; ?>
					<?php foreach($hp_info_cards as $hp_info_card): ?>
						<?php $hp_info_card_title = str_replace('"', "'", Text::meta($hp_info_card['title'])); ?>
						<?php if($hp_info_card['link']): ?><a class="hp-card hp-card-link<?php if($hp_info_card['template'] == 'template_card_medium'): ?> hp-card-medium<?php elseif($hp_info_card['template'] == 'template_card_small'): ?> hp-card-small<?php endif; ?>" href="<?php echo $hp_info_card['link']; ?>" <?php if($hp_info_card['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $hp_info_card['id']; ?>|<?php echo $hp_info_card_title; ?>|<?php echo $hp_info_card['image']; ?>|home - infocards<?php if($hp_info_card['template'] == 'template_card_medium'): ?> - medium<?php elseif($hp_info_card['template'] == 'template_card_small'): ?> - small<?php endif; ?> - <?php echo $hpic_i; ?>"><?php else: ?><div class="hp-card<?php if($hp_info_card['template'] == 'template_card_medium'): ?> hp-card-medium<?php elseif($hp_info_card['template'] == 'template_card_small'): ?> hp-card-small<?php endif; ?>"><?php endif; ?>
							<?php if(!empty($hp_info_card['image'])): ?>
								<div class="hp-cards-img lloader" data-tracking_gtm_promo_view="<?php echo $hp_info_card['id']; ?>|<?php echo $hp_info_card_title; ?>|<?php echo $hp_info_card['image']; ?>|home - infocards<?php if($hp_info_card['template'] == 'template_card_medium'): ?> - medium<?php elseif($hp_info_card['template'] == 'template_card_small'): ?> - small<?php endif; ?> - <?php echo $hpic_i; ?>">
									<span>
										<?php if($hp_info_card['template'] == 'template_card_medium'): ?>
											<img loading="lazy" <?php echo Thumb::generate($hp_info_card['image'], array('width' => 730, 'height' => 540, 'crop' => true, 'default_image' => '/media/images/no-image-100.webp', 'placeholder' => '/media/images/no-image-100.webp', 'srcset' => '1460c 2x')); ?> alt="<?php echo $hp_info_card['title']; ?>" />
										<?php elseif($hp_info_card['template'] == 'template_card_small'): ?>
											<img loading="lazy" <?php echo Thumb::generate($hp_info_card['image'], array('width' => 730, 'height' => 260, 'crop' => true, 'default_image' => '/media/images/no-image-100.webp', 'placeholder' => '/media/images/no-image-100.webp', 'srcset' => '1460c 2x')); ?> alt="<?php echo $hp_info_card['title']; ?>" />
										<?php endif; ?>
									</span>	
								</div>
							<?php endif; ?>
							<?php if(!empty($hp_info_card['image_2']) OR !empty($hp_info_card['title']) OR !empty($hp_info_card['content']) OR !empty($hp_info_card['element_button'])): ?>
								<div class="hp-cards-cnt">
									<?php if(!empty($hp_info_card['image_2']) AND $hp_info_card['template'] == 'template_card_medium'): ?>
										<div class="hp-cards-logo">
											<img loading="lazy" <?php echo Thumb::generate($hp_info_card['image_2'], ['width' => 140, 'height' => 70, 'html_tag' => true, 'srcset' => '280c 2x']); ?>  alt="<?php echo $hp_info_card['title']; ?>" />
										</div>
									<?php endif; ?>
									<?php if(!empty($hp_info_card['title'])): ?>
										<div class="hp-cards-title"><?php echo $hp_info_card['title']; ?></div>
									<?php endif; ?>
									<?php if(!empty($hp_info_card['content'])): ?>
										<div class="hp-cards-desc"><?php echo $hp_info_card['content']; ?></div>
									<?php endif; ?>
									<?php if(!empty($hp_info_card['element_button'])): ?>
										<div class="hp-cards-btn btn btn-white"><?php echo $hp_info_card['element_button']; ?></div>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						<?php if($hp_info_card['link']): ?></a><?php else: ?></div><?php endif; ?>
						<?php $hpic_i++; ?>
					<?php endforeach; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<?php $blog = Widget_Publish::category($info['lang'], 'info'); ?>
	<?php if (!empty($blog)): ?>
		<?php $publish_categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => '01')); ?>
		<?php $publish_post_featured = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => 'info', 'single' => 1, 'extra_fields' => ['short_description'])); ?>
		<?php $publish_post = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $blog['code'], 'limit' => 4, 'extra_fields' => ['short_description'])); ?>
		<?php if($blog): ?>
			<div class="pw">
				<div class="wrapper">		
					<div class="pw-row">
						<div class="pw-col1">
							<div class="pw-col1-header">
								<div class="pw-title hp-title"><?php echo Arr::get($cmslabel, 'blog'); ?></div>
								<div class="btn btn-white btn-mobile pw-categories-btn">
									<?php echo Arr::get($cmslabel, 'm_categories'); ?>
									<div class="pw-categories-btn-cnt"></div>
								</div>
							</div>
							<ul class="pw-nav">
								<?php foreach ($publish_categories as $publish_category): ?>
									<li>
										<a href="<?php echo $publish_category['url']; ?>"<?php if ($publish_category['url'] == $publish_category): ?> class="active"<?php endif; ?>>
											<span><?php echo $publish_category['title']; ?></span>
											<?php if($publish_category['main_image']): ?>
												<span class="pw-nav-img"><img loading="lazy" src="<?php echo Utils::file_url($publish_category['main_image']); ?>" alt="<?php echo $publish_category['title']; ?>"/></span>
											<?php endif; ?>
										</a>
									</li>
								<?php endforeach; ?>
							</ul>
							<a href="<?php echo $blog['url']; ?>" class="btn btn-arrow pw-btn"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
						</div>
						<div class="pw-col2">
							<div class="pw-items">
								<div class="pw-items-col1">
									<?php if(!empty($publish_post_featured)): ?>
										<div class="hp-p-featured">
											<?php echo View::factory('publish/index_entry_big', ['item' => $publish_post_featured]); ?>	
										</div>
									<?php endif; ?>
								</div>
								<div class="pw-items-col2">
									<?php if(!empty($publish_post)): ?>
										<?php if($info['user_device'] == 'm'): ?>
											<?php echo View::factory('publish/index_entry', ['items' => $publish_post]); ?>
										<?php else: ?>
											<?php echo View::factory('publish/index_entry', ['items' => $publish_post, 'mode' => 'homepage']); ?>
										<?php endif; ?>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>
	
	<?php /* ?>
	<?php $menu_sis = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'shop_in_shop', 'level_range' => '1.1']); ?>
	<?php if($menu_sis): ?>
		<div class="sis-section hp-bg-grey">
			<div class="wrapper">
				<div class="sis-title hp-title"><?php echo Arr::get($cmslabel, 'sis_title'); ?></div>
				<div class="sis-cnt">
					<?php foreach ($menu_sis as $menu_sis_item): ?>
						<div class="sis-item">
							<div class="sis-item-cnt">
								<a class="sis-item-title" href="<?php echo $menu_sis_item['url']; ?>" <?php if($menu_sis_item['target_blank']): ?> target="_blank"<?php endif; ?>>
									<div class="sis-item-img lloader">
										<span>
											<img data-lazy="<?php echo Thumb::generate(Arr::get($menu_sis_item, 'image'), 170, 100, true, 'thumb', TRUE, '/media/images/no-image-355.jpg'); ?>" <?php echo Thumb::generate(Arr::get($menu_sis_item, 'image'), array('width' => 170, 'height' => 100, 'crop' => true, 'default_image' => '/media/images/no-image-355.jpg', 'placeholder' => '/media/images/no-image-355.jpg', 'srcset' => '240c 2x')); ?> alt="<?php echo $menu_sis_item['title']; ?>" />
										</span>
									</div>
									<?php echo $menu_sis_item['title']; ?>
								</a>
								<?php $menu_sis_l2 = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'shop_in_shop', 'start_position' => $menu_sis_item['position_h'], 'level_range' => '2.2']); ?>								
								<?php if($menu_sis_l2): ?>
									<ul class="sis-item-nav">
										<?php foreach($menu_sis_l2 as $menu_sis_item_l2): ?>
											<li><a href="<?php echo $menu_sis_item_l2['url']; ?>"><?php echo $menu_sis_item_l2['title']; ?></a></li>
										<?php endforeach; ?>
									</ul>
								<?php endif; ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>
	<?php */ ?>

<?php $this->endblock('main'); ?>

<?php $this->block('extrabody'); ?>
	<script async defer data-domain="Big Bang UAU " data-thankyou="Big Bang UAU " data-back="Big Bang UAU " data-new-tab="true" data-position="left" src="https://unicef.cnj.digital/js/widget.js"></script>
<?php $this->endblock('extrabody'); ?>