<?php $this->extend('cms/homepage'); ?>

<?php $this->block('hp_intro'); ?>
	<div class="hp2-section">
		<?php $homepage_intro_left = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_intro_left', 'limit' => 1]); ?>
		<?php if($homepage_intro_left): ?>
			<?php foreach($homepage_intro_left as $homepage_intro_left_item): ?>
				<?php if($homepage_intro_left_item['link']): ?><a class="hp-intro hp-intro-link hp-intro-left" href="<?php echo $homepage_intro_left_item['link']; ?>"<?php if($homepage_intro_left_item['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php else: ?><div class="hp-intro hp-intro-left"><?php endif; ?>
					<picture>
						<?php if(!empty($homepage_intro_left_item['image_2'])): ?>
							<source srcset="<?php echo Thumb::generate($homepage_intro_left_item['image_2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
						<?php endif; ?>
						<img <?php echo Thumb::generate($homepage_intro_left_item['image'], array('width' => 340, 'height' => 500, 'html_tag' => TRUE, 'srcset' => '680c 2x')); ?> alt="<?php echo $homepage_intro_left_item['title']; ?>" />
					</picture>
				<?php if($homepage_intro_left_item['link']): ?></a><?php else: ?></div><?php endif; ?>
			<?php endforeach; ?>
		<?php endif; ?>

		<?php $hp_rotator2 = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_rotator2', 'limit' => 4]); ?>
		<?php if($hp_rotator2): ?>
			<div class="hp-slider2">
				<?php foreach($hp_rotator2 as $hp_rotator_item): ?>
					<?php if($hp_rotator_item['link']): ?><a class="hp-rotator-slide hp-rotator-slide-link slick-slide-<?php echo $hp_rotator_item['id']; ?> <?php if($hp_rotator_item['template'] == 'template_text_left'): ?> hp-slide-text-left<?php elseif($hp_rotator_item['template'] == 'template_text_right'): ?> hp-slide-text-right<?php endif; ?>" href="<?php echo $hp_rotator_item['link']; ?>"<?php if($hp_rotator_item['link_target_blank']): ?> target="_blank"<?php endif; ?> ><?php else: ?><div class="hp-rotator-slide slick-slide-<?php echo $hp_rotator_item['id']; ?> <?php if($hp_rotator_item['template'] == 'template_text_left'): ?> hp-slide-text-left<?php elseif($hp_rotator_item['template'] == 'template_text_right'): ?> hp-slide-text-right<?php endif; ?>"><?php endif; ?>
						<?php if(!empty($hp_rotator_item['element_color_1']) OR !empty($hp_rotator_item['element_color_2']) OR !empty($hp_rotator_item['element_color_3'])): ?>
							<style>
								.slick-slide-<?php echo $hp_rotator_item['id']; ?>{color: <?php echo $hp_rotator_item['element_color_1']; ?>!important;}
								.slick-slide-<?php echo $hp_rotator_item['id']; ?> a{color: <?php echo $hp_rotator_item['element_color_1']; ?>!important;}
								.slick-slide-<?php echo $hp_rotator_item['id']; ?> a:hover{color: <?php echo $hp_rotator_item['element_color_1']; ?>!important;}
								.slick-slide-<?php echo $hp_rotator_item['id']; ?> .hp-rotator-btn{background: linear-gradient(226.06deg, <?php echo $hp_rotator_item['element_color_2']; ?> 0%, <?php echo $hp_rotator_item['element_color_3']; ?> 100%); color: <?php echo $hp_rotator_item['element_color_1']; ?>!important;}
							</style>
						<?php endif; ?>

						<?php if(!empty($hp_rotator_item['image'])): ?>
							<div class="hp-rotator-img">
								<picture>
									<?php if(!empty($hp_rotator_item['image_4'])): ?>
										<source srcset="<?php echo Thumb::generate($hp_rotator_item['image_4'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
									<?php endif; ?>
									<img <?php echo Thumb::generate($hp_rotator_item['image'], array('width' => 760, 'height' => 500, 'crop' => true, 'default_image' => '/media/images/no-image-760.webp', 'html_tag' => TRUE, 'srcset' => '1520c 2x')); ?> alt="<?php echo $hp_rotator_item['title']; ?>" />
								</picture>
							</div>
						<?php endif; ?>
						<?php if(!empty($hp_rotator_item['image_2']) OR !empty($hp_rotator_item['content']) OR !empty($hp_rotator_item['element_button'])): ?>
							<?php if(!empty($hp_rotator_item['image_2'])): ?>
								<div class="hp-rotator-cnt-icon">
									<div class="hp-rotator-icon"><img src="<?php echo Utils::file_url($hp_rotator_item['image_2']); ?>" alt="<?php echo $hp_rotator_item['title']; ?>" /></div>
								</div>						
							<?php endif; ?>
							<div class="hp-rotator-cnt<?php if(!empty($hp_rotator_item['image_2'])): ?> hp-rotator-cnt-special<?php endif; ?>">
								<?php /*if(!empty($hp_rotator_item['title'])): ?>
									<div class="hp-rotator-title"><?php echo $hp_rotator_item['title']; ?></div>
								<?php endif;*/ ?>
								<?php if(!empty($hp_rotator_item['content'])): ?>
									<div class="hp-rotator-desc"><?php echo $hp_rotator_item['content']; ?></div>
								<?php endif; ?>
								<?php if(!empty($hp_rotator_item['element_button'])): ?>
									<div class="hp-rotator-btn btn"><?php echo $hp_rotator_item['element_button']; ?></div>
								<?php endif; ?>
							</div>
						<?php endif; ?>
					<?php if($hp_rotator_item['link']): ?></a><?php else: ?></div><?php endif; ?>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<?php $hp_intro_right = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_intro_right', 'limit' => 1]); ?>
		<?php if($hp_intro_right): ?>
			<?php foreach($hp_intro_right as $hp_intro_right_item): ?>
				<?php if($hp_intro_right_item['link']): ?><a class="hp-intro hp-intro-link hp-intro-right" href="<?php echo $hp_intro_right_item['link']; ?>"<?php if($hp_intro_right_item['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php else: ?><div class="hp-intro hp-intro-right"><?php endif; ?>
					<picture>
						<?php if(!empty($hp_intro_right_item['image_2'])): ?>
							<source srcset="<?php echo Thumb::generate($hp_intro_right_item['image_2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
						<?php endif; ?>
						<img <?php echo Thumb::generate($hp_intro_right_item['image'], array('width' => 340, 'height' => 500, 'html_tag' => TRUE, 'srcset' => '680c 2x')); ?> alt="<?php echo $hp_intro_right_item['title']; ?>" />
					</picture>
				<?php if($hp_intro_right_item['link']): ?></a><?php else: ?></div><?php endif; ?>
			<?php endforeach; ?>
		<?php endif; ?>
	</div>
<?php $this->endblock('hp_intro'); ?>

<?php $this->block('homepage_promo'); ?>
	<?php $promotions = Widget_Publish::category($info['lang'], 'promotions'); ?>
	<?php $promotions_categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => '02')); ?>
	<?php if($promotions): ?>
		<div class="hp-pr pr-tabs-container">
			<div class="hp-pr-title hp-title"><?php echo Arr::get($cmslabel, 'discounts'); ?></div>
			<select class="tabs pr-tabs-select">								
				<?php foreach ($promotions_categories as $promotions_category): ?>					
					<?php if($promotions_category['total'] > 0): ?>
						<option value="tab-<?php echo $promotions_category['code']; ?>"><?php echo $promotions_category['title']; ?></option>
						<?php print_r($promotions_category); ?>
					<?php endif; ?>
				<?php endforeach; ?>
			</select>
			<div class="tabs-content pr-content">
				<?php foreach ($promotions_categories as $promotions_category): ?>
					<div class="tab" id="tab-<?php echo $promotions_category['code']; ?>">
						<?php $promotions_post = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $promotions_category['code'], 'limit' => 4, 'extra_fields' => ['short_description'])); ?>	
						<div class="prh-items">
							<?php echo View::factory('publish/promotions/index_entry', ['items' => $promotions_post]); ?>	
						</div>
						<div class="prh-btn-section">
							<a href="<?php echo $promotions_category['url']; ?>" class="btn btn-white btn-medium"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
						</div>
					</div>
				<?php endforeach; ?>			
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('homepage_promo'); ?>	