<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-contact<?php $this->endblock('page_class'); ?>


<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="main-header<?php if(!empty($cms_page['main_image'])): ?> bg-img<?php endif; ?>" style="background-image: url(<?php echo Utils::file_url(Arr::get($cms_page, 'main_image')); ?>);">
			<div class="wrapper">
				<?php $this->block('h1'); ?>
					<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
				<?php $this->endblock('h1'); ?>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content'); ?>
	<?php if(!empty($cms_page['content'])): ?>
		<div class="content-intro"><?php echo Arr::get($cms_page, 'content'); ?></div>
	<?php endif; ?>
	<?php $contacts = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'contacts', 'limit' => 30)); ?>
	<div class="contact-row1">
		<div class="contact-card">
			<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
			<?php echo Arr::get($cmslabel, 'support'); ?>
		</div>
		<?php foreach ($contacts as $contact): ?>
			<div class="contact-card">
				<div class="contact-card-title"><?php echo $contact['title']; ?></div>
				<?php if($contact['element_title_small']): ?>
					<div class="contact-card-department">(<?php echo $contact['element_title_small']; ?>)</div>
				<?php endif; ?>
				<?php if($contact['element_tel']): ?>
					<?php $tel = str_replace(' ', '', $contact['element_tel']) ?>
					<a href="tel:<?php echo $tel; ?>" class="contact-card-num contact-card-tel"><?php echo $contact['element_tel']; ?></a>
				<?php endif; ?>
				<?php if($contact['element_mail']): ?>
					<a href="mailto:<?php echo $contact['element_mail']; ?>" class="contact-card-num contact-card-mail"><span><?php echo $contact['element_mail']; ?></span></a>
				<?php endif; ?>
			</div>
		<?php endforeach; ?>
	</div>

	<?php /* ?>
		<div class="contact-row2">
			<?php if($cms_page['content']): ?>
				<div class="contact-title"><?php echo Arr::get($cmslabel, 'big_bang_doo'); ?></div>
			<?php endif; ?>
			<div class="contact-cnt"><?php echo Arr::get($cms_page, 'content'); ?></div>
		</div>
	<?php */ ?>


	<?php $map_points = (isset($city_array)) ? $city_array : Widget_Location::points(array('lang' => $info['lang'])); ?>
	<?php if(!empty($map_points)): ?>
		<a href="<?php echo Utils::app_absolute_url($info['lang'], 'location') ?>" class="btn btn-white btn-contact-stores"><span><?php echo Arr::get($cmslabel, 'stores_business_hour'); ?></span></a>
	<?php endif; ?>
	<?php /* ?>
		<div class="contact-row3">	
			<div class="contact-title"><?php echo Arr::get($cmslabel, 'stores'); ?></div>

			<?php $city = ''; ?>

			<?php $city_array = array(); ?>
		    <?php foreach ($map_points as $point): ?>
		        <?php $city = $point['headline']; ?>
		        <?php 
		            if(!empty($city) AND !in_array($city, $city_array)){
		                $city_array[] = $city; 
		            }
		        ?>
		    <?php endforeach; ?>
			
			<div class="city-list">
		        <?php foreach ($city_array as $city_item): ?>
		            <a href="<?php if(!isset($url)): ?><?php echo Utils::app_absolute_url($info['lang'], 'location') ?><?php endif; ?>#<?php echo str_replace(' ', '', $city_item); ?>"><span><?php echo $city_item; ?></span></a>
		        <?php endforeach; ?>
			</div>
		</div>
	<?php */ ?>
<?php $this->endblock('content'); ?>

<?php $this->block('share'); ?>
	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('share'); ?>

<?php /*$this->block('map'); ?>
<div class="map-content map-content-contact">
	<?php echo View::factory('location/widget/map', ['map_points' => $map_points]); ?>
</div>
<?php $this->endblock('map');*/ ?>

<?php $this->block('extrabody'); ?>
	<?php if ($map_points): ?>
		<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>