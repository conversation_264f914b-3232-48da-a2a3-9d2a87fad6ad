<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>


<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="main-header<?php if(!empty($cms_page['main_image'])): ?> bg-img<?php endif; ?>" style="background: url(<?php echo Utils::file_url(Arr::get($cms_page, 'main_image')); ?>) no-repeat;">
			<div class="wrapper">
				<?php $this->block('h1'); ?>
					<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
				<?php $this->endblock('h1'); ?>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content'); ?>

	<?php echo Arr::get($cms_page, 'content'); ?>

<?php $this->endblock('content'); ?>

<?php $this->block('share'); ?>
	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('share'); ?>

<?php /*if (Text::starts_with($cms_page['slug'], '/storitve/')): ?>
	<?php $this->block('sidebar'); ?>
		<aside class="sidebar">
			<div class="sidebar-title"><?php echo Arr::get($cmslabel, 'about_storitve', 'Storitve'); ?></div>
			<ul class="nav-sidebar">
				<?php $pages = Widget_Cms::pages(['lang' => $info['lang'], 'slug_starts_with' => '/storitve/']); ?>
				<?php foreach ($pages AS $page): ?> 
					<li><a href="<?php echo $page['url']; ?>"><?php echo $page['title']; ?></a></li>
				<?php endforeach; ?>
			</ul>
			<div class="support support-sidebar">
				<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
				<?php echo Arr::get($cmslabel, 'support'); ?>
				<div class="faq-link"><?php echo Arr::get($cmslabel, 'faq'); ?></div>
			</div>
		</aside>
	<?php $this->endblock('sidebar'); ?>
<?php endif;*/ ?>