<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php if($mode == 'extra'): ?>
	<?php $featured_promo_id = 'featured_promo_extra'; ?>
<?php else: ?>
	<?php $featured_promo_id = 'featured_promo'; ?>
<?php endif; ?>

<?php $featured_promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => $featured_promo_id, 'limit' => 20]); ?>


<?php if(!empty($featured_promo)): ?>
	<div class="hp-featured-promo wrapper<?php if($mode == 'extra'): ?> extra<?php endif; ?>">
		<?php if(!empty($cmslabel['homepage_featured_promo_title'])): ?>
			<div class="hp-featured-promo-header hp-section-header">
				<div class="hp-featured-promo-title hp-title">
					<?php if($mode == 'extra'): ?>
						<?php echo Arr::get($cmslabel, 'homepage_featured_extra_promo_title'); ?>
					<?php else: ?>
						<?php echo Arr::get($cmslabel, 'homepage_featured_promo_title'); ?>
					<?php endif; ?>
				</div>
			</div>
		<?php endif; ?>
		<div class="hp-featured-promo-slider swipe-slider blazy-container" data-count-items="<?php echo count($featured_promo); ?>">
			<?php $fp_i = 1; ?>
			<?php foreach($featured_promo as $featured_promo_item): ?>
				<?php $featured_promo_item_title = str_replace('"', "'", Text::meta($featured_promo_item['title'])); ?>
				<?php if(!empty($featured_promo_item['link'])): ?><a href="<?php echo $featured_promo_item['link']; ?>" <?php if($featured_promo_item['link_target_blank']): ?>target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $featured_promo_item['id']; ?>|<?php echo $featured_promo_item_title; ?>|<?php echo $featured_promo_item['image']; ?>|home - featured - <?php echo $fp_i; ?>" class="hp-fpromo-item hp-fpromo-item-<?php echo $featured_promo_item['id']; ?> link"><?php else: ?><div class="hp-fpromo-item hp-fpromo-item-<?php echo $featured_promo_item['id']; ?>"><?php endif; ?>
					<div class="hp-fpromo-item-box">
						<style>
							<?php if(!empty($featured_promo_item['element_color_4'])): ?>
								.hp-fpromo-item-<?php echo $featured_promo_item['id']; ?> .hp-fpromo-item-bg{background: <?php echo $featured_promo_item['element_color_4']; ?>!important;}
							<?php endif; ?>
						</style>
						<?php if(!empty($featured_promo_item['image'])): ?>
							<div class="hp-fpromo-item-img" data-tracking_gtm_promo_view="<?php echo $featured_promo_item['id']; ?>|<?php echo $featured_promo_item_title; ?>|<?php echo $featured_promo_item['image']; ?>|home - featured - <?php echo $fp_i; ?>">
								<picture>
									<?php if(!empty($featured_promo_item['image_2'])): ?>
										<source srcset="<?php echo Thumb::generate($featured_promo_item['image_2'], 390, 270, false, 'thumb', TRUE, '/media/images/no-image-50.webp'); ?>" media="(max-width: 760px)">
									<?php endif; ?>
									<img <?php echo Thumb::generate($featured_promo_item['image'], array('width' => 355, 'height' => 180, 'crop' => true, 'default_image' => '/media/images/no-image-355x180.webp', 'html_tag' => true, 'srcset' => '710c 2x')); ?> alt="<?php if(!empty($featured_promo_item['title'])): ?><?php echo $featured_promo_item['title']; ?><?php endif; ?>" />
								</picture>
							</div>
						<?php endif; ?>
						<?php if(empty($featured_promo_item['image']) AND !empty($featured_promo_item['element_color_4'])): ?>
							<div class="hp-fpromo-item-bg"></div>
						<?php endif; ?>
						<?php if(!empty($featured_promo_item['image_3'])): ?>
							<div class="hp-fpromo-item-icon">
								<?php $fileExtension = pathinfo($featured_promo_item['image_3'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img loading="lazy" src="<?php echo Utils::file_url($featured_promo_item['image_3']); ?>" alt="<?php echo $featured_promo_item['title']; ?>">
								<?php else: ?>
									<img loading="lazy" <?php echo Thumb::generate($featured_promo_item['image_3'], array('width' => 75, 'height' => 75, 'crop' => false, 'default_image' => '/media/images/no-image-50.webp', 'placeholder' => '/media/images/no-image-50.webp')); ?> alt="<?php echo $featured_promo_item['title']; ?>" />
								<?php endif; ?>
							</div>
						<?php endif; ?>
					</div>
					<?php if(!empty($featured_promo_item['title'])): ?>
						<div class="hp-fpromo-item-title"><?php echo $featured_promo_item['title']; ?></div>
					<?php endif; ?>
				<?php if(!empty($featured_promo_item['link'])): ?></a><?php else: ?></div><?php endif; ?>
				<?php $fp_i++; ?>
			<?php endforeach; ?>
		</div>
	</div>
<?php endif; ?>