<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
<div class="wrapper">
	<div class="header-nav-main">
		<div class="header-nav-main-item header-categories-btn has-children">
			<span class="icon"></span>
			<span class="title"><?php echo Arr::get($cmslabel, 'header_categories_btn'); ?></span>
		</div>

		<?php $h_manufacturers = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'special' => 1, 'limit' => 10)); ?>
		<?php if($h_manufacturers): ?>
			<div class="header-nav-main-item header-brands-btn<?php if($h_manufacturers): ?> special has-children<?php endif; ?>">
		<?php else: ?>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog', 'manufacturer'); ?>" class="header-nav-main-item header-brands-btn<?php if($h_manufacturers): ?> has-children<?php endif; ?>">
		<?php endif; ?>
			<span class="title" data-menu_title="<?php echo Arr::get($cmslabel, 'header_brands_btn'); ?>"><?php echo Arr::get($cmslabel, 'header_brands_btn'); ?></span>
			
			<?php if($h_manufacturers): ?>
				<div class="header-dropdown header-brands">
					<div class="header-brands-col1">
						<div class="header-dropdown-title"><?php echo Arr::get($cmslabel, 'header_brands_title'); ?></div>
						<div class="header-brands-wrapper">
							<?php foreach ($h_manufacturers as $h_manufacturer):?>
								<a class="header-brand" href="<?php echo $h_manufacturer['url']; ?>">
									<?php if(!empty($h_manufacturer['main_image'])): ?>
										<?php $fileExtension = pathinfo($h_manufacturer['main_image'], PATHINFO_EXTENSION); ?>
										<?php if($fileExtension == 'svg'): ?>
											<img src="<?php echo Utils::file_url($h_manufacturer['main_image']); ?>" alt="<?php echo $h_manufacturer['title']; ?>">
										<?php else: ?>
											<img <?php echo Thumb::generate($h_manufacturer['main_image'], array('width' => 170, 'height' => 80, 'default_image' => '/media/images/no-image-160.webp', 'html_tag' => true)); ?> alt="<?php echo $h_manufacturer['title']; ?>" />
										<?php endif; ?>
									<?php else: ?>
										<span><?php echo $h_manufacturer['title']; ?></span>
									<?php endif; ?>
								</a>
							<?php endforeach; ?>
						</div>
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog', 'manufacturer'); ?>" class="header-brand-btn-all btn"><?php echo Arr::get($cmslabel, 'header_brands_show_all'); ?></a>
					</div>
				</div>
			<?php endif; ?>
		<?php if($h_manufacturers): ?>
			</div>
		<?php else: ?>
			</a>
		<?php endif; ?>

		<?php $services = Widget_Publish::category($info['lang'], 'services'); ?>
		<?php $services_categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '1.2', 'start_position' => '03')); ?>
		<?php if($services): ?>
			<div class="header-nav-main-item header-services-btn<?php if($services_categories): ?> special has-children<?php endif; ?>">
				<span class="title" data-menu_title="<?php echo Arr::get($cmslabel, 'header_services_btn'); ?>"><?php echo Arr::get($cmslabel, 'header_services_btn'); ?></span>

				<div class="header-dropdown header-services">
					<div class="header-services-wrapper">
						<div class="header-services-lvl1">
							<div class="header-dropdown-title"><?php echo Arr::get($cmslabel, 'header_services_title'); ?></div>
							<?php foreach ($services_categories as $services_category): ?>
								<?php $services_posts = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $services_category['code'], 'limit' => 10)); ?>
								<?php if(!empty($services_posts)): ?>
									<div class="header-service-category has-children" data-menu_item_title="<?php echo $services_category['title']; ?>" data-code="<?php echo $services_category['code']; ?>">
								<?php else: ?>
									<a class="header-service-category link" href="<?php echo $services_category['url']; ?>">
								<?php endif; ?>
									<?php if($services_category['main_image_2']): ?>
										<span class="img">
											<?php $fileExtension = pathinfo($services_category['main_image_2'], PATHINFO_EXTENSION); ?>
											<?php if($fileExtension == 'svg'): ?>
												<img loading="lazy" src="<?php echo Utils::file_url($services_category['main_image_2']); ?>" alt="<?php echo $services_category['title']; ?>">
											<?php else: ?>
												<img loading="lazy" <?php echo Thumb::generate($services_category['main_image_2'], array('width' => 36, 'height' => 36, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($services_category['title']); ?>" />
											<?php endif; ?>
										</span>
									<?php endif; ?>
									<span class="title"><?php echo $services_category['title']; ?></span>
								<?php if(!empty($services_posts)): ?>
									</div>
								<?php else: ?>
									</a>
								<?php endif; ?>
							<?php endforeach; ?>
						</div>
						<?php foreach ($services_categories as $services_category): ?>
							<?php $services_posts = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $services_category['code'], 'limit' => 10)); ?>
							<?php if(!empty($services_posts)): ?>
								<div class="header-services-lvl2" data-code="<?php echo $services_category['code']; ?>">
									<div class="header-services-lvl2-col1">
										<a href="<?php echo $services_category['url']; ?>" class="header-dropdown-title"><?php echo $services_category['title']; ?></a>
										<div class="header-services-posts">
											<?php echo View::factory('publish/services/index_entry_small', ['items' => $services_posts, 'mode' => 'menu']); ?>
										</div>
									</div>
									<?php if(!empty($services_category['main_image_3'])): ?>
										<a href="<?php echo $services_category['url']; ?>" class="header-services-lvl2-col2">
											<img <?php echo Thumb::generate($services_category['main_image_3'], array('width' => 980, 'height' => 980, 'crop' => false, 'default_image' => '/media/images/no-image-50.webp', 'html_tag' => true)); ?> alt="<?php echo Text::meta($services_category['title']); ?>" />
										</a>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				</div>
			</div>
		<?php endif; ?>
		
		<?php $nav_loyalty = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'header_loyalty', 'selected' => $active_menu_item, 'level_range' => '1', 'hierarhy_by_position' => true)); ?>
		<?php if(!empty($nav_loyalty)): ?>
			<div class="header-nav-main-item header-loyalty-btn special has-children">
				<span class="title" data-menu_title="<?php echo Arr::get($cmslabel, 'header_loyalty_btn'); ?>"><?php echo Arr::get($cmslabel, 'header_loyalty_btn'); ?></span>
				
				<div class="header-dropdown header-dropdown-loyalty">
					<?php foreach ($nav_loyalty as $nav_loyalty_item): ?>
						<a class="header-loyalty-item<?php if(!empty($nav_loyalty_item['style'])): ?> <?php echo $nav_loyalty_item['style']; ?><?php endif; ?>" href="<?php echo $nav_loyalty_item['url']; ?>" <?php if($nav_loyalty_item['target_blank']): ?> target="_blank"<?php endif; ?>>
							<?php if(!empty($nav_loyalty_item['image'])): ?>
								<span class="img">
									<?php $fileExtension = pathinfo($nav_loyalty_item['image'], PATHINFO_EXTENSION); ?>
									<?php if($fileExtension == 'svg'): ?>
										<img loading="lazy" src="<?php echo Utils::file_url($nav_loyalty_item['image']); ?>" alt="<?php echo $nav_loyalty_item['title']; ?>">
									<?php else: ?>
										<img loading="lazy" <?php echo Thumb::generate($nav_loyalty_item['image'], array('width' => 36, 'height' => 36, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($nav_loyalty_item['title']); ?>" />
									<?php endif; ?>
								</span>
							<?php endif; ?>
							<span class="title"><?php echo $nav_loyalty_item['title']; ?></span>
						</a>
					<?php endforeach; ?>
				</div>
			</div>
		<?php endif; ?>


		<?php $blog = Widget_Publish::category($info['lang'], 'info'); ?>
		<?php $blog_categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '1.2', 'start_position' => '01')); ?>
		<?php if($blog): ?>
			<div class="header-nav-main-item header-blog-btn<?php if($blog_categories): ?> special has-children<?php endif; ?>">
				<span class="title" data-menu_title="<?php echo Arr::get($cmslabel, 'header_blog_title'); ?>"><?php echo Arr::get($cmslabel, 'header_blog_btn'); ?></span>

				<div class="header-dropdown header-publish">
					<div class="header-publish-wrapper">
						<div class="header-publish-col header-publish-col1">
							<div class="header-dropdown-title"><?php echo Arr::get($cmslabel, 'header_blog_title'); ?></div>
							<div class="header-publish-show-all"><a href="<?php echo $blog['url']; ?>"><strong><?php echo Arr::get($cmslabel, 'header_blog_show_all'); ?></strong></a></div>
							<?php foreach ($blog_categories as $blog_category): ?>
								<a class="header-service-category link" href="<?php echo $blog_category['url']; ?>">
									<?php if($blog_category['main_image']): ?>
										<span class="img">
											<?php $fileExtension = pathinfo($blog_category['main_image'], PATHINFO_EXTENSION); ?>
											<?php if($fileExtension == 'svg'): ?>
												<img loading="lazy" src="<?php echo Utils::file_url($blog_category['main_image']); ?>" alt="<?php echo $blog_category['title']; ?>">
											<?php else: ?>
												<img loading="lazy" <?php echo Thumb::generate($blog_category['main_image'], array('width' => 36, 'height' => 36, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($blog_category['title']); ?>" />
											<?php endif; ?>
										</span>
									<?php endif; ?>
									<span class="title"><?php echo $blog_category['title']; ?></span>
								</a>
							<?php endforeach; ?>
						</div>
						<?php $publish_post = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => $blog['code'], 'limit' => 5)); ?>
						<?php if(!empty($publish_post)): ?>
							<div class="header-publish-col header-publish-col2">
								<div class="header-dropdown-title"><?php echo Arr::get($cmslabel, 'header_blog_posts'); ?></div>
								<?php echo View::factory('publish/index_entry', ['items' => $publish_post, 'mode' => 'menu']); ?>
								<a class="header-publish-show-all-btn btn" href="<?php echo $blog['url']; ?>"><strong><?php echo Arr::get($cmslabel, 'header_blog_show_all'); ?></strong></a>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>

	<?php $nav_featured = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'header_featured', 'selected' => $active_menu_item, 'level_range' => '1.2', 'hierarhy_by_position' => true)); ?>
	<?php if(!empty($nav_featured)): ?>
		<ul class="header-nav-second">
			<?php foreach ($nav_featured as $nav_featured_item): ?>
				<?php $nav_featured_lvl2 = (!empty($nav_featured_item['children'])) ? $nav_featured_item['children'] : [] ?>
				<li class="<?php if(!empty($nav_featured_lvl2)): ?>has-children<?php endif; ?>">
					<a href="<?php echo $nav_featured_item['url']; ?>" <?php if($nav_featured_item['target_blank']): ?> target="_blank"<?php endif; ?>>
						<?php if(!empty($nav_featured_item['image'])): ?>
							<span class="image<?php if(!empty($nav_featured_item['style'])): ?> <?php echo $nav_featured_item['style']; ?><?php endif; ?>">
								<?php $fileExtension = pathinfo($nav_featured_item['image'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img loading="lazy" src="<?php echo Utils::file_url($nav_featured_item['image']); ?>" alt="<?php echo $nav_featured_item['title']; ?>">
								<?php else: ?>
									<img loading="lazy" <?php echo Thumb::generate($nav_featured_item['image'], array('width' => 32, 'height' => 32, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($nav_featured_item['title']); ?>" />
								<?php endif; ?>
							</span>
						<?php endif; ?>
						<span class="title"><?php echo $nav_featured_item['title']; ?></span>
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
	<?php endif; ?>

	<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.3', 'hierarhy_by_position' => true)); ?>
	<?php if(!empty($categories)): ?>
		<div class="header-categories">
			<div class="header-categories-wrapper header-categories-wrapper-lvl1">
				<div class="header-categories-lvl header-categories-lvl1">
					<div class="header-categoris-title"><?php echo Arr::get($cmslabel, 'header_categories_title'); ?></div>
					<?php foreach ($categories as $category): ?>
						<?php $subcategories = (!empty($category['children'])) ? $category['children'] : []; ?>
						<a class="header-categories-item<?php if(!empty($subcategories)): ?> has-children<?php endif; ?>" href="<?php echo $category['url']; ?>" data-menu_item_position="menu-item-position<?php echo $category['position_h']; ?>" data-menu_item_title="<?php echo $category['title']; ?>" data-category="<?php echo $category['code']; ?>">
							<?php if($category['main_image']): ?>
								<span class="img"><img loading="lazy" src="<?php echo Utils::file_url($category['main_image']); ?>" width="36" height="36" alt="<?php echo $category['title']; ?>"/></span>
							<?php endif; ?>
							<span class="title"><?php echo $category['title']; ?></span>
						</a>
					<?php endforeach; ?>
				</div>
			</div>

			<?php foreach ($categories as $category): ?>
				<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
				<?php if($subcategories): ?>
					<div class="header-categories-wrapper header-categories-wrapper-lvl2" data-category="<?php echo $category['code']; ?>" data-menu_title="<?php echo $category['title']; ?>">
						<div class="header-categories-lvl header-categories-lvl2">
							<div class="header-categoris-title"><?php echo $category['title']; ?></div>
							<div class="header-categories-show-all"><a href="<?php echo $category['url']; ?>"><strong><?php echo Arr::get($cmslabel, 'header_categories_show_all'); ?></strong></a></div>
							<?php foreach ($subcategories as $subcategory): ?>
								<?php $subsubcategories = (!empty($subcategory['children'])) ? $subcategory['children'] : [] ?>
									<a class="header-categories-item<?php if(!empty($subsubcategories)): ?> has-children<?php endif; ?>" href="<?php echo $subcategory['url']; ?>" data-subcategory="<?php echo $subcategory['code']; ?>" data-menu_item_position="menu-item-position<?php echo $subcategory['position_h']; ?>" data-menu_item_title="<?php echo $subcategory['title']; ?>">
										<?php if($subcategory['main_image_2']): ?>
											<span class="img"><img loading="lazy" src="<?php echo Utils::file_url($subcategory['main_image_2']); ?>" width="36" height="36" alt="<?php echo $subcategory['title']; ?>"/></span>
										<?php endif; ?>
										<span class="title"><?php echo $subcategory['title']; ?></span>
									</a>
							<?php endforeach; ?>
						</div>

						<?php if(!empty($category['code'])): ?>
							<?php $menu_promo = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'menu_promo', 'catalogcategory_id' => $category['id'], 'limit' => 15)); ?>
							<?php $header_products_list = Widget_Catalog::speciallists(['lang' => $info['lang'], 'category' => $category['id'], 'limit' => 1, 'single' => true]); ?>
							<?php if(!empty($header_products_list)): ?>
								<?php $header_products = (!empty($header_products_list['code'])) ? Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $header_products_list['code'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 4)) : []; ?>
							<?php endif; ?>
							<?php if(!empty($menu_promo)): ?>
								<?php foreach($menu_promo as $menu_promo_item): ?>
									<div class="header-categories-lvl header-promo header-extra-content" data-tracking_gtm_promo_view="<?php echo $category['id']; ?>|<?php echo $category['title']; ?>||megamenu - <?php echo $category['title']; ?>">
										<?php if(!empty($menu_promo_item['url'])): ?><a href="<?php echo $menu_promo_item['url']; ?>"<?php if($menu_promo_item['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $category['id']; ?>|<?php echo $category['title']; ?>||megamenu - <?php echo $category['title']; ?>"><?php endif; ?>
											<img <?php echo Thumb::generate($menu_promo_item['image'], array('width' => 980, 'height' => 980, 'crop' => false, 'default_image' => '/media/images/no-image-50.webp', 'html_tag' => true)); ?> alt="<?php echo Text::meta($category['title']); ?>" />
										<?php if(!empty($menu_promo_item['url'])): ?></a><?php endif; ?>
									</div>
								<?php endforeach; ?>
							<?php elseif(!empty($header_products_list) && !empty($header_products)): ?>
								<div class="header-categories-lvl header-bestsellers header-extra-content">
									<div class="header-categoris-title"><?php echo Arr::get($cmslabel, 'top_products'); ?></div>
									<?php echo View::factory('catalog/index_entry_featured', ['items' => $header_products, 'mode' => 'menu']); ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					</div>
				<?php endif; ?>
			<?php endforeach; ?>

			<?php foreach ($categories as $category): ?>
				<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
				<?php if($subcategories): ?>
					<?php foreach ($subcategories as $subcategory): ?>
						<?php $subsubcategories = (!empty($subcategory['children'])) ? $subcategory['children'] : [] ?>
						<?php if($subsubcategories): ?>
							<div class="header-categories-wrapper header-categories-wrapper-lvl3" data-subcategory="<?php echo $subcategory['code']; ?>">
								<div class="header-categories-lvl header-categories-lvl3">
									<div class="header-categoris-title"><?php echo $subcategory['title']; ?></div>
									<div class="header-categories-show-all"><a href="<?php echo $subcategory['url']; ?>"><strong><?php echo Arr::get($cmslabel, 'header_categories_show_all'); ?></strong></a></div>
									<?php if(!empty($subcategory['total_discount'])): ?>
										<a class="header-categories-item discount" href="<?php echo $subcategory['url']; ?>?discount=1"><?php echo Arr::get($cmslabel, 'sale'); ?></a>
									<?php endif; ?>
									<?php foreach ($subsubcategories as $subsubcategory): ?>
											<a class="header-categories-item" href="<?php echo $subsubcategory['url']; ?>" data-menu_item_position="menu-item-position<?php echo $subsubcategory['position_h']; ?>" data-menu_item_title="<?php echo $subsubcategory['title']; ?>">
												<?php if($subsubcategory['main_image_2']): ?>
													<span class="img"><img loading="lazy" src="<?php echo Utils::file_url($subsubcategory['main_image_2']); ?>" width="36" height="36" alt="<?php echo $subsubcategory['title']; ?>"/></span>
												<?php endif; ?>
												<span class="title"><?php echo $subsubcategory['title']; ?></span>
											</a>
									<?php endforeach; ?>
									<?php if(!empty($subcategory['total_new'])): ?>
										<a class="header-categories-item new" href="<?php echo $subcategory['url']; ?>?new=1"><?php echo Arr::get($cmslabel, 'new'); ?></a>
									<?php endif; ?>
								</div>

								<?php if(!empty($subcategory['code'])): ?>
									<?php $menu_promo = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'menu_promo', 'catalogcategory_id' => $subcategory['id'], 'limit' => 15)); ?>
									<?php $header_products_list = Widget_Catalog::speciallists(['lang' => $info['lang'], 'category' => $subcategory['id'], 'limit' => 1, 'single' => true]); ?>
									<?php if(!empty($header_products_list)): ?>
										<?php $header_products = (!empty($header_products_list['code'])) ? Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $header_products_list['code'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 4)) : []; ?>
									<?php endif; ?>
									<?php if(!empty($menu_promo)): ?>
										<?php foreach($menu_promo as $menu_promo_item): ?>
											<div class="header-categories-lvl header-promo header-extra-content" data-tracking_gtm_promo_view="<?php echo $subcategory['id']; ?>|<?php echo $subcategory['title']; ?>||megamenu - <?php echo $subcategory['title']; ?>">
												<?php if(!empty($menu_promo_item['url'])): ?><a href="<?php echo $menu_promo_item['url']; ?>"<?php if($menu_promo_item['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $subcategory['id']; ?>|<?php echo $subcategory['title']; ?>||megamenu - <?php echo $subcategory['title']; ?>"><?php endif; ?>
													<img <?php echo Thumb::generate($menu_promo_item['image'], array('width' => 980, 'height' => 980, 'crop' => false, 'default_image' => '/media/images/no-image-50.webp', 'html_tag' => true)); ?> alt="<?php echo Text::meta($subcategory['title']); ?>" />
												<?php if(!empty($menu_promo_item['url'])): ?></a><?php endif; ?>
											</div>
										<?php endforeach; ?>
									<?php elseif(!empty($header_products_list) && !empty($header_products)): ?>
										<div class="header-categories-lvl header-bestsellers header-extra-content">
											<div class="header-categoris-title"><?php echo Arr::get($cmslabel, 'top_products'); ?></div>
											<?php echo View::factory('catalog/index_entry_featured', ['items' => $header_products, 'mode' => 'menu']); ?>
										</div>
									<?php endif; ?>
								<?php endif; ?>
							</div>
						<?php endif; ?>
					<?php endforeach; ?>
				<?php endif; ?>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>
</div>