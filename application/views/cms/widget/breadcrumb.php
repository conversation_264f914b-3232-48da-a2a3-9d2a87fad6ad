<?php $last_breadcrumb = @array_pop(array_keys($breadcrumb)); ?>
<div class="bc-items" xmlns:v="http://rdf.data-vocabulary.org/#" itemprop="breadcrumb">
	<?php foreach ($breadcrumb as $link => $title): ?>
		<?php if ($link === $last_breadcrumb): ?>
			<span class="bc-last" property="v:title"><?php echo $title; ?></span>
		<?php else: ?>
			<span class="bc-item">
				<a href="<?php echo $link; ?>" rel="v:url" property="v:title"><?php echo $title; ?></a>

				<?php /*if (!empty($breadcrumb_submenu[$link])): ?>
					<ul>
						<?php foreach ($breadcrumb_submenu[$link] AS $submenu_link => $submenu_title): ?>
							<li><a href="<?php echo $submenu_link; ?>"><?php echo $submenu_title; ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif;*/ ?>
			</span>
		<?php endif; ?>
	<?php endforeach; ?>
</div>