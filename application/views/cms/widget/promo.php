<?php $hp_promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage_promo', 'limit' => 8]); ?>
<?php $promotions = Widget_Publish::category($info['lang'], 'promotions'); ?>
<?php if($hp_promo): ?>
	<div class="hp-promo-header">
		<div class="hp-promo-title hp-title"><?php echo Arr::get($cmslabel, 'best_offers'); ?></div>
		<a href="<?php echo $promotions['url']; ?>" class="btn btn-arrow"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
	</div>
	<div class="hp-promo-section">
		<?php $hpp_i = 1; ?>
		<?php foreach ($hp_promo as $hp_promo_item): ?>
			<?php $hp_promo_item_title = str_replace('"', "'", Text::meta($hp_promo_item['title'])); ?>
			<?php if($hp_promo_item['image']): ?>
				<?php if($hp_promo_item['link']): ?><a href="<?php echo $hp_promo_item['link']; ?>" class="hp-promo-item lloader hp-promo-item-link<?php if($hp_promo_item['template'] == 'template_hp_promo_big'): ?> hp-promo-item-big<?php elseif($hp_promo_item['template'] == 'template_hp_promo_medium'): ?> hp-promo-item-medium<?php endif; ?>" <?php if($hp_promo_item['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $hp_promo_item['id']; ?>|<?php echo $hp_promo_item_title; ?>|<?php echo $hp_promo_item['image']; ?>|home - bestoffers<?php if($hp_promo_item['template'] == 'template_hp_promo_big'): ?> - big<?php elseif($hp_promo_item['template'] == 'template_hp_promo_medium'): ?> - medium<?php endif; ?> - <?php echo $hpp_i; ?>"><?php else: ?><div class="hp-promo-item lloader<?php if($hp_promo_item['template'] == 'template_hp_promo_big'): ?> hp-promo-item-big<?php elseif($hp_promo_item['template'] == 'template_hp_promo_medium'): ?> hp-promo-item-medium<?php endif; ?>"><?php endif; ?>
					<?php if($hp_promo_item['image']): ?>
						<span data-tracking_gtm_promo_view="<?php echo $hp_promo_item['id']; ?>|<?php echo $hp_promo_item_title; ?>|<?php echo $hp_promo_item['image']; ?>|home - bestoffers<?php if($hp_promo_item['template'] == 'template_hp_promo_big'): ?> - big<?php elseif($hp_promo_item['template'] == 'template_hp_promo_medium'): ?> - medium<?php endif; ?> - <?php echo $hpp_i; ?>">
							<?php if($hp_promo_item['template'] == 'template_hp_promo_big'): ?>
								<img loading="lazy" <?php echo Thumb::generate($hp_promo_item['image'], array('width' => 730, 'height' => 2000, 'default_image' => '/media/images/no-image-100.webp', 'placeholder' => '/media/images/no-image-100.webp', 'srcset' => '1460c 2x')); ?> alt="<?php echo $hp_promo_item['title']; ?>" />
							<?php elseif($hp_promo_item['template'] == 'template_hp_promo_medium'): ?>
								<img loading="lazy" <?php echo Thumb::generate($hp_promo_item['image'], array('width' => 480, 'height' => 2000, 'default_image' => '/media/images/no-image-100.webp', 'placeholder' => '/media/images/no-image-100.webp', 'srcset' => '960c 2x')); ?> alt="<?php echo $hp_promo_item['title']; ?>" />
							<?php endif; ?>
							<?php /*<img <?php echo Thumb::generate($hp_promo_item['image'], array('width' => 480, 'height' => 260, 'html_tag' => TRUE, 'srcset' => '960c 2x')); ?> alt="<?php echo $hp_promo_item['title']; ?>" />	*/?>
						</span>
					<?php endif; ?>
				<?php if($hp_promo_item['link']): ?></a><?php else: ?></div><?php endif; ?>
			<?php endif; ?>
			<?php $hpp_i++; ?>
		<?php endforeach; ?>
	</div>
<?php endif; ?>