<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta((!empty($kind['seo_title'])) ? $kind['seo_title'] : Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-faq<?php $this->endblock('page_class'); ?>

<?php $this->block('title_container_class'); ?> special-title-container faq-title-container<?php $this->endblock('title_container_class'); ?>
<?php $this->block('main_class'); ?> faq-main<?php $this->endblock('main_class'); ?>

<?php $this->block('breadcrumb'); ?>
    <?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
    <?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main'); ?>
    <div class="faq-intro ps-intro" style="background: url(<?php if($info['user_device'] == 'm'): ?><?php if (!empty($kind)): ?><?php echo Utils::file_url(Arr::get($kind, 'main_image_3')); ?><?php else: ?><?php echo Utils::file_url(Arr::get($cms_page, 'main_image_3')); ?><?php endif; ?><?php else: ?><?php if (!empty($kind)): ?><?php echo Utils::file_url(Arr::get($kind, 'main_image_2')); ?><?php else: ?><?php echo Utils::file_url(Arr::get($cms_page, 'main_image_2')); ?><?php endif; ?><?php endif; ?>) no-repeat; background-size: cover;">
       <div class="faq-intro-section ps-intro-section">
            <h1 class="faq-intro-title ps-intro-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?></h1>
            <div class="faq-intro-cnt ps-intro-cnt"><?php echo ($kind) ? $kind['content'] : Arr::get($cms_page, 'content'); ?></div>
            <?php if(empty($kind)): ?>
                <form class="faq-form pss-form" method="get">
                    <input class="faq-input pss-input" name="search_q" type="text" placeholder="<?php echo Arr::get($cmslabel, 'search_faq', 'Pretraživanje...'); ?>" />
                    <button class="btn btn-lightBlue faq-btn pss-btn" type="submit"><?php echo Arr::get($cmslabel, 'search_button', 'Traži'); ?></button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <div class="faq-c-content">
        <div class="faq-container wrapper">
            <?php if (sizeof($subcategories)): ?>
                <div class="p-items ps-items faq-items">
                    <?php foreach ($subcategories as $subcategory): ?>
                        <div href="<?php echo $subcategory['url']; ?>" class="pp pps faqc">
                            <figure class="faqc-image pp-image pps-image">
                                <a class="faqc-image-link" href="<?php echo $subcategory['url']; ?>">
                                    <img loading="lazy" data-lazy="<?php echo Thumb::generate($subcategory['main_image'], 480, 220, true, 'thumb', TRUE, '/media/images/no-image-475.webp'); ?>" <?php echo Thumb::generate($subcategory['main_image'], array('width' => 480, 'height' => 220, 'crop' => TRUE, 'default_image' => '/media/images/no-image-475.webp', 'placeholder' => '/media/images/no-image-475.webp')); ?> alt="<?php echo Text::meta($subcategory['title']); ?>" />
                                </a>
                                <a href="<?php echo $subcategory['url']; ?>" class="faqc-title pps-title<?php if (empty($subcategory['main_image'])): ?> faqc-title-no-image<?php endif; ?>"><?php echo $subcategory['title']; ?></a>
                            </figure>
                            <?php if (!empty($subcategory['children'])): ?>
                                <div class="faqc-short-desc pps-cnt">                                   
                                    <ul class="faqc-list<?php if(count($subcategory['children']) > 3): ?> column<?php endif; ?>">
                                        <?php foreach ($subcategory['children'] AS $subcategory_children): ?>                                          
                                            <li><a href="<?php echo $subcategory_children['url'];?>"><?php echo $subcategory_children['title'];?></a></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="p-empty wrapper">
                    <?php echo Arr::get($cmslabel, 'no_faq'); ?>
                </div>
            <?php endif; ?>
        </div>
   </div>

   <div class="faq-section wrapper">    
        <?php if(empty($kind)): ?>    
            <?php $faq_promos = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'faq_promo', 'limit' => 2)); ?>
            <?php if(!empty($faq_promos)): ?>
                <div class="faq-promos">
                    <?php foreach ($faq_promos as $faq_promo): ?>
                       <?php if($faq_promo['link']): ?><a href="<?php echo $faq_promo['link']; ?>" class="faq-promo faq-promo-link"<?php if($faq_promo['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php else: ?><div class="faq-promo"><?php endif; ?>
                            <?php if($faq_promo['image']): ?>
                                 <img loading="lazy" <?php echo Thumb::generate($faq_promo['image'], array('width' => 730, 'height' => 220, 'crop' => TRUE, 'html_tag' => TRUE, 'srcset' => '730c 2x')); ?> alt="<?php echo $faq_promo['title']; ?>" />
                            <?php endif; ?>
                        <?php if($faq_promo['link']): ?></a><?php else: ?></div><?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <div class="faq-bottom">
            <?php if(!empty($items)): ?>
                <div class="faq-bottom-col1">
                    <div class="faq-bottom-title"><?php echo Arr::get($cmslabel, 'top_faq'); ?></div>
                    <div class="faq-top-content">
                        <?php $i = 1; ?>
                        <?php foreach ($items as $top_question): ?>
                            <div class="fp<?php if($i == 1): ?> active<?php endif; ?>">
                                <a href="javascript:;" class="fp-title"><span></span><?php echo $top_question['title']; ?></a>
                                <div class="fp-cnt"><?php echo $top_question['content']; ?></div>
                            </div>
                            <?php $i++; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            <div class="faq-bottom-col2<?php if (empty($top_questions)): ?> no-top-faq<?php endif; ?>">
                <div class="faq-bottom-title"><?php echo Arr::get($cmslabel, 'info_faq'); ?></div>
                <div class="faq-contact faq-tel"><?php echo Arr::get($cmslabel, 'faq_tel'); ?></div>
                <div class="faq-contact faq-mail"><?php echo Arr::get($cmslabel, 'faq_mail'); ?></div>
                <div class="faq-contact faq-store"><?php echo Arr::get($cmslabel, 'faq_store'); ?></div>
            </div>
        </div>
   </div>
<?php $this->endblock('main'); ?>