<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta((!empty($kind['seo_title'])) ? $kind['seo_title'] : Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-faq-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?>
<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('title_container_class'); ?> special-title-container faq-title-container<?php $this->endblock('title_container_class'); ?>

<?php $this->block('h1'); ?>
    <h1><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('main_class'); ?> faq-main<?php $this->endblock('main_class'); ?>

<?php $this->block('content'); ?>
    <div class="faqd-short-desc"><?php echo ($kind) ? $kind['short_description'] : Arr::get($cms_page, 'short_description'); ?></div>
    <div class="faqd-desc"><?php echo ($kind) ? $kind['content'] : Arr::get($cms_page, 'content'); ?></div>
    
    <?php if($items): ?>
        <div class="faqd-content">
            <?php $i = 1; ?>
            <?php foreach ($items as $item): ?>
                <div class="fp<?php if($i == 1): ?> active<?php endif; ?>">
                    <a href="javascript:;" class="fp-title"><span></span><?php echo $item['title']; ?></a>
                    <div class="fp-cnt"><?php echo $item['content']; ?></div>
                </div>
                <?php $i++; ?>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
            <?php echo Arr::get($cmslabel, 'no_faq'); ?>
        <?php endif; ?>
<?php $this->endblock('content'); ?>

<?php $this->block('sidebar'); ?>
    <aside class="sidebar">
        <div class="sidebar-title"><?php echo Arr::get($cmslabel, 'sidebar_help'); ?></div>
        <ul class="nav-sidebar faq-sidebar">
            <?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
            <?php $faq_categories = Widget_Faq::categories(['lang' => $info['lang'], 'level_range' => '1.1']); ?>
            <?php $url_c = (!empty($kind['url'])) ? dirname($kind['url']).'/' : ''; ?>
            <?php foreach ($faq_categories as $faq_category): ?>
                <li>
                    <a href="<?php echo $faq_category['url']; ?>" <?php if($url_c == $faq_category['url']): ?>class="active"<?php endif; ?>><?php echo $faq_category['title']; ?></a>
                    <?php $faq_categories_lvl2 = Widget_Faq::categories(['lang' => $info['lang'], 'start_position' => $faq_category['position_h'], 'level_range' => '1.2']); ?>
                    <?php if($url_c == $faq_category['url'] AND $faq_categories_lvl2): ?>
                        <ul>
                            <?php foreach ($faq_categories_lvl2 as $faq_category_lvl2): ?>
                                <li>
                                   <a href="<?php echo $faq_category_lvl2['url']; ?>" <?php if($info['url'] == $faq_category_lvl2['url']): ?>class="active"<?php endif; ?>><?php echo $faq_category_lvl2['title']; ?></a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ul>
        <div class="support support-sidebar">
            <div class="support-title"><?php echo Arr::get($cmslabel, 'faq_support'); ?></div>
            <?php echo Arr::get($cmslabel, 'support'); ?>
        </div>
    </aside>
<?php $this->endblock('sidebar'); ?>

<?php $this->block('share'); ?>
     <?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('share'); ?>