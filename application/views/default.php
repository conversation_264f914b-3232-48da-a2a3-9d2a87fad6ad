<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>" data-token_id="<?php echo Utils::token_id(); ?>">
<head>
    <?php $display_gtm = (Kohana::$environment === 1 OR in_array($info['site_url'], ['https://bigbangmarkerdev.info', 'https://mp.bigbangmarkerdev.info', 'http://bigbang.marker', 'http://mpbigbang.marker']));  ?>
    <?php if ($display_gtm): ?>
		<script>
			var dataLayer = window.dataLayer || [];
			<?php if (isset($cookie_gdpr_statuses)): ?>
				dataLayer.push({<?php echo  Arr::implode_r(', ', $cookie_gdpr_statuses, true, ':'); ?>});
			<?php endif; ?>
		</script>
		<?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
    <?php endif; ?>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="format-detection" content="telephone=no">
	<meta name="author" content="Marker.hr">
	<link rel="dns-prefetch" href="//www.google-analytics.com">
	<link rel="dns-prefetch" href="//ssl.google-analytics.com">
	<link rel="dns-prefetch" href="//connect.facebook.net">
	<link rel="dns-prefetch" href="//static.ak.facebook.com">
	<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
	<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
	<link rel="dns-prefetch" href="//maps.gstatic.com">
	<link rel="dns-prefetch" href="//maps.google.com">
	<link rel="dns-prefetch" href="//maps.googleapis.com">
	<link rel="dns-prefetch" href="//mt0.googleapis.com">
	<link rel="dns-prefetch" href="//mt1.googleapis.com">
	
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<meta name="msapplication-TileColor" content="#005aa9">
	<meta name="theme-color" content="#ffffff">

	<link rel="preload" href="/media/fonts/icomoon52.woff?v2" as="font" type="font/woff" crossorigin="anonymous">
	<link rel="preload" href="/media/fonts/lato-semibold.woff" as="font" type="font/woff" crossorigin="anonymous">
	<link rel="preload" href="/media/fonts/lato-regular.woff" as="font" type="font/woff" crossorigin="anonymous">

	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php echo Html::media('css_gdefault'); ?>
    <?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="<?php echo $info['page_class']; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?>" data-module="<?php echo $info['controller']; ?>" data-fixed_header="<?php $this->block('page_fixed_header'); ?>145<?php $this->endblock('page_fixed_header'); ?>">
    <?php $this->block('extrabody_top'); ?><?php $this->endblock('extrabody_top'); ?>
	<?php if ($display_gtm): ?>
		<?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
	<?php endif; ?>

	<?php //echo Facebook::init('<App ID>', $info['lang']); ?>
	<div class="page-wrapper" id="top">
		<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
		<?php $pop_up = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'pop_up', 'limit' => 1]); ?>
		<?php if(!empty($pop_up)): ?>
			<?php foreach($pop_up as $pop_up_item): ?>
				<div class="pop-up-special" id="popUpSpecial">
					<div class="pop-up-special-close"></div>
					<div class="pop-up-special-content cms-content">
						<?php echo $pop_up_item['content']; ?>
					</div>
				</div>
			<?php endforeach; ?>
		<?php endif; ?>
		
		<?php $this->block('header'); ?>
			<header class="header">
				<?php $this->block('hello_bar'); ?>
					<?php $hello_bar = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1]); ?>
					<?php if(!empty($hello_bar)): ?>
						<div class="hello-bar" id="helloBar">
							<?php foreach($hello_bar as $hello_bar_item): ?>
								<style>
									<?php if(!empty($hello_bar_item['element_color_1'])): ?>
										.hello-bar-content{color: <?php echo($hello_bar_item['element_color_1']); ?>;}
										.hello-bar-content a{color: <?php echo($hello_bar_item['element_color_1']); ?>;}
										.hello-bar-content a:hover{color: <?php echo($hello_bar_item['element_color_1']); ?>;}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['element_color_4']) AND !empty($hello_bar_item['element_color_5'])): ?>
										.hello-bar{background: linear-gradient(180deg, <?php echo($hello_bar_item['element_color_4']); ?> 0%, <?php echo($hello_bar_item['element_color_5']); ?> 100%);;}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['element_color_2'])): ?>
										.hello-bar-close{background: <?php echo($hello_bar_item['element_color_2']); ?>;}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['element_color_3'])): ?>
										.hello-bar-close:before{color: <?php echo($hello_bar_item['element_color_3']); ?>;}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['image'])): ?>
										.hello-bar-content:before{content:''; background: url(<?php echo Utils::file_url($hello_bar_item['image']); ?>) no-repeat; background-size: contain; background-position: left bottom;}
										@media screen and (max-width: 760px) {
											.hello-bar-content:before{background-position: right bottom;}
										}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['image_3'])): ?>
										@media screen and (max-width: 760px) {
											.hello-bar-content:before{content:''; background: url(<?php echo Utils::file_url($hello_bar_item['image_3']); ?>) no-repeat; background-size: contain; background-position: right bottom;}
										}
									<?php endif; ?>
									<?php if(!empty($hello_bar_item['image_2'])): ?>
										.hello-bar-content:after{content:''; background: url(<?php echo Utils::file_url($hello_bar_item['image_2']); ?>) no-repeat; background-size: contain; background-position: right bottom;}
										@media screen and (max-width: 760px) {
											.hello-bar-content:after{content: none;}
										}
									<?php endif; ?>
								</style>
								<?php if(!empty($hello_bar_item['content'])): ?>
									<div class="hello-bar-content wrapper<?php if(empty($hello_bar_item['image']) AND empty($hello_bar_item['image_3'])): ?> no-image<?php endif; ?>">
										<?php echo($hello_bar_item['content']); ?>
									</div>
								<?php endif; ?>
								<div class="hello-bar-close"></div>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>
				<?php $this->endblock('hello_bar'); ?>

				<div class="header-top">
					<div class="wrapper">
						<a class="m-btn" href="javascript:void(0);"><span class="icon"></span></a>
						<a class="logo" href="<?php echo Utils::homepage($info['lang']); ?>" id="logo"></a>
						<span class="m-categories-back"></span>
						<span class="m-categories-title" data-menu_title="<?php echo Arr::get($cmslabel, 'header_categories'); ?>" data-main_menu_title="<?php echo Arr::get($cmslabel, 'header_menu'); ?>"><?php echo Arr::get($cmslabel, 'header_menu'); ?></span>
					
						<?php echo View::factory('search/widget/form'); ?>

						<div class="header-buttons">
							<div class="header-support">
								<div class="btn-header header-btn-support">
									<span class="btn-header-text"><?php echo Arr::get($cmslabel, 'header_btn_support'); ?></span>
								</div>
								<div class="header-support-box">
									<div class="header-support-box-col header-support-box-col1">
										<div class="header-support-contact user-support"><?php echo Arr::get($cmslabel, 'header_support_contact1'); ?></div>
										<div class="header-support-contact reclamation"><?php echo Arr::get($cmslabel, 'header_support_contact2'); ?></div>
									</div>
									<div class="header-support-box-col">
										<ul class="header-support-nav">
											<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'support_header']); ?>
										</ul>
									</div>
								</div>
							</div>

							<?php echo View::factory('auth/widget/user_box'); ?>
							<?php //echo View::factory('catalog/widget/wishlist'); ?>
							<?php echo View::factory('catalog/widget/compare'); ?>
							<?php echo View::factory('webshop/widget/shopping_cart'); ?>
						</div>
					</div>
				</div>
				
				<?php $this->block('header_bottom'); ?>
					<div class="header-bottom">
						<?php
						$cache_entry_id = Cache::generate_entry_id('vcms_widget_header', 'cms_widget_header'.$info['lang']);
						$cms_widget_header = '';
						try {
							$cms_widget_header = Cache::instance()->get($cache_entry_id);
						} catch (Exception $e) {

						}

						if (empty($cms_widget_header)) {
							$cms_widget_header = View::factory('cms/widget/header');
							Cache::instance()->set($cache_entry_id, (string) $cms_widget_header, 60); // 60 seconds cache
						}
						echo $cms_widget_header;

						?>
					</div>
				<?php $this->endblock('header_bottom'); ?>
				<?php $this->block('header_landing'); ?> <?php $this->endblock('header_landing'); ?>
			</header>
			<div class="header-placeholder"></div>
		<?php $this->endblock('header'); ?>

		<?php $this->block('breadcrumb_section'); ?>
			<div class="bc">
				<div class="wrapper wrapper-bc">
					<?php $this->block('breadcrumb'); ?>
						<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
						<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
					<?php $this->endblock('breadcrumb'); ?>
				</div>
			</div>
		<?php $this->endblock('breadcrumb_section'); ?>
				
		<?php $this->block('main'); ?>
			<div class="main">
				<?php $this->block('main_header'); ?>
					<?php $this->block('main_header_style'); ?><?php $this->endblock('main_header_style'); ?>
					<div class="main-header">
						<div class="wrapper">
							<?php $this->block('h1'); ?><?php $this->endblock('h1'); ?>
						</div>
					</div>
				<?php $this->endblock('main_header'); ?>
							
				<?php $this->block('content_layout'); ?>
					<div class="main-content">
						<div class="wrapper">
							<div class="cms-content">
								<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
							</div>
						
							<?php $this->block('sidebar'); ?>
								<aside class="sidebar">
									<div class="sidebar-title"><?php echo Arr::get($cmslabel, 'about_us'); ?></div>
									<ul class="nav-sidebar">
										<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'sidebar', 'selected' => $active_menu_item]); ?>
									</ul>
									<div class="support support-sidebar">
										<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
										<?php echo Arr::get($cmslabel, 'support'); ?>
										<div class="faq-link"><?php echo Arr::get($cmslabel, 'faq'); ?></div>
									</div>
								</aside>
							<?php $this->endblock('sidebar'); ?>
						</div>
						<?php $this->block('share'); ?><?php $this->endblock('share'); ?>
					</div>
				<?php $this->endblock('content_layout'); ?>
				<?php $this->block('main_extra'); ?> <?php $this->endblock('main_extra'); ?>
			</div>
		<?php $this->endblock('main'); ?>

		<?php $this->block('map'); ?><?php $this->endblock('map'); ?>

		<?php /*$this->block('benefits'); ?>
			<?php $benefits = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'benefits', 'limit' => 5)); ?>
			<?php if($benefits): ?>
				<div class="benefits">
					<div class="wrapper wrapper-benefits">
						<?php foreach ($benefits as $benefit_item): ?>
							<div class="benefit">
								<?php if($benefit_item['link']): ?><a href="<?php echo $benefit_item['link']; ?>"<?php if($benefit_item['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php endif; ?>
									<?php if(!empty($benefit_item['image'])): ?>
										<div class="benefit-img">
											<img data-css="lazyload" data-original="<?php echo Utils::file_url($benefit_item['image']); ?>" src="/media/images/no-image-50.jpg" alt="<?php echo $benefit_item['title']; ?>"/>
										</div>
									<?php endif; ?>
									<?php if($benefit_item['title']): ?>
										<div class="benefit-title"><?php echo $benefit_item['title']; ?></div>
									<?php endif; ?>
								<?php if($benefit_item['link']): ?></a><?php endif; ?>
							</div>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>
		<?php $this->endblock('benefits');*/ ?>

		<?php $this->block('nw'); ?>
			<?php echo View::factory('newsletter/widget/manage'); ?>	
		<?php $this->endblock('nw'); ?>

		<?php $this->block('footer'); ?>	
			<footer class="footer">
				<div class="wrapper wrapper-footer">
					<div class="footer-row footer-row1">
						<div class="footer-col footer-col1">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title1'); ?></div>
							<ul class="nav nav-footer">
								<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'footer_col1', 'selected' => $active_menu_item]); ?>
							</ul>
						</div>
						<div class="footer-col footer-col2">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title2'); ?></div>
							<ul class="nav nav-footer">
								<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'footer_col2', 'selected' => $active_menu_item]); ?>
								<li><a href="javascript:void(0);" class="gdpr_configurator_button" id="gdpr_configurator_button"><?php echo Arr::get($cmslabel, 'gdpr_edit', 'Uredi privole'); ?></a></li>
							</ul>
						</div>
						<div class="footer-col footer-col3">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title3'); ?></div>
							<ul class="nav nav-footer">
								<?php echo Widget_Cms::menu(['lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'footer_col3', 'selected' => $active_menu_item]); ?>
							</ul>
							<?php /* ?>
								<div class="footer-payment">
									<?php echo Arr::get($cmslabel, 'payment_info_note'); ?>
								</div>
							<?php */ ?>
							<div class="cards">
								<?php echo Arr::get($cmslabel, 'cards'); ?>
							</div>
						</div>
						<div class="footer-col footer-col4">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
							<div class="support footer-support">
								<?php echo Arr::get($cmslabel, 'support'); ?>
							</div>
							<div class="complaints">
								<div class="complaints-title"><?php echo Arr::get($cmslabel, 'complaints'); ?></div>
								<div><?php echo Arr::get($cmslabel, 'complaints_tel'); ?></div>								
							</div>	
						</div>
					</div>
					
					<div class="footer-row footer-row2">
						<div class="footer-col footer-col1">
							<span class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></span>
							<span class="copy copy-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
						</div>
						<div class="footer-col footer-col2">
							<span class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></span>
						</div>
						<div class="footer-col footer-col3">
							<div class="footer-badges">
								<div class="footer-trustmark smdWrapperTag"></div>
								<?php echo Arr::get($cmslabel, 'footer_badges'); ?>
							</div>
						</div>
						<div class="footer-col footer-col4">
							<div class="social">
								<?php echo Arr::get($cmslabel, 'social'); ?>
							</div>
						</div>
					</div>
				</div>
				
				<a class="to-top" href="#top"></a>
			</footer>
			<?php if(!empty(Arr::get($cmslabel, 'eu'))): ?>
				<div class="eu">
					<div class="wrapper eu-wrapper">
						<?php echo Arr::get($cmslabel, 'eu'); ?>
					</div>
				</div>
			<?php endif; ?>
		<?php $this->endblock('footer'); ?>
	</div>
	
	<?php echo View::factory('cms/widget/support_details'); ?>
	<?php echo Html::media('js_gdefault'); ?>
	<?php echo View::factory('gdpr/widget/configurator'); ?>
	<?php //echo Html::media('js_gcookie'); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
	<?php $newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list')); ?>
	<?php //echo View::factory('newsletter/widget/manage_leaving', ['newsletter_form' => $newsletter_form]); ?>
    <?php echo View::factory('auth/widget/keycloak'); ?>
	<?php echo View::factory('admin/widget_fe/toolbar'); ?>

	<?php
	$chat_active = true;
	$chat_holidays = explode(PHP_EOL, Arr::get($appconfig, 'chat_holiday'));
	if ($chat_holidays) {
		natsort($chat_holidays);
		if (in_array(date('Y-m-d'), $chat_holidays)) {
			$chat_active = false;
		}
	}

	if ($chat_active) {
		$chat_working_hour = explode(PHP_EOL, Arr::get($appconfig, 'chat_working_hour'));
		if ($chat_working_hour) {
			$chat_day = strtolower(date('D'));
			foreach ($chat_working_hour AS $chat_working_hour_single) {
				if (strpos($chat_working_hour_single, $chat_day.'|') !== false) {
					$chat_working_hour_single = substr($chat_working_hour_single, 4);
					if (strpos($chat_working_hour_single, '-') !== false) {
						$chat_time = (int) date('Hi');
						list($chat_working_hour_single_from, $chat_working_hour_single_to) = explode('-', str_replace(':', '', $chat_working_hour_single));
						if ($chat_time < (int)$chat_working_hour_single_from OR $chat_time > (int)$chat_working_hour_single_to) {
							$chat_active = false;
						}
					} else {
						$chat_active = false;
					}
				}
			}
		}
	}
	?>
	<?php if ($chat_active): ?>
		<a href="javascript:void(0);" id="chatActive" class="el-chat"></a>
	<?php endif; ?>

	<?php if ($info['user_id'] == 1 AND (int)Arr::get($_GET, 'cmsdebug', 0) === 1): ?><?php echo '<br/ class="clear">' . View::factory('profiler/stats'); ?><?php endif; ?>

</body>
</html>