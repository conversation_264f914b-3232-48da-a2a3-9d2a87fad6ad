<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-contact page-stores<?php $this->endblock('page_class'); ?>

<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="main-header main-header-stores">
			<div class="wrapper-small">
				<?php $this->block('h1'); ?>
					<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
				<?php $this->endblock('h1'); ?>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
	<?php if(!empty($cms_page['content'])): ?>
		<div class="content-intro stores-intro wrapper-small"><?php echo Arr::get($cms_page, 'content'); ?></div>
	<?php endif; ?>

    <?php $map_points = Widget_Location::points(array('lang' => $info['lang'], 'only_visible_locationpage' => true)); ?>
	<?php if(!empty($map_points)): ?>
		<div class="city-list stores-city-list wrapper-small">
			<?php 
			$city = ''; 
			$city_id = '';
			$city_array = array(); 
			$city_points = array(); 
			foreach ($map_points as $point) {
				$city = $point['headline']; 
				if (empty($city)) {
					continue;
				}

				if(!in_array($city, $city_array)){
					$city_array[] = $city; 
					$city_points[$city] = 1;
				} else {
					$city_points[$city] += 1;
				}
			} 
			?>
			
			<?php foreach ($city_array as $city_item): ?>
				<a href="#<?php echo str_replace(' ', '', $city_item); ?>"><span><?php echo $city_item; ?></span></a>
			<?php endforeach; ?>

		</div>

		<div class="map-content map-content-stores" id="map-content">
			<?php echo View::factory('location/widget/map', ['map_points' => $map_points]); ?>
		</div>	
		
		<div class="stores-city-content">
			<div class="wrapper-small">
				<?php $i = 1; ?>
				<?php foreach ($city_array as $city_item): ?>
					<div class="sc-section">
						<div class="sc-header">	
							<div class="sc-title" id="<?php echo str_replace(' ', '', $city_item); ?>">
								<?php echo $city_item; ?>
								<span>(<?php echo Arr::get($city_points, $city_item); ?>)</span>
							</div>
							<a class="btn-stores-details" href="javascript:">
								<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'btn_stores_details', 'Pokaži podrobnosti'); ?></span>
								<span class="btn-active"><?php echo Arr::get($cmslabel, 'btn_hide_stores_details', 'Skri podrobnosti'); ?></span>
							</a>
						</div>
						<div class="sc-cnt">
							<?php foreach ($map_points as $item): ?>
								<?php if($item['headline'] == $city_item): ?>
									<div class="sc-row<?php if(empty($item['main_image'])): ?> no-image<?php endif; ?>" id="locationpoint-<?php echo $item['id']; ?>">
										<?php if(!empty($item['main_image'])): ?>
											<div class="sc-left">
												<figure class="sc-item-image lloader">
													<span><img loading="lazy" data-src="<?php echo Thumb::generate($item['main_image'], 640, 320, TRUE, 'thumb', TRUE, '/media/images/no-image-640.webp'); ?>" <?php echo Thumb::generate($item['main_image'], ['width' => 640, 'height' => 320, 'crop' => TRUE, 'default_image' => '/media/images/no-image-640.webp', 'placeholder' => '/media/images/no-image-640.webp', 'srcset' => '1280c 2x']); ?> alt="<?php echo Text::meta($item['title']); ?>" /></span>
												</figure>
											</div>
										<?php endif; ?>
										<div class="sc-right">
											<div class="sc-item-title"><?php echo $item['title']; ?></div>
											<?php if($item['address']): ?>
												<a href="#map-content" class="sc-item-address"><?php echo $item['address']; ?></a>
											<?php endif; ?>
											<?php if($item['business_hour']): ?>
												<div class="sc-item-title sc-title-business-time"><?php echo Arr::get($cmslabel, 'business_hour_title'); ?></div>
												<div class="sc-item-business-time"><?php echo $item['business_hour']; ?></div>
											<?php endif; ?>
											<?php if($item['contact']): ?>
												<div class="sc-item-contact"><?php echo $item['contact']; ?></div>
											<?php endif; ?>
										</div>
									</div>
								<?php endif; ?>	
							<?php endforeach; ?>
						</div>
					</div>
					<?php $i++; ?>
				<?php endforeach; ?>
			</div>
		</div>
	<?php else: ?>
		<div class="wrapper no-locations-label"><?php echo Arr::get($cmslabel, 'no_locations'); ?></div>
	<?php endif; ?>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('share'); ?> <?php $this->endblock('share'); ?>
<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>

<?php $this->block('extrabody'); ?>
	<?php if ($map_points): ?>
		<?php echo Html::media('cmslocation,infobox', 'js'); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>