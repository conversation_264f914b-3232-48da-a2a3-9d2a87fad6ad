<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>
<?php if ($newsletter_form): ?>
<div class="nw">
	<div class="wrapper wrapper-nw">
		<!-- Newsletter Subscribe -->
		<div class="nw-col nw-col1">
			<div class="nw-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
		</div>
		<div class="nw-col nw-col2">
			<form class="nw-form" action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>" data-siteform_gtm_event_tracking="newsletter">
				<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
				<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>"  />
				<input type="hidden" name="first_name" value="" />
				<input type="hidden" name="last_name" value="" />
				<input class="nw-input" type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" />
				<button class="nw-button btn-lightBlue" type="submit"><?php echo Arr::get($cmslabel, 'newsletter_signup', 'Pošlji'); ?></button>
				<div id="field-error-email" class="nw-error newsletter-error" style="display: none;"></div>
				<div class="nw-note"><?php echo Arr::get($cmslabel, 'newsletter_note'); ?></div>

				<?php if (!empty($newsletter_form['gdpr_accept_label'])): ?>
					<div class="nw-checkbox">
						<input type="hidden" name="gdpr_accept" value="0" />
						<input type="checkbox" name="gdpr_accept" value="1" id="gdpr_accept-1" />
						<label for="gdpr_accept-1"><?php echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></label>
						<span id="field-error-newsletter_gdpr_accept" class="error gdpr_accept-error" style="display: none"></span>
					</div>
				<?php endif; ?>

				<?php 
				$gdpr_template_api = App::config('bigbangsigdpr_api_newsletter');
				$gdpr_template_api = (!empty($gdpr_template_api)) ? json_decode($gdpr_template_api, true) : [];
				?>
				<?php if (!empty($gdpr_template_api)): ?>
					<div class="nw-gdpr">
						<div class="nw-gdpr-note">
							<?php echo $gdpr_template_api['content']['header']; ?>
						</div>
						<div class="nw-gdpr-checkbox">
							<p class="field field-gdpr_template_api">
								<input type="checkbox" name="gdpr_template_api_all-newsletter_manage" value="1" id="newsletter_manage-gdpr_template_api_all" />
								<label for="newsletter_manage-gdpr_template_api_all"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?></label>
								<span class="signup-desc nw-desc active">
									<span class="signup-desc-cnt nw-desc-cnt signup-desc-cnt-special"><?php echo Arr::get($cmslabel, 'gdpr_select_all_tips'); ?></span>
								</span>
							</p>
							<?php foreach ($gdpr_template_api['objects'] AS $gdpr_template_api_object): ?>
								<p class="field field-gdpr_template_api">
									<input type="checkbox" name="gdpr_template_api[]" value="<?php echo $gdpr_template_api_object['code']; ?>" id="newsletter_manage-gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>" <?php if (!empty($gdpr_template_api_object['subscribed'])): ?>checked<?php endif; ?> />
									<label for="newsletter_manage-gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>"><?php echo $gdpr_template_api_object['title']; ?></label>
									<span class="signup-desc nw-desc">
										<span class="signup-desc-link nw-desc-link">
											<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'gdpr_show_more'); ?></span>
											<span class="btn-active"><?php echo Arr::get($cmslabel, 'gdpr_hidde_more'); ?></span>				
										</span>
										<span class="signup-desc-cnt nw-desc-cnt"><?php echo $gdpr_template_api_object['content']; ?></span>
									</span>
								</p>
							<?php endforeach; ?>
						</div>
						<div class="nw-gdpr-note">
							<?php echo $gdpr_template_api['content']['footer']; ?>
						</div>
					</div>
				<?php endif; ?>
			</form>
			<div class="nw-success newsletter_subscribe_success" style="display: none;"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
		</div>
	</div>
</div>
<?php endif; ?>