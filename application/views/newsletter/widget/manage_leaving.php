<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>
<?php if ($newsletter_form): ?>
<div id="newsletter_popup" style="display: none">
	<div class="nl-popup">
		<div class="nw nw-leaving">
			<a class="nw-leaving-close" href="javascript:parent.$.fancybox.close();"><?php echo Arr::get($cmslabel, 'newsletters_leaving_close'); ?></a>
			<div class="nw-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
			<div class="nw-cnt">
				<form class="nw-popup-form" novalidate action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>_popup" data-newsletter_leaving="<?php echo $newsletter_form['url_manage']; ?>" data-newsletter_leaving_ignores="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>*">
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
					<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>"  />
					<input type="hidden" name="event" value="<?php echo Arr::get($_GET, 'event', 'leave'); ?>" />
					<input type="hidden" name="first_name" value="" />
					<input type="hidden" name="last_name" value="" />
					<input class="nw-input nw-leaving-input" type="email" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" />
					<button class="nw-button" type="submit"><span><?php echo Arr::get($cmslabel, 'send', 'Pošalji'); ?></span></button>
					<div id="field-error-email" class="nw-error nw-leaving-error" style="display: none"></div>
					
					<?php 
					$gdpr_template_api = App::config('bigbangsigdpr_api_newsletter');
					$gdpr_template_api = (!empty($gdpr_template_api)) ? json_decode($gdpr_template_api, true) : [];
					?>
					<?php if (!empty($gdpr_template_api)): ?>
						<div class="nw-gdpr-note">
							<?php echo $gdpr_template_api['content']['header']; ?>
						</div>
						<div class="nw-gdpr-checkbox nw-leaving-gdpr-checkbox">
							<p class="field field-gdpr_template_api">
								<input type="checkbox" name="gdpr_template_api_all-newsletter_manage_leaving" value="1" id="newsletter_manage_leaving-gdpr_template_api_all" />
								<label for="newsletter_manage_leaving-gdpr_template_api_all"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?></label>
								<span class="signup-desc nw-desc active">
									<span class="signup-desc-cnt nw-desc-cnt signup-desc-cnt-special"><?php echo Arr::get($cmslabel, 'gdpr_select_all_tips'); ?></span>
								</span>
							</p>						
							<?php foreach ($gdpr_template_api['objects'] AS $gdpr_template_api_object): ?>
								<p class="field field-gdpr_template_api">
									<input type="checkbox" name="gdpr_template_api[]" value="<?php echo $gdpr_template_api_object['code']; ?>" id="newsletter_manage_leaving-gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>" <?php if (!empty($gdpr_template_api_object['subscribed'])): ?>checked<?php endif; ?> />
									<label for="newsletter_manage_leaving-gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>"><?php echo $gdpr_template_api_object['title']; ?></label>
									<span class="signup-desc nw-desc">
										<span class="signup-desc-link nw-desc-link">
											<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'gdpr_show_more'); ?></span>
											<span class="btn-active"><?php echo Arr::get($cmslabel, 'gdpr_hidde_more'); ?></span>				
										</span>
										<span class="signup-desc-cnt nw-desc-cnt"><?php echo $gdpr_template_api_object['content']; ?></span>
									</span>
								</p>
							<?php endforeach; ?>
						</div>
						<div class="nw-gdpr-note">
							<?php echo $gdpr_template_api['content']['footer']; ?>
						</div>
					<?php endif; ?>					
				</form>
				<div class="nw-success nw-leaving-success newsletter_subscribe_success" style="display: none"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
			</div>
		</div>
	</div>
</div>		
<?php endif; ?>