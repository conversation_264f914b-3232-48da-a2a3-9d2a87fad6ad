<?php if (sizeof($items)): ?>
	<div class="p-items" id="items_<?php echo $kind['code']; ?>" data-infinitescroll="items_<?php echo $kind['code']; ?>" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="1">
		<?php if($kind['level'] == 1): ?>
			<?php echo View::factory('publish/index_entry', ['items' => $items]); ?>
		<?php elseif($kind['level'] == 2): ?>
			<?php echo View::factory('publish/index_entry', ['items' => $items, 'mode' => 'publish_speciallist']); ?>
		<?php endif; ?>
	</div>

	<?php echo $pagination; ?>
	<?php if ($pagination): ?>
		<div class="load-more-container">
			<a href="javascript:void(0);" class="btn btn-white btn-medium load-more btn-load-more" style="display: none"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_publish')); ?></a>
		</div>
	<?php endif; ?>
<?php else: ?>
	<div class="p-empty">
		<?php echo Arr::get($cmslabel, 'no_publish'); ?>
	</div>
<?php endif; ?>