<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main'); ?>
<div class="p-header">
	<div class="wrapper">
		<h1><?php echo $kind['seo_h1']; ?></h1>
		<div class="p-categories">
			<?php $publish_categories = Widget_Publish::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => '01')); ?>
			<?php foreach ($publish_categories as $publish_category): ?>
				<?php if($publish_category['url'] != $info['url']): ?>
					<a class="p-category <?php if ($info['url'] == $publish_category['url']): ?> active<?php endif; ?><?php if(!$publish_category['main_image']): ?> no-image<?php endif; ?>" href="<?php echo $publish_category['url']; ?>">
						<span><?php echo $publish_category['title']; ?></span>
						<?php if($publish_category['main_image']): ?>
							<span class="p-category-img"><img loading="lazy" src="<?php echo Utils::file_url($publish_category['main_image']); ?>" alt="<?php echo $publish_category['title']; ?>"/></span>
						<?php endif; ?>
					</a>
				<?php endif; ?>
			<?php endforeach; ?>
			<?php if (!empty($kind['parents'])): ?>
				<a class="p-btn-all" href="<?php echo reset($kind['parents'])['url']; ?>"><span><?php echo Arr::get($cmslabel, 'publish_categories_show_all'); ?></span></a>
			<?php endif; ?>
		</div>
	</div>
</div>

<div class="wrapper">
	<?php if($kind['level'] == 1): ?>
		<?php $featured_item = Widget_Publish::publishes(array('lang' => $info['lang'], 'category_code' => 'info', 'single' => 1, 'extra_fields' => ['short_description'])); ?>
		<div class="p-featured">
			<?php echo View::factory('publish/index_entry_big', ['item' => $featured_item]); ?>
		</div>
	<?php endif; ?>
</div>

<?php if($kind['level'] == 1): ?>
	<?php $publish_speciallist = Widget_Publish::speciallist($info['lang'], 'top_articles', true); ?>
	<?php $publish_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'list_code' => $publish_speciallist['code'], 'sort' => 'list_position', 'limit' => 12, 'extra_fields' => ['short_description'])); ?>
	<?php if($publish_items): ?>
		<div class="pp-speciallist">
			<div class="wrapper">	
				<div class="pp-speciallist-cnt">
					<div class="pp-speciallist-title"><?php echo Arr::get($cmslabel, 'top_articles'); ?></div>
					<div class="pp-speciallist-slider">
						<?php echo View::factory('publish/index_entry', ['items' => $publish_items, 'mode' => 'publish_speciallist']); ?>
					</div>
				</div>
			</div>
		</div>
	<?php endif; ?>
<?php endif; ?>

<div class="wrapper">
	<div id="items_<?php echo $kind['code']; ?>_layout">
		<?php echo View::factory('publish/index_layout', [
			'kind' => $kind,
			'q' => '',
			'items' => $items,
			'items_per_page' => $items_per_page,
			'items_all' => $items_all,
			'items_total' => $items_total,
			'child_categories' => $child_categories,
			'child_categories_published' => $child_categories_published,
			'pagination' => $pagination,
			]); ?>
	</div>
</div>
<?php $this->endblock('main'); ?>