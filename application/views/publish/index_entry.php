<?php $mode = (isset($mode)) ? $mode : ''; ?>

<?php $i = 1; ?>
<?php foreach ($items as $item): ?>
	<?php if($mode == 'search'): ?>
		<?php echo View::factory('publish/index_entry_small', array('item' => $item, 'mode' => $mode)); ?>
	<?php elseif($i == 1 AND ($mode == 'pd_related' OR $mode == 'publish_speciallist')): ?>
		<?php echo View::factory('publish/index_entry_big', array('item' => $item, 'mode' => $mode)); ?>
	<?php elseif($i > 1): ?>
		<?php echo View::factory('publish/index_entry_small', array('item' => $item, 'mode' => $mode)); ?>
	<?php endif; ?>
	<?php $i++; ?>
<?php endforeach; ?>