<?php $mode = (isset($mode)) ? $mode : ''; ?>

<?php foreach ($items as $item): ?>
<?php if($mode == 'sidebar'): ?><li><?php endif; ?>
	<a href="<?php echo $item['url']; ?>" class="<?php if($mode != 'sidebar'): ?>pps-list-title<?php endif; ?><?php if($item['url'] == $info['url']): ?> active<?php endif; ?>" >
		<?php if($mode == 'menu' && $item['author_image']): ?>
			<span class="pps-list-img">
				<?php $fileExtension = pathinfo($item['author_image'], PATHINFO_EXTENSION); ?>
				<?php if($fileExtension == 'svg'): ?>
					<img loading="lazy" src="<?php echo Utils::file_url($item['author_image']); ?>" alt="<?php echo $item['title']; ?>">
				<?php else: ?>
					<img loading="lazy" <?php echo Thumb::generate($item['author_image'], array('width' => 36, 'height' => 36, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($item['title']); ?>" />
				<?php endif; ?>
			</span>
		<?php endif; ?>
		<span><?php echo $item['title']; ?></span>
	</a>
<?php if($mode == 'sidebar'): ?></li><?php endif; ?>
<?php endforeach; ?>