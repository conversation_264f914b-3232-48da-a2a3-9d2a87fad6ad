<?php foreach ($items as $item): ?>
<a href="<?php echo $item['url']; ?>" class="pp pps special<?php if(!$item['main_image']): ?> pps-no-img<?php endif; ?>">
	<figure class="pp-image pps-image lloader">
		<span class="pps-image-link">
			<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 480, 'height' => 220, 'crop' => TRUE, 'default_image' => '/media/images/no-image-480.webp', 'placeholder' => '/media/images/no-image-480.webp', 'srcset' => '960c 2x')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
		</span>
		<div class="pps-title"><?php echo $item['title']; ?></div>
	</figure>
	<?php if(!empty($item['short_description'])): ?>
		<div class="pps-cnt">
			<div class="pps-short-desc"><?php echo Text::limit_words(strip_tags($item['short_description']), 25, '...'); ?></div>
			<div class="pps-btn-cnt">
				<div class="pps-btn btn btn-lightBlue btn-medium"><?php echo Arr::get($cmslabel, 'show_more'); ?></div>
			</div>
		</div>
	<?php endif; ?>
</a>
<?php endforeach; ?>