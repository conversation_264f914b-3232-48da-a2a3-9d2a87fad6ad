<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-services-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="main-header">
			<div class="wrapper">
				<?php $this->block('h1'); ?>
					<h1><?php echo Arr::get($item, 'seo_h1'); ?></h1>
				<?php $this->endblock('h1'); ?>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content'); ?>
	<?php echo Arr::get($item, 'short_description'); ?>
	<?php echo Arr::get($item, 'content'); ?>
	<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
	<?php if ($images): ?>
		<div class="pd-thumbs">
			<?php foreach ($images as $file): ?>
				<a href="<?php echo $file['url']; ?>" class="fancybox" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
					<img loading="lazy" <?php echo Thumb::generate($file['file'], ['width' => 920, 'default_image' => '/media/images/no-image-920.webp', 'html_tag' => true, 'srcset' => '1840c 2x']); ?> alt="<?php echo Text::meta($file['description']); ?>" />
				</a>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>

<?php $this->endblock('content'); ?>

<?php $this->block('share'); ?>
	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('share'); ?>

<?php $this->block('sidebar'); ?>
	<aside class="sidebar">
		<?php $services = Widget_Publish::category($info['lang'], 'services'); ?>
		<?php $categories = Widget_Publish::categories(array('lang' => $info['lang'], 'start_position' => $services['position_h'])); ?>
		<div class="sidebar-title sidebar-title-services"><a href="<?php echo $services['url']; ?>"><?php echo Arr::get($cmslabel, 'services'); ?></a></div>
		<ul class="nav-sidebar nav-sidebar-services">
			<?php foreach($categories as $category): ?>
				<li>
					<a href="<?php echo $category['url']; ?>"<?php if($item['category_url'] == $category['url']): ?> class="active"<?php endif; ?>><?php echo $category['title']; ?></a>
					<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_position ' => '03', 'category_code_only' => $category['code'], 'limit' => 0]); ?>
					<?php if ($related_items): ?>
						<ul class="nav-sidebar-services-lvl2">
							<?php echo View::factory('publish/services/index_entry_small', ['items' => $related_items, 'mode' => 'sidebar']); ?>
						</ul>
					<?php endif; ?>
				</li>
			<?php endforeach; ?>
		</ul>
	</aside>
<?php $this->endblock('sidebar'); ?>
