<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-services<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main'); ?>
	<?php $services = Widget_Publish::category($info['lang'], 'services'); ?>

	<div class="ps-intro" style="background: url(<?php if(!empty($kind) AND !empty($kind['main_image_2']) AND $info['user_device'] == 'm'): ?><?php echo Utils::file_url(Arr::get($kind, 'main_image_2')); ?><?php else: ?><?php echo Utils::file_url(Arr::get($kind, 'main_image')); ?><?php endif; ?>) no-repeat; background-size: cover;">
		<?php if(!empty($kind) AND $kind['level'] > 1): ?>
			<a class="btn btn-white ps-intro-btn" href="<?php echo $services['url']; ?>"><span><?php echo Arr::get($cmslabel, 'all_services'); ?></span></a>
		<?php endif; ?>
		<div class="ps-intro-section">
			<h1 class="ps-intro-title"><?php echo $kind['seo_h1']; ?></h1>
			<?php if(!empty($kind) AND!empty($kind['short_description'])): ?>
				<div class="ps-intro-cnt"><?php echo $kind['short_description']; ?></div>
			<?php endif; ?>
			<?php /* ?>
			<form class="pss-form" method="get" form="?">
				<input class="pss-input" name="search_q" type="text" placeholder="<?php echo Arr::get($cmslabel, 'search_storitve', 'Pretraživanje...'); ?>" />
				<button class="btn btn-lightBlue pss-btn" type="submit"><?php echo Arr::get($cmslabel, 'search_button', 'Traži'); ?></button>
			</form>
			<?php */ ?>
		</div>
	</div>
	<div class="ps-items-bg" id="items_<?php echo $kind['code']; ?>_layout">
		<div class="wrapper">
			<?php if(!empty($kind) AND $kind['level'] == 1): ?>
				<?php $categories = Widget_Publish::categories(array('lang' => $info['lang'], 'extra_fields' => ['content'], 'extra_fields' => ['short_description'], 'start_position' => $services['position_h'])); ?>
				<div class="p-items ps-items">
					<?php foreach($categories as $category): ?>
						<div class="pp pps<?php if(!$category['main_image']): ?> pps-no-img<?php endif; ?>">
							<figure class="pp-image pps-image lloader">
								<a class="pps-image-link" href="<?php echo $category['url']; ?>">
									<img loading="lazy" <?php echo Thumb::generate($category['main_image'], array('width' => 480, 'height' => 220, 'crop' => TRUE, 'default_image' => '/media/images/no-image-480.webp', 'placeholder' => '/media/images/no-image-480.webp', 'srcset' => '960c 2x')); ?> title="<?php echo Text::meta($category['title']); ?>" alt="<?php echo Text::meta($category['title']); ?>" />
								</a>
								<?php if(!empty($category['title'])): ?>
									<a href="<?php echo $category['url']; ?>" class="pps-title"><?php echo $category['title']; ?></a>
								<?php endif; ?>
							</figure>
							<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_position ' => '03', 'category_code_only' => $category['code'], 'limit' => 0]); ?>
							<?php if(!empty($category['short_description']) OR !empty($related_items)): ?>
								<div class="pps-cnt">
									<?php if(!empty($category['short_description'])): ?>
										<div class="pps-short-desc"><?php echo Text::limit_words(strip_tags($category['short_description']), 22, '...'); ?></div>
									<?php endif; ?>
									<?php if ($related_items): ?>
										<div class="pps-list">
											<?php echo View::factory('publish/services/index_entry_small', ['items' => $related_items]); ?>
										</div>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
			<?php if(!empty($kind) AND $kind['level'] > 1): ?>
				<?php echo View::factory('publish/services/index_layout', [
					'kind' => $kind,
					'q' => '',
					'items' => $items,
					'items_per_page' => $items_per_page,
					'items_all' => $items_all,
					'items_total' => $items_total,
					'child_categories' => $child_categories,
					'child_categories_published' => $child_categories_published,
					'pagination' => $pagination,
					]); ?>
			<?php endif; ?>
		</div>
	</div>

	<?php if(!empty($kind) AND !empty($kind['content'])): ?>
		<div class="faq-section ps-extra-content wrapper">
			<div class="faq-bottom ps-extra-bottom">
					<div class="faq-bottom-col1 ps-extra-col1 cms-content">
						<?php echo $kind['content']; ?>
					</div>
				<div class="faq-bottom-col2 ps-extra-col2">
					<?php $services = Widget_Publish::category($info['lang'], 'services'); ?>
					<?php $categories = Widget_Publish::categories(array('lang' => $info['lang'], 'start_position' => $services['position_h'])); ?>
					<div class="sidebar-title sidebar-title-services"><a href="<?php echo $services['url']; ?>"><?php echo Arr::get($cmslabel, 'services'); ?></a></div>
					<ul class="nav-sidebar nav-sidebar-services">
						<?php foreach($categories as $category): ?>
							<li>
								<a href="<?php echo $category['url']; ?>"<?php if($info['url'] == $category['url']): ?> class="active"<?php endif; ?>><?php echo $category['title']; ?></a>
								<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_position ' => '03', 'category_code_only' => $category['code'], 'limit' => 0]); ?>
								<?php if ($related_items): ?>
									<ul class="nav-sidebar-services-lvl2">
										<?php echo View::factory('publish/services/index_entry_small', ['items' => $related_items, 'mode' => 'sidebar']); ?>
									</ul>
								<?php endif; ?>
							</li>
						<?php endforeach; ?>
					</ul>
				</div>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('main'); ?>