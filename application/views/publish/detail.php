<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-publish-detail<?php $this->endblock('page_class'); ?>


<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="pd-header">
			<div class="pd-wrapper">
				<a class="pd-category" href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
				<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>
				<div class="pd-header-bottom">
					<!-- Post star rating -->
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<div class="pd-header-rate-section">
							<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
							<span class="pd-comment-average special"><?php echo number_format($item['feedback_rate_widget']['rates'], 1); ?></span>
						</div>
					<?php endif; ?>

					<!-- Post comments counter -->
					<?php if (isset($item['feedback_comment_widget'])): ?>
					<div class="pd-comments">
						<a class="value" href="#pd_comments_title">
							<span class="label"><?php echo Arr::get($cmslabel, 'show_comments', 'Komentara'); ?></span>
							<span class="value">(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)</span>
						</a>
					</div>
					<?php endif; ?>
				</div>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="wrapper">
		<?php if ($item['main_image']): ?>
			<div class="pd-hero-image">
				<img <?php echo Thumb::generate($item['main_image'], ['width' => 1480, 'height' => 540, 'crop' => true, 'default_image' => '/media/images/no-image-hero.webp', 'placeholder' => '/media/images/no-image-hero.webp', 'srcset' => '2960c 2x']); ?>  alt="<?php echo Text::meta($item['main_image_description']); ?>" />
			</div>
		<?php endif; ?>
	</div>

	<div class="pd-cnt">
		<div class="pd-wrapper">
			<div class="pd-info">
				<?php $blog = Widget_Publish::category($info['lang'], 'info'); ?>
				<a class="btn btn-white pd-btn-all" href="<?php echo $blog['url']; ?>"><span><?php echo Arr::get($cmslabel, 'back_to_all_posts'); ?></span></a>
			
				<div class="pd-date">
					<span class="value"><?php echo Date::humanize($item['datetime_published'], 'custom', 'd.m.Y.'); ?></span>
				</div>	
			</div>

			<!-- Post excerpt/short description -->
			<div class="pd-short-desc intro-text"><?php echo $item['short_description']; ?></div>
			
			<!-- Post content and related documents -->
			<div class="pd-desc cms-content">
				<?php echo $item['content']; ?>
				<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
				<?php if ($documents): ?>
					<ul class="pd-documents">
						<?php foreach ($documents as $file): ?>
							<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</div>


			<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
			<?php if ($images): ?>
				<div class="pd-thumbs">
					<?php foreach ($images as $file): ?>
						<a href="<?php echo $file['url']; ?>" class="fancybox" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
							<img loading="lazy" <?php echo Thumb::generate($file['file'], ['width' => 920, 'default_image' => '/media/images/no-image-920.webp', 'html_tag' => true, 'srcset' => '1840c 2x']); ?> alt="<?php echo Text::meta($file['description']); ?>" />
						</a>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
		</div>
	</div>

	<div class="pd-wrapper">
		<!-- Related products -->
		<?php $related_products = (Kohana::config('app.catalog.use_productrelatedpublishes')) ? Widget_Catalog::products(['lang' => $info['lang'], 'related_publish_id' => $item['id'], 'only_available' => true, 'sort' => 'list_position', 'limit' => 8]) : []; ?>
		<?php if ($related_products): ?>
			<div class="pd-related-products">
				<div class="pd-related-title"><?php echo Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'); ?></div>
				<?php echo View::factory('catalog/index_entry_featured', ['items' => $related_products, 'mode' => 'publish']); ?>
			</div>
		<?php endif; ?>
	
		<?php if (isset($item['feedback_comment_widget'])): ?>
			<div class="pd-tab-comment" id="feedback_comment_<?php echo Arr::get($item['feedback_comment_widget'], 'content'); ?>">
				<div class="cd-tab-body-title" id="pd_comments_title"><?php echo Arr::get($cmslabel, 'tab_comments'); ?></div>
				<div class="cd-comments-info">
					<div class="cd-comments-info-col1<?php if($item['feedback_comment_widget']['comments'] == 0): ?> empty<?php endif; ?>">
						<?php if (isset($item['feedback_rate_widget'])): ?>
							<div class="cd-comments-rate-section">
								<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
								<?php if(number_format($item['feedback_rate_widget']['rates'], 1) > 0): ?>
									<span class="cd-comments-rate-average"><?php echo number_format($item['feedback_rate_widget']['rates'], 1); ?></span>
								<?php endif; ?>
							</div>
						<?php endif; ?>
						<div class="cd-comments-qty">
							<?php if($item['feedback_comment_widget']['comments'] > 0): ?>
								<?php echo str_replace('%c%', $item['feedback_comment_widget']['comments'], Arr::get($cmslabel, 'comments_qty')); ?>
							<?php else: ?>
								<?php echo Arr::get($cmslabel, 'no_comments_publish'); ?>
							<?php endif; ?>
						</div>
						<button class="cd-comments-form-button">
							<span class="show"><?php echo Arr::get($cmslabel, 'comments_show_form'); ?></span>
							<span class="hide"><?php echo Arr::get($cmslabel, 'comments_hide_form'); ?></span>
						</button>
					</div>
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<?php if(number_format($item['feedback_rate_widget']['rates'], 1) > 0): ?>
							<div class="cd-comments-info-col2 cd-chart-items">
								<?php $r = 1; ?>
								<?php
									$rates_number = array();
									foreach ($item['feedback_comment_widget']['items'] AS $rates => $rates_item) {
										if(!empty($rates_item['rate'])) {
											$rates_number[$rates] = $rates_item['rate'];
										}
									}
									$rates_number_total = count($rates_number);
								?>
								<?php while($r < 6): ?>
									<?php
										if (in_array($r, $rates_number)) {
											$rates_number_count = array_count_values($rates_number);
											$rates_number_qty =  $rates_number_count[$r];
										} else {
											$rates_number_qty = '0';
										}

										if($rates_number_qty > 0) {
											$rates_percent = $rates_number_qty / $rates_number_total * 100;
										} else {
											$rates_percent = 0;
										}
									?>

									<div class="cd-chart-item">
										<div class="cd-chart-rate"><?php echo $r; ?></div>
										<div class="cd-chart-bar"><span class="cd-chart-progress-bar" style="width: <?php echo number_format((float)$rates_percent, 2, '.', ''); ?>%"></span></div>
										<div class="cd-chart-qty"><?php echo $rates_number_qty; ?></div>
									</div>
									<?php $r++ ?>
								<?php endwhile; ?>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</div>
				<?php echo View::factory('feedback/comments', Arr::merge($item['feedback_comment_widget'], ['mode' => 'publish'])); ?>
			</div>
		<?php endif; ?>
	
		<!-- Related posts -->
		<?php $blog = Widget_Publish::category($info['lang'], 'info'); ?>
		<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'extra_fields' => ['short_description'], 'limit' => 3]); ?>
		<?php if ($related_items): ?>
			<div class="pd-related">
				<div class="pd-related-title"><?php echo Arr::get($cmslabel, 'publish_related', 'Povezane objave'); ?></div>
				<div class="pd-related-items">
					<?php echo View::factory('publish/index_entry', ['items' => $related_items, 'mode' => 'publish_speciallist']); ?>
				</div>
				<div class="pd-related-btn">
					<a class="btn btn-white" href="<?php echo $blog['url']; ?>"><span><?php echo Arr::get($cmslabel, 'show_all_posts'); ?></span></a>
				</div>
			</div>
		<?php endif; ?>
	</div>

<?php $this->endblock('content_layout'); ?>