<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> publish-promotions<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="pr-row wrapper">
		<div class="pr-col1">
			<?php $child_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => '02']); ?>
			<?php if (sizeof($child_categories)): ?>
				<div class="pr-categories-title"><?php echo Arr::get($cmslabel, 'promotions_sidebar_title'); ?></div>
				<ul class="p-categories pr-categories">
					<?php $current = Utils::extract_segments($info['lang'], $info['cmspage_url'], 2); ?>
					<?php foreach ($child_categories as $child_category): ?>
						<li><a href="<?php echo $child_category['url']; ?>"<?php if ($child_category['url'] == $current): ?> class="active"<?php endif; ?>><span><?php echo $child_category['title']; ?></span><span class="prc-counter"><?php echo($child_category['total']); ?></span></a></li>
					<?php endforeach; ?>
					<?php if(!empty($kind) AND $kind['level'] > 1): ?>
						<li><a href="<?php echo reset($kind['parents'])['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all_promotions'); ?></a></li>
					<?php endif; ?>
				</ul>
			<?php endif; ?>
		</div>

		<div class="pr-col2">
			<div class="pr-header">
				<h1 class="pr-title"><?php echo $kind['seo_h1']; ?></h1>
				<div class="pr-m-categories btn btn-white btn-mobile"><?php echo Arr::get($cmslabel, 'promotions_sidebar_title'); ?></div>
			</div>

			<?php $promotions_speciallist = Widget_Publish::speciallist($info['lang'], 'special_promotion', true); ?>
			<?php $promotions_items = Widget_Publish::publishes(array('lang' => $info['lang'], 'list_code' => $promotions_speciallist['code'], 'sort' => 'list_position', 'limit' => 1, 'extra_fields' => ['short_description'])); ?>
			<?php if($promotions_items): ?>
				<div class="pr-speciallist">	
					<?php echo View::factory('publish/promotions/index_entry', ['items' => $promotions_items, 'mode' => 'ppr_special']); ?>
				</div>
			<?php endif; ?>

			<?php if ($items_total > 1): ?>
				<div class="pr-toolbar c-toolbar">
					<div class="pr-counter c-counter">
						Prikazanih <span><?php echo $items_total ?></span> izdelkov
					</div>

					<div class="sort p-sort pr-sort c-sort">
						<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
						<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
						<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
						<select onchange="window.location.href=this.options[this.selectedIndex].value">
							<?php if (!$selected_sort): ?><option><?php echo Arr::get($cmslabel, 'ordering'); ?></option><?php endif; ?>
							<option value="<?php echo $sort_base_url; ?>sort=az"<?php if ($selected_sort == 'az'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_az'); ?></option>
							<option value="<?php echo $sort_base_url; ?>sort=za"<?php if ($selected_sort == 'za'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_za'); ?></option>
							<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent'); ?></option>
							<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older'); ?></option>
							<!--<option value="?<?php echo $sort_base_url; ?>"><?php echo Arr::get($cmslabel, 'ordering_default'); ?></option>-->
						</select>
					</div>

				</div>
			<?php endif; ?>

			<div id="items_<?php echo $kind['code']; ?>_layout">
				<?php echo View::factory('publish/promotions/index_layout', [
					'kind' => $kind,
					'q' => '',
					'items' => $items,
					'items_per_page' => $items_per_page,
					'items_all' => $items_all,
					'items_total' => $items_total,
					'child_categories' => $child_categories,
					'child_categories_published' => $child_categories_published,
					'pagination' => $pagination,
					]); ?>
			</div>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>