<?php $mode = (isset($mode)) ? $mode : ''; ?>

<?php foreach ($items as $item): ?>
<a href="<?php if($item['external_url']): ?><?php echo $item['external_url']; ?><?php else: ?><?php echo $item['url']; ?><?php endif; ?>" class="ppr">
	<?php $img_w = ($mode == 'ppr_special') ? 730 : 760; ?>
	<?php $img_h = ($mode == 'ppr_special') ? 382 : 398; ?>

	<figure class="ppr-image lloader">
		<span>
			<img loading="lazy" data-lazy="<?php echo Thumb::generate($item['main_image'], $img_w, $img_h, true, 'thumb', TRUE, '/media/images/no-image-'.$img_w.'.webp'); ?>" <?php echo Thumb::generate($item['main_image'], array('width' => $img_w, 'height' => $img_h, 'crop' => TRUE, 'default_image' => '/media/images/no-image-'.$img_w.'.webp', 'placeholder' => '/media/images/no-image-'.$img_w.'.webp')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
		</span>
	</figure>
	<div class="ppr-cnt">
		<div class="ppr-date">
			<span><?php if(empty($item['datetime_expire'])): ?><?php echo Date::humanize($item['datetime_published']); ?><?php else: ?><?php echo Date::humanize($item['datetime_published'], 'custom', ' d. m.'); ?><?php endif; ?><?php if($item['datetime_expire']): ?> - <?php echo Date::humanize($item['datetime_expire']); ?><?php endif; ?></span>
		</div>
		<div class="ppr-cnt-bottom">
			<h2 class="ppr-title"><?php echo $item['title']; ?></h2>
			<div class="ppr-short-desc"><?php echo Text::limit_words(strip_tags($item['short_description']), 30, '...'); ?></div>
			<?php if($item['element_button_text']): ?>
				<div class="ppr-btn-cnt">
					<div class="ppr-btn btn"><?php echo $item['element_button_text']; ?></div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</a>
<?php endforeach; ?>