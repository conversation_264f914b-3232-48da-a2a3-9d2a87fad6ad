<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-publish-detail<?php $this->endblock('page_class'); ?>


<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('main_header'); ?>
	<?php $this->block('main_header_style'); ?>
		<div class="pd-header">
			<div class="pd-wrapper">
				<a class="pd-category" href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
				<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>
			</div>
		</div>
	<?php $this->endblock('main_header_style'); ?>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="wrapper">
		<?php if($info['user_device'] == 'm'): ?>
			<?php $images_main = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 0, 1); ?>
		<?php else: ?>
			<?php $images_main = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1, 1); ?>
		<?php endif; ?>
		<?php if ($images_main): ?>
			<div class="pd-hero-image">	
				<?php foreach ($images_main as $file): ?>
					<img <?php echo Thumb::generate($file['file'], ['width' => 1480, 'height' => 540, 'crop' => true, 'default_image' => '/media/images/no-image-hero.webp', 'placeholder' => '/media/images/no-image-hero.webp', 'srcset' => '2960c 2x']); ?>  alt="<?php echo Text::meta($file['description']); ?>" />
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
	</div>

	<div class="pd-cnt pd-cnt-special">
		<div class="pd-wrapper">
			<!-- Post excerpt/short description -->
			<div class="pd-short-desc intro-text"><?php echo $item['short_description']; ?></div>
			
			<!-- Post content and related documents -->
			<div class="pd-desc cms-content">
				<?php echo $item['content']; ?>
				<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
				<?php if ($documents): ?>
					<ul class="pd-documents">
						<?php foreach ($documents as $file): ?>
							<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</div>		

			<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
		</div>
	</div>
	
	<div class="pd-wrapper">
		<!-- Related products -->
		<?php $related_products = (Kohana::config('app.catalog.use_productrelatedpublishes')) ? Widget_Catalog::products(['lang' => $info['lang'], 'related_publish_id' => $item['id'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 4]) : []; ?>
		<?php if ($related_products): ?>
			<div class="pd-related-products">
				<div class="pd-related-title"><?php echo Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'); ?></div>
				<?php echo View::factory('catalog/index_entry_featured', ['items' => $related_products]); ?>
			</div>
		<?php endif; ?>

		<!-- Related posts -->
		<?php $blog = Widget_Publish::category($info['lang'], 'info'); ?>
		<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'extra_fields' => ['short_description'], 'limit' => 3]); ?>
		<?php if ($related_items): ?>
			<div class="pd-related">
				<div class="pd-related-title"><?php echo Arr::get($cmslabel, 'publish_related', 'Povezane objave'); ?></div>
				<div class="pd-related-items">
					<?php echo View::factory('publish/index_entry', ['items' => $related_items, 'mode' => 'publish_speciallist']); ?>
				</div>
				<div class="pd-related-btn">
					<a class="btn btn-white" href="<?php echo $blog['url']; ?>"><span><?php echo Arr::get($cmslabel, 'show_all_posts'); ?></span></a>
				</div>
			</div>
		<?php endif; ?>
	</div>

<?php $this->endblock('content_layout'); ?>