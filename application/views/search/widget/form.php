<div class="sw">
	<a href="javascript:;" class="btn-header sw-toggle"><span class="btn-header-text"><?php echo Arr::get($cmslabel, 'header_btn_search'); ?></span></a>
	<form class="sw-form" action="<?php echo Utils::app_absolute_url($info['lang'], 'catalog'); ?>" id="main_search" method="get">
	<?php $query = rawurldecode(Arr::get($_GET, 'search_q', '')); ?>
		<label class="sw-label" for="search_q"><?php if(!empty($query)): ?><span class="sw-query"><?php echo $query; ?></span><?php endif; ?><?php echo Arr::get($cmslabel, 'enter_search_term', 'Pretraživanje...'); ?></label>
		<input class="sw-input" name="search_q" type="text" placeholder="" value="<?php echo $query; ?>" />

		<div id="field-search_q-autocomplete_position" class="autocomplete-container" style="display: none">
			<div class="autocomplete-wrapper">
				<?php if($info['user_device'] == 'm'): ?>
					<div class="autocomplete-col-item autocomplete-col-item-m" data-autocomplete_contenttype_box="catalogcategory">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_categories'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogcategory"></ul>
					</div>
				<?php endif; ?>
				<div class="autocomplete-col autocomplete-col1 catalogproduct">
					<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_products'); ?></div>
					<ul class="ui-autocomplete ui-autocomplete1" data-autocomplete_contenttype="catalogproduct"></ul>
				</div>
				<div class="autocomplete-col autocomplete-col2 catalogcategory">
					<?php if($info['user_device'] != 'm'): ?>
						<div class="autocomplete-col-item" data-autocomplete_contenttype_box="catalogcategory">
							<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_categories'); ?></div>
							<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogcategory"></ul>
						</div>
					<?php endif; ?>
					<div class="autocomplete-col-item catalogmanufacturer" data-autocomplete_contenttype_box="catalogmanufacturer">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_manufacturers'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogmanufacturer"></ul>	
					</div>
					<div class="autocomplete-col-item publish" data-autocomplete_contenttype_box="publish">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_publish'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="publish"></ul>	
					</div>
					<?php /* ?>
					<div class="autocomplete-col-item catalogattribute" data-autocomplete_contenttype_box="catalogattribute">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_caracteristics'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogattribute"></ul>
					</div>
					<?php */ ?>
				</div>
			</div>
		</div>	

		<a href="javascript:;" class="sw-clear"></a>
		<button class="sw-btn" type="submit"><?php echo Arr::get($cmslabel, 'search_button', 'Traži'); ?></button>
	</form>
</div>