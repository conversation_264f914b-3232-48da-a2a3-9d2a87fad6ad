<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-search page-search-<?php echo $search_content; ?><?php $this->endblock('page_class'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
<?php $search_content = Arr::get($_GET, 'search_content'); ?>
<?php $search_totals = Widget_Search::totals($info['lang'], $query, $search_content); ?>
<?php $other_results = array_sum($search_totals); ?>
<?php $blog_category = Widget_Publish::category($info['lang'], 'info'); ?>
<?php $blog_url = (!empty($blog_category['url'])) ? $blog_category['url'] : ''; ?>

<?php $this->block('main'); ?>
	<div class="s-header">
		<div class="s-nav-cnt special">
			<ul class="s-nav">
				<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $query; ?>"><span><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></span></a></li>
				<li<?php if($search_content == 'publish.01'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=publish.01"><span><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', (($items AND $search_content == 'publish.01') ? count($items['publish.01']) : 0)); ?>)</span></span></a></li>
				<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=cms"><span><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></span></a></li>
			</ul>
		</div>
	</div>

	<?php if (sizeof($items) > 0 ): ?>
		<?php foreach ($items as $module => $results): ?>
			<div class="search-container">
				<?php if ($module == 'publish.01'): ?>
					<div class="wrapper">
						<div class="s-publish">
							<?php echo View::factory('publish/index_entry', array('items' => $results, 'mode' => 'search')); ?>
						</div>
					</div>
				<?php elseif ($module == 'catalog'): ?>
					<?php echo View::factory('catalog/index_entry', array('items' => $results, 'mode' => 'search')); ?>					
				<?php else: ?>
					<div class="s-cms-wrapper">
						<?php foreach ($results as $item): ?>
							<article class="s-item">
								<h2 class="s-item-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
								<?php if (isset($item['content']) AND $item['content']): ?><p><?php echo Text::limit_words(strip_tags($item['content']), 50, '...'); ?></p><?php endif; ?>
							</article>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
			<div class="clear"></div>
		<?php endforeach; ?>
	<?php else: ?>
		<div class="wrapper">
			<div class="s-no-results"><?php echo Arr::get($cmslabel, 'nothing_found', 'Nema rezultata za traženi pojam'); ?></div>
		</div>
	<?php endif; ?>
<?php $this->endblock('main'); ?>