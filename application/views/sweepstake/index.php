<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php if ($pagination->current_page > 1): ?><?php echo sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages); ?><?php endif; ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => isset($cms_page) ? $cms_page : [],'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
<?php echo Arr::get($cms_page, 'content'); ?>

<?php foreach ($items as $item): ?>
	<h2><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
<?php endforeach; ?>

<?php echo $pagination; ?>
<?php $this->endblock('content'); ?>