<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb'); ?>
<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('extrahead'); ?>
<?php if (!empty($item['use_recaptcha'])): ?>
    <script src="https://www.google.com/recaptcha/api.js"></script>
<?php endif; ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('content'); ?>
<h1><?php echo $item['seo_h1']; ?></h1>

<?php echo $item['content']; ?>

<?php if ($info['message_type'] AND $info['message']): ?>
    <?php if (is_array($info['message'])): ?>
        <p class="<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
    <?php else: ?>
        <p class="<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, "{$info['message_type']}_{$info['message']}"); ?></p>
    <?php endif; ?>
<?php endif; ?>

<?php if ($can_vote): ?>
    <form action="<?php echo $item['vote_url']; ?>" method="POST" class="ajax_siteform sweepstake-form form-animated-label" <?php if (!empty($item['use_recaptcha'])): ?>data-recaptcha_form=1 <?php endif; ?>>
        <div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>

        <?php
        if($item['content_type'] == 'leadgeneration'):
            $user_fields = Utils::config_fields('sweepstake', 'participant_user_fields_alternative');
        ?>
            <div class="sweepstake-userfields">
                <h2 class="sweepstake-question-title"><?php echo Arr::get($cmslabel, 'sweepstake_user', 'Podaci za prijavu'); ?></h2>
                <?php foreach ($user_fields as $user_field): ?>
                    <?php echo $user_field; ?>
                    <?php if (!empty($participant_extra_data['subscriber_active']) AND $user_field == 'newsletter') {continue;} ?>
                    <?php $error = Valid::get_error($user_field, $errors); ?>
                    <p class="field field-<?php echo $user_field; ?>">
                        <?php echo $user_participant->input($user_field, 'form'); ?>
                        <label for="field-<?php echo $user_field; ?>" class="label-<?php echo $user_field; ?>"><?php echo Arr::get($cmslabel, $user_field, $user_participant->meta()->field($user_field)->label); ?><?php echo (in_array($user_field, $user_request_fields) ? ' *' : ''); ?></label>
                        <span id="field-error-<?php echo $user_field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
                    </p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="sweepstake-questions">
            <?php foreach ($item['questions'] AS $question_id => $question): ?>

                <div class="sweepstake-field field-<?php echo "question_{$question['id']}"; ?> clear">
                    <?php if ($question['main_image']): ?>
                        <span class="question-image"><img <?php echo Thumb::generate($question['main_image'], array('width' => 100, 'height' => 100, 'html_tag' => TRUE)); ?> alt="" /></span>
                    <?php endif; ?>
                    <h2 class="sweepstake-question-title"><?php echo $question['title']; ?><?php if ($question['requested']): ?> *<?php endif; ?></h2>
                    <?php if ($question['tips']): ?><div class="sweepstake-note"><?php echo $question['tips']; ?></div><?php endif; ?>
                    <?php if ($question['type'] == ''): ?>
                        <?php echo Form::select_as_radio2("question_{$question['id']}", $question['answers_option'], Arr::get($_POST, "question_{$question['id']}", '')); ?>
                    <?php elseif ($question['type'] == 'selectm'): ?>
                        <?php echo Form::select_as_checkbox("question_{$question_id}", $question['answers_option'], Arr::get($_POST, "question_{$question_id}", '')); ?>
                    <?php elseif ($question['type'] == 'text'): ?>
                        <?php echo Form::textarea("question_{$question['id']}", Arr::get($_POST, "question_{$question['id']}", '')); ?>
                    <?php endif; ?> 
                    <?php $error = Valid::get_error("question_{$question['id']}", $errors); ?>
                    <div id="field-error-<?php echo "question_{$question['id']}"; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (!empty($participant_extra_data['webshoporder']) OR $item['content_type'] == 'leadgeneration'): ?>

        <?php else: ?>
            <div class="sweepstake-userfields">
                <h2 class="sweepstake-question-title"><?php echo Arr::get($cmslabel, 'sweepstake_user', 'Podaci za prijavu'); ?></h2>
                <?php foreach ($user_fields as $user_field): ?>
                <?php echo $user_field; ?>
                    <?php if (!empty($participant_extra_data['subscriber_active']) AND $user_field == 'newsletter') {continue;} ?>
                    <?php $error = Valid::get_error($user_field, $errors); ?>
                    <p class="field field-<?php echo $user_field; ?>">  
                        <?php echo $user_participant->input($user_field, 'form'); ?>
                        <label for="field-<?php echo $user_field; ?>" class="label-<?php echo $user_field; ?>"><?php echo Arr::get($cmslabel, $user_field, $user_participant->meta()->field($user_field)->label); ?><?php echo (in_array($user_field, $user_request_fields) ? ' *' : ''); ?></label>
                        <span id="field-error-<?php echo $user_field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
                    </p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <?php
            $gdpr_template_api = App::config('bigbangsigdpr_api_sweepstake');
            $gdpr_template_api = (!empty($gdpr_template_api)) ? json_decode($gdpr_template_api, true) : [];
        ?>
        <?php if (!empty($gdpr_template_api) AND !empty($item['use_gdpr_api'])): ?>
            <div class="signup-note">
                <?php echo $gdpr_template_api['content']['header']; ?>
            </div>
            <div class="signup-gdpr-checkbox">
                <p class="field field-gdpr_template_api">
                    <input type="checkbox" name="gdpr_template_api_all" value="1" id="gdpr_template_api_all" />
                    <label for="gdpr_template_api_all"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?></label>
                    <span class="signup-desc active">
					    <span class="signup-desc-cnt signup-desc-cnt-special"><?php echo Arr::get($cmslabel, 'gdpr_select_all_tips'); ?></span>
					</span>
                </p>

                <div class="signup-gdpr-checkbox-wrapper">
                    <?php foreach ($gdpr_template_api['objects'] AS $gdpr_template_api_object): ?>
                        <p class="field field-gdpr_template_api">
                            <input type="checkbox" name="gdpr_template_api[]" value="<?php echo $gdpr_template_api_object['code']; ?>" id="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>" />
                            <label for="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>"><?php echo $gdpr_template_api_object['title']; ?></label>
                            <span class="signup-desc">
                                <span class="signup-desc-link">
                                    <span class="btn-inactive"><?php echo Arr::get($cmslabel, 'gdpr_show_more'); ?></span>
                                    <span class="btn-active"><?php echo Arr::get($cmslabel, 'gdpr_hidde_more'); ?></span>
                                </span>
                                <span class="signup-desc-cnt"><?php echo $gdpr_template_api_object['content']; ?></span>
                            </span>
                        </p>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="signup-note">
                <?php echo $gdpr_template_api['content']['footer']; ?>
            </div>
        <?php endif; ?>
        <?php if (!empty($item['use_recaptcha'])):?>
            <button type="submit" data-sitekey="<?php echo App::config('recaptcha_key'); ?>" data-callback='onReCaptchaSubmit' data-action='submit' class="btn-sweepstake g-recaptcha"><?php echo Arr::get($cmslabel, 'vote'); ?></button>
        <?php else: ?>
            <button type="submit" class="btn-sweepstake"><?php echo Arr::get($cmslabel, 'vote'); ?></button>
        <?php endif; ?>
    </form>
<?php else: ?>
    <?php if (!$item['vote_url']): ?>
        <?php echo Arr::get($cmslabel, 'votes_closed'); ?>
    <?php elseif (!$can_vote): ?>
        <?php echo Arr::get($cmslabel, 'already_vote'); ?>
    <?php endif; ?>
<?php endif; ?>
<?php $this->endblock('content'); ?>

<?php $this->block('extrabody'); ?>
<?php if (!empty($participant_feedback_items)): ?>
    <?php echo Html::media('cmsfeedback', 'js'); ?>
<?php endif; ?>
<?php $this->endblock('extrabody'); ?>