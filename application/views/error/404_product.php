<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cmslabel, '404_product_title', __('Proizvod nije pronađen'))); ?><?php $this->endblock('title'); ?>

<?php $this->block('seo'); ?>
<meta name="description" content="<?php echo Text::meta(Arr::get($cmslabel, '404_product_description', __('<p>Odabrani proizvod trenutno nije dostupan.</p>'))); ?>" />
<meta property="og:title" content="<?php echo Text::meta(Arr::get($cmslabel, '404_product_title', __('Proizvod nije pronađen'))); ?>"/>
<meta property="og:description" content="<?php echo Text::meta(Arr::get($cmslabel, '404_product_description', __('<p>Odabrani proizvod trenutno nije dostupan.</p>'))); ?>" />
<meta name="robots" content="noindex, follow" />
<?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog-index page-catalog-index-404 page-landing-catalog<?php $this->endblock('page_class'); ?>

<?php $this->block('main_header'); ?>
    <div class="main-header">
        <div class="wrapper">
            <h1 class="c-landing-catalog-title"><?php echo Arr::get($cmslabel, '404_product_h1', __('Proizvod trenutno nije dostupan')); ?></h1>
            <div class="c-landing-catalog-desc"><?php echo Arr::get($cmslabel, '404_product_description', __('<p>Odabrani proizvod trenutno nije dostupan.</p>')); ?></div>
        </div>
    </div>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
    <div class="wrapper">
	    <?php echo View::factory('catalog/widget/categories'); ?>
    </div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('main_extra'); ?>
    <div class="wrapper">
	    <?php echo View::factory('catalog/widget/catalog_promo', ['mode' => 'catalog_landing']); ?>
    </div>
<?php $this->endblock('main_extra'); ?>

