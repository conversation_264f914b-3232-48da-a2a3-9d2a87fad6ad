<?php if (!empty($payment_data['list_title'])): ?><br/><strong><?php echo $payment_data['list_title']; ?></strong><?php endif; ?>
<?php if (!empty($payment_data['alert']) AND !empty($payment_data['alert_missing'])): ?>
	<div class="payment-disabled-info">
		<?php echo Arr::get($cmslabel, 'payment_info_leanpay_disabled'); ?>
	</div>
	<div class="payment-disabled-section">
		<span class="payment-disabled-label"><?php echo Arr::get($cmslabel, 'leanpay_disabled_label'); ?></span>
		<div class="payment-disabled-items">
			<?php foreach ($payment_data['alert_missing'] AS $product_missing): ?>
				<div class="payment-disabled-item">
					<div class="pdi-image">
						<figure>
							<img <?php echo Thumb::generate($product_missing['main_image'], array('width' => 50, 'height' => 50, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.webp', 'srcset' => '180c 2x')); ?> alt="" />
						</figure>
					</div>
					<div class="pdi-col">
						<div class="pdi-cnt">
							<div class="pdi-title"><?php echo $product_missing['title']; ?></div>
							<div class="pdi-code"><?php echo Arr::get($cmslabel, 'id'); ?>: <span><?php echo $product_missing['code']; ?></span></div>
						</div>
						<div class="pdi-price"><?php echo Utils::currency_format($product_missing['price'] * $currency['exchange'], $currency['display']); ?></div>
					</div>
				</div>
			<?php endforeach;; ?>
		</div>
		<div class="payment-disabled-buttons">
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-lightBlue pdi-btn-change"><?php echo Arr::get($cmslabel, 'change_cart_short'); ?></a>
			<a href="javascript:cmswebshop.shopping_cart.set_customer_data('remove_payment_alert_missing', 1);" class="btn pdi-btn-remove"><?php echo Arr::get($cmslabel, 'leanypay_remove_items'); ?></a>
		</div>
	</div>
<?php endif; ?>