<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step2<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header">
		<div class="wc-header-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="wc-logo" id="logo"></a>
			<div class="wc-progress-section">
				<div class="wc-progress-title"><?php echo Arr::get($cmslabel, 'checkout_progress_title'); ?></div>
				<div class="wc-progress-bar">
					<div class="wc-progress-bar-bg step3"></div>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main'); ?>
<div class="wc-container" data-tracking_gtm_checkout="3|<?php echo Utils::token_id(); ?>">
	<form class="wc-col wc-col1 wc-step3-col1 checkout-form ajax_siteform ajax_siteform_loading form form-animated-label" action="#webshop_form" method="post" name="webshop" id="webshop_form">
		<div class="wc-steps-active">
			<a class="wc-step active step step1 step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>">
				<span class="title">1. <?php echo Arr::get($cmslabel, 'step1'); ?></span> 
				<span class="wc-step-change"><?php echo Arr::get($cmslabel, 'change'); ?><span>
			</a>
		</div>

		<div class="col-cont">
			<div class="wc-subtitle special"><span class="wc-num">2.</span> <?php echo Arr::get($cmslabel, 'step2'); ?></div>
			<div class="wc-step2-col-shipping">
				<div class="wc-subtitle-label"><?php echo Arr::get($cmslabel, 'step2_shippings'); ?></div>
				<?php $i = 1; ?>
				<?php foreach ($customer_fields as $field): ?>
					<?php if ($field != 'shipping') {continue;} ?>
					<div class="section-shipping wc3-section-shipping">
						<?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data)); ?>
					</div>
					<?php $i++; ?>
				<?php endforeach; ?>
			</div>

			<div class="wc-step2-col-payment">
				<div class="wc-subtitle-label"><?php echo Arr::get($cmslabel, 'step2_payments'); ?></div>
				<?php if (sizeof($errors) > 0): ?>
					<p class="wc-global-error error global-error"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php endif; ?>

				<!-- Payment options -->
				<div class="payment-options">
					<div class="payment-options-label"><?php echo Arr::get($cmslabel, 'payment_options_title'); ?></div>
					<?php foreach ($customer_fields as $field): ?>
						<?php if ($field != 'payment') {continue;} ?>
						<?php $error = Valid::get_error($field, $errors); ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get(@$cmslabel, "error_{$error}", $error); ?></span>
						<div class="field-<?php echo $field; ?>">
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> <?php endif; ?></label>
							<?php if ($field == 'payment'): ?>
								<?php if (sizeof($available_payments_none) == 1): ?>
									<div class="cart_info_payment_box_normal <?php if ($shopping_cart_info['total'] > 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments, ($shopping_cart_info['total'] > 0) ? $data->payment->id : NULL); ?>
									</div>
									<div class="cart_info_payment_box_0 <?php if ($shopping_cart_info['total'] == 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments_none, ($shopping_cart_info['total'] == 0) ? key($available_payments_none) : NULL); ?>
									</div>
								<?php else: ?>
									<?php echo Form::select_as_radio2($field, $available_payments, $data->payment->id); ?>
								<?php endif; ?>
							<?php else: ?>
								<?php echo $data->input($field, 'form'); ?>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
		</div>
		<?php if (!empty($shopping_cart_info['selected_payment_alert']) AND !empty($shopping_cart_info['selected_payment_alert_missing'])): ?>
			<button type="submit" class="btn btn-green btn-checkout disabled" disabled="disabled"><span><?php echo Arr::get($cmslabel, 'goto_step2_button'); ?></span></button>
		<?php else: ?>
			<button type="submit" class="btn btn-green btn-checkout"><span><?php echo Arr::get($cmslabel, 'goto_step2_button'); ?></span></button>
		<?php endif; ?>
		<div class="wc-steps">
			<a class="wc-step step step3 step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order'); ?>"><span class="num">3.</span><span class="label"> <?php echo Arr::get($cmslabel, 'step3'); ?></span></a>
		</div>
	</form>

	<div class="wc-col wc-col2 wc-step3-col2">
		<div class="wc-col2-box">
			<!-- Coupons -->
			<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>

			<!-- Cart -->
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>
			
			<div class="wc-total">
				<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
			</div>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('footer'); ?>
	<div class="wc-footer">
		<div class="wc-container">
			<div class="wc-footer-col1 support support-sidebar">
				<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
				<?php echo Arr::get($cmslabel, 'support'); ?>
			</div>
			<div class="wc-footer-col2">
				<div class="cards wc-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
				<div class="wc-copyright"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
				<span class="wc-copyright wc-copyright-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
			</div>
			<div class="wc-footer-col3 footer-badges wc-badges">
				<div class="footer-trustmark smdWrapperTag"></div>
				<?php echo Arr::get($cmslabel, 'footer_badges'); ?>
			</div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>