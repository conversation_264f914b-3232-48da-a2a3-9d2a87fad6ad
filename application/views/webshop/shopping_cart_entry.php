<div class="wp" id="product_details_<?php echo $product_code; ?>">
	<input type="hidden" name="product_price" value="<?php echo $product_status['price'] ?>" />
	<input type="hidden" name="product_total" value="<?php echo $product_status['total'] ?>" />
	<span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'code'); ?></span>
    <span style="display: none;" data-product_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'title'); ?></span>
    <span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'category_title'); ?></span>
    <span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'manufacturer_title'); ?></span>
    <span style="display: none;" data-product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format(Arr::get($product_data, 'price')); ?></span>
	<div class="wp-col1 inner">
		<div class="wp-image">
			<figure>
				<a href="<?php echo $product_data['url']; ?>">
				<img loading="lazy" <?php echo Thumb::generate($product_data['main_image'], array('width' => 200, 'height' => 200, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-160.webp', 'srcset' => '400c 2x')); ?> alt="<?php echo $product_data['title']; ?>" />
				</a>
			</figure>
		</div>
		<a class="wp-btn-delete" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');">
			<?php if($info['user_device'] == 'm'): ?>
				<span><?php echo Arr::get($cmslabel, 'm_remove_product', 'Ukloni'); ?></span>
			<?php else: ?>
				<span><?php echo Arr::get($cmslabel, 'remove_product', 'Ukloni'); ?></span>
			<?php endif; ?>
		</a>
	</div>
	<div class="wp-row inner">
		<div class="wp-row-col1">
			<div class="wp-cnt">
				<div class="wp-category" data-product_category_title="<?php echo $product_code; ?>"><a href="<?php echo $product_data['category_url']; ?>"><?php echo $product_data['category_title']; ?></a></div>
				<div class="wp-title" data-product_title="<?php echo $product_code; ?>">
					<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
				</div>
				<div class="wp-code"><?php echo Arr::get($cmslabel, 'id', 'Šifra'); ?>: <span><?php echo (!empty($product_data['variation_code'])) ? $product_data['variation_code'] : $product_data['code']; ?></span></div>

				<?php if (!empty($product_data['coupon_price'])): ?>
					<?php echo Arr::get($cmslabel, 'coupon_value', 'Vrijednost kupona'); ?>: <?php echo $product_data['coupon_price']; ?>
				<?php elseif (!empty($product_data['bonus_total'])): ?>
					<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
				<?php elseif (!empty($product_data['type']) AND $product_data['type'] == 'download'): ?>
					<?php echo Arr::get($cmslabel, 'download_files', 'Preuzimanje datoteka'); ?>
				<?php endif; ?>
			</div>
			<div class="wp-price">
				<div class="wp-qty">
					<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-');"></a>
					<input class="wp-input-qty product_qty_input" tabindex="<?php echo $i; ?>" type="text" name="qty[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
					<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+');"></a>
					<?php $unit = (!empty($product_data['unit'])) ? $product_data['unit'] : Arr::get($cmslabel, 'unit', 'kom'); ?>
					<span class="wp-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>	
				</div>
				<div class="wp-total">
                    <?php if (($product_status['total_basic'] - $product_status['total']) > 0.01): ?>
                        <div class="wp-price-old line-through" data-shoppingcart_product_total_basic="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></div>
						<div class="wp-price-discount wp-price-current<?php if(!empty($product_status['is_loyalty_price'])): ?> blue<?php else: ?> red<?php endif; ?>" data-shoppingcart_product_total_without_service="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
					<?php else: ?>
						<div class="wp-price-current" data-shoppingcart_product_total_without_service="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
					<?php endif ?>
					<div class="wp-qty-count wp-price-old">
						<span class="product_qty" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> x <span data-shoppingcart_product_price_without_service="<?php echo $product_code; ?>" data-product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['price_without_service'] * $currency['exchange'], $currency['display']); ?></span>
					</div>
				</div>
			</div>
		</div>

		<?php if (!empty($product_status['services'])): ?>
			<?php $service_selected = 0; ?>
			<div class="wp-extra-benefits-section">
				<div class="wp-extra-benefits">
					<?php foreach ($product_status['services'] AS $item_service_group): ?>
						<?php 
						if (!empty($item_service_group['selected'])) {
							$service_selected += $item_service_group['selected'];
						}
						?>

						<?php foreach ($item_service_group['items'] AS $item_service_item_id => $item_service_item): ?>
							<?php if (empty($item_service_item['active'])) {continue;} ?>
							<div class="wp-extra-benefit-item">
								<div class="wp-extra-benefit-row">
									<?php if ($item_service_group['type'] == 's'): ?>
										<input type="radio" name="services_<?php echo $product_code; ?>[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_code; ?>" data-service_extra_category="<?php echo $item_service_group['code']; ?>" <?php if (!empty($item_service_item['extradata_request'])): ?>data-service_extra_extradata_request="1"<?php endif; ?> <?php if (!empty($item_service_item['selected'])): ?>checked <?php endif; ?> />
									<?php elseif ($item_service_group['type'] == 'c'): ?>
										<input type="checkbox" name="services_<?php echo $product_code; ?>[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_code; ?>" data-service_extra_category="<?php echo $item_service_group['code']; ?>" <?php if (!empty($item_service_item['extradata_request'])): ?>data-service_extra_extradata_request="1"<?php endif; ?> <?php if (!empty($item_service_item['selected'])): ?>checked <?php endif; ?> />
									<?php endif; ?>
									<label class="wp-extra-benefit" for="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>">
										<div class="wp-extra-benefit-title" data-service_extra_title="<?php echo $item_service_item['id']; ?>">
											<?php echo $item_service_item['title']; ?>
										</div>
									</label>
									<div class="wp-extra-benefit-price">+ <span class="product_qty" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> x <span data-service_extra_price="<?php echo $item_service_item['id']; ?>"><?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?></span></div>						
									<?php if($item_service_item['description']): ?><span class="wp-extra-benefit-icon"></span><?php endif; ?>
								</div>
								<?php if($item_service_item['description']): ?>
									<div class="wp-extra-benefit-desc"><?php echo $item_service_item['description']; ?></div>
								<?php endif; ?>
							</div>
						<?php endforeach; ?>

						<?php if ($item_service_group['type'] == 's'): ?>
							<div class="wp-extra-benefit-item" data-service_extra_none="<?php echo $product_code; ?>_<?php echo $item_service_group['code']; ?>" <?php if (!$service_selected): ?>style="display: none"<?php endif; ?>>
								<div class="wp-extra-benefit-row">
									<input type="radio" name="services_<?php echo $product_code; ?>[]" value="0" id="service-<?php echo $product_code; ?>-0" data-service_extra="<?php echo $product_code; ?>"  data-service_extra_category="<?php echo $item_service_group['code']; ?>" />
									<label class="wp-extra-benefit" for="service-<?php echo $product_code; ?>-0">
										<div class="wp-extra-benefit-title" data-service_extra_title="<?php echo @$item_service_item['id']; ?>">
											<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
										</div>
									</label>
								</div>
							</div>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			</div>

			<div data-service_extra_form="<?php echo $product_code; ?>" class="wp-total-price" <?php if (empty($service_selected)): ?>style="display: none"<?php endif; ?>>
				<span class="name">Total:</span><span class="value"><strong class="product_total"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></strong></span>
			</div>
		<?php endif; ?>
	</div>
</div>