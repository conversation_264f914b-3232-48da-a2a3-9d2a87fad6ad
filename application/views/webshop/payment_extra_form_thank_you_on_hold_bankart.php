<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-thank-you<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
    <div class="thank-you-section">
        <div class="thank-you-box checkout-cnt">
            <span data-payment_gateway_check_orderadditionalpayment="<?php echo $order->id; ?>"></span>
            <h1 class="success-title"> <?php echo Arr::get($cmslabel, 'transaction_on_hold'); ?> </h1>
        </div>
    </div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>