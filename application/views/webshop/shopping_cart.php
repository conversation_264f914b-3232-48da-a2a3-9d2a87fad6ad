<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> flyout-page page-cart<?php $this->endblock('page_class'); ?>


<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('extrahead'); ?>
    <script type="module" crossorigin src="/media/assets/index.8917a578.js"></script>
    <link rel="stylesheet" href="/media/assets/index.288837c6.css">
<?php $this->endblock('extrahead'); ?>

<?php $this->block('content_layout'); ?>
<div id="app">
    <div style="text-align:center; padding-top: 40px;"><img src="/media/images/loader.svg" width="60" height="60" alt=""></div>
</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('extrabody_top'); ?>
<?php if ((Kohana::$environment === 1 OR $info['site_url'] === 'https://bigbangmarkerdev.info' OR $info['site_url'] === 'http://bigbang.marker')): ?>
    <?php
    $product_ids = [];
    if (!empty($products)) {
        $product_ids = array_column($products, 'id');
    }
    $product_ids = implode(',', $product_ids);
    ?>
    <?php if (!empty($product_ids)): ?>
        <script>
            var dataLayer = window.dataLayer || [];
            dataLayer.push({'basketIds': '<?php echo $product_ids; ?>'});
        </script>
    <?php endif; ?>
<?php endif; ?>
<?php $this->endblock('extrabody_top'); ?>
