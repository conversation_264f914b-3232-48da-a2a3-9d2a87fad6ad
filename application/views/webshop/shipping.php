<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step1<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header">
		<div class="wc-header-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="wc-logo" id="logo"></a>
			<div class="wc-progress-section">
				<div class="wc-progress-title"><?php echo Arr::get($cmslabel, 'checkout_progress_title'); ?></div>
				<div class="wc-progress-bar">
					<div class="wc-progress-bar-bg step2"></div>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main'); ?>
<div class="wc-container" data-tracking_gtm_checkout="2|<?php echo Utils::token_id(); ?>">
	<form class="wc-col wc-col1 wc-step2-col1 checkout-form form-animated-label ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form" accept-charset="utf-8" data-webshop_autocomplete="zipcode_city_location" <?php if (!empty(Kohana::config('app.webshop.company_api'))): ?> data-webshop_company_api="b_company_oib"<?php endif; ?>>
		<?php if ((!empty($customer_data['address']) AND !empty($customer_data['city']) AND !empty($customer_data['zipcode'])) OR (!empty($customer_data['usercontact']) AND $customer_data['usercontact'] != -1)) {
			$user_has_address = true;
		} ?>
		<div class="wc-col1-cnt wc-col1-shipping-cnt wc-step-col1-cnt">
			<div class="wc-subtitle special"><span class="wc-num">1.</span> <?php echo Arr::get($cmslabel, 'step1'); ?></div>
			<?php echo Arr::get($cms_page, 'content'); ?>
			<div class="wc-global-error global_error global-error"<?php if ( ! count($errors)): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
			<?php if ($info['user_id'] AND !empty($user_has_address)): ?>
				<div class="wc-user-info">
					<p><strong><?php echo $user->first_name; ?> <?php echo $user->last_name; ?></strong></p>
					<?php if($user->address): ?>
						<p>
							<?php echo $user->address; ?><br>
							<?php echo $user->zipcode; ?> <?php echo $user->city; ?>
						</p>
					<?php endif; ?>
					<?php if($user->email): ?>
						<p><?php echo $user->email; ?></p>
					<?php endif; ?>
					<?php if($user->phone): ?>
						<p><?php echo $user->phone; ?></p>
					<?php endif; ?>

					<a class="wc-edit-profile" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'checkout_edit_profile'); ?></span></a>
				</div>
			<?php endif; ?>

			<?php $i = 1; ?>
			<?php if ($info['user_id'] AND !empty($user_has_address)): ?><div class="wc-user-login" style="display:none;"><?php endif; ?>
			<?php foreach ($customer_fields as $field): ?>
				<?php if(in_array($field, ['country', 'city', 'zipcode'])) {continue;} ?>
				<div class="field field-<?php echo $field; ?><?php if($i % 2 == 0): ?> last<?php endif; ?><?php if(count($available_shippings) <= 1): ?> single<?php endif; ?>">
					<p>
						<?php $error = Valid::get_error($field, $errors); ?>
						<?php if($field == 'message'): ?>	
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'note', 'Optional remark'); ?></label>
						<?php else: ?>
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
						<?php endif; ?>
						<?php if ($field == 'location'): ?>
							<input type="text" name="location" id="field-location" value="<?php echo (!empty($customer_data['zipcode']) ? $customer_data['zipcode'] . ' ' : '') . Arr::get($customer_data, 'city'); ?>" data-display_format="3" />
							<input type="hidden" name="zipcode" id="field-zipcode" value="<?php echo Arr::get($customer_data, 'zipcode'); ?>" />
							<input type="hidden" name="country" id="field-country" value="<?php echo Arr::get($customer_data, 'country'); ?>" />
							<input type="hidden" name="city" id="field-city" value="<?php echo Arr::get($customer_data, 'city'); ?>" />
						<?php else: ?>
						<?php echo $data->input($field, 'form'); ?>
						<?php endif; ?>
						<?php if(in_array($field, ['location'])): ?>
							<span id="field-error-<?php echo $field ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
							<span id="field-error-city" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
						<?php else: ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
						<?php endif; ?>
						<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'We will only call you if there are questions regarding your order.'); ?></span><?php endif; ?>
					</p>
				</div>
				<?php $i++; ?>
			<?php endforeach; ?>
			<?php if ($info['user_id'] AND !empty($user_has_address)): ?></div><?php endif; ?>

			<div class="bill-fields">
				<div class="col-cont-2">
					<?php if (Kohana::config('app.webshop.bill_on_shipping_step')): ?>
						<?php if (sizeof($customer_bill_fields)): ?>
							<?php $b = 0; ?>
							<div class="shipping-options-label"><?php echo Arr::get($cmslabel, 'bill_shipping'); ?></div>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php if(in_array($field, ['b_country', 'b_city', 'b_zipcode'])) {continue;} ?>
								<?php $error = Valid::get_error($field, $errors); ?>
								<?php if($field == 'b_same_as_shipping' OR $field == 'b_r1'): ?>
									<?php if($field == 'b_r1' AND !empty($shopping_cart_info['forbid_r1'])): ?>
										<p class="field field-b_r1 ffl-floated" style="overflow: hidden;" >
											<input type="hidden" id="field-b_r1-none" name="b_r1" value="0" class="class">
											<input type="checkbox" id="field-b_r1" name="b_r1" value="0" class="field_boolean" disabled>
											<label for="field-b_r1" class="label-b_r1">Želim račun na podjetje</label>
											<span id="field-error-b_r1" class="field_error error" style="display: none"></span>
										</p>
										<?php if(!empty($shopping_cart_info['forbid_r1'])): ?>
											<div class="cd-r1-info">
												<span><?php echo Arr::get($cmslabel, 'notification'); ?></span>
												<?php echo Arr::get($cmslabel, 'forbid_r1_info'); ?>
											</div>
										<?php endif; ?>
									<?php else: ?>
										<p class="field field-<?php echo $field; ?>">
											<?php echo $data->input($field, 'form'); ?>
											<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?></label>
											<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
										</p>
									<?php endif; ?>
								<?php else: ?>
									<p class="field field-<?php echo $field; ?><?php if($b % 2 == 0): ?> last<?php endif; ?>">
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?></label>
										<?php if ($field == 'b_location'): ?>
											<input type="text" name="b_location" id="field-b_location" value="<?php echo (!empty($customer_data['b_zipcode']) ? $customer_data['b_zipcode'] . ' ' : '') . Arr::get($customer_data, 'b_city'); ?>" data-display_format="3" />
											<input type="hidden" name="b_zipcode" id="field-b_zipcode" value="<?php echo Arr::get($customer_data, 'b_zipcode'); ?>" />
											<input type="hidden" name="b_country" id="field-b_country" value="<?php echo Arr::get($customer_data, 'b_country'); ?>" />
											<input type="hidden" name="b_city" id="field-b_city" value="<?php echo Arr::get($customer_data, 'b_city'); ?>" />
										<?php else: ?>
											<?php if ($field == 'b_company_oib'): ?>
												<?php echo $data->input($field, 'form', ['input_type' => 'number']); ?>
												<a class="btn btn-white btn-company-oib" data-autosubmit_field="b_company_oib" href="javascript:void(0);"><span><?php echo Arr::get($cmslabel, 'company_oib_btn'); ?></span></a>
											<?php else: ?>
												<?php echo $data->input($field, 'form'); ?>
											<?php endif; ?>
										<?php endif; ?>
										<?php if($field == 'b_phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?></span><?php endif; ?>

										<?php if(in_array($field, ['b_location'])): ?>
											<span id="field-error-<?php echo $field ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
										<?php else: ?>
											<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
										<?php endif; ?>
									</p>
								<?php endif; ?>
								<?php $b++; ?>

								<?php if($field == 'b_phone' AND $info['user_id'] AND !empty($user_has_address)): ?>
									<div class="wc-shipping-extra">
										<?php foreach ($customer_fields as $field): ?>
											<?php if($field == 'message'): ?>
												<p class="field field-message-custom field-<?php echo $field; ?>">
													<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'note'); ?><?php if (in_array($field, $request_fields)): ?> <?php endif; ?></label>
													<?php echo $data->input($field, 'form'); ?>
													<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get(@$cmslabel, "error_{$error}", $error); ?></span>
												</p>
											<?php endif; ?>
										<?php endforeach; ?>
									</div>
								<?php endif; ?>
							<?php endforeach; ?>

						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping', 'Use delivery adress as billing adress'); ?>
						<?php endif; ?>
					<?php endif; ?>
					
				</div>
			</div>
			<button class="btn btn-green btn-checkout" type="submit"><span><?php echo Arr::get($cmslabel, 'goto_step2_button'); ?></span></button>
		</div>
		<!-- Steps -->
		<?php //echo View::factory('webshop/widget/step', ['current_step' => 2]); ?>
		<div class="wc-steps">
			<a class="wc-step step step2 step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>"><span class="num">2.</span> <?php echo Arr::get($cmslabel, 'step2'); ?></a>
			<a class="wc-step step step3 step_link" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order'); ?>"><span class="num">3.</span> <?php echo Arr::get($cmslabel, 'step3'); ?></a>
		</div>

		<?php if (!empty($gdpr_template['content'])): ?>
			<?php echo str_replace(['<p>', '</p>'], " ", $gdpr_template['content']); ?>
		<?php endif; ?>
	</form>

	<div class="wc-col wc-col2 wc-step2-col2">
		<div class="wc-col2-box">
			<!-- Coupons -->
			<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>

			<!-- Cart -->
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>
			
			<div class="wc-total">
				<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
			</div>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('footer'); ?>
	<div class="wc-footer">
		<div class="wc-container">
			<div class="wc-footer-col1 support support-sidebar">
				<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
				<?php echo Arr::get($cmslabel, 'support'); ?>
			</div>
			<div class="wc-footer-col2">
				<div class="cards wc-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
				<div class="wc-copyright"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
				<span class="wc-copyright wc-copyright-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
			</div>
			<div class="wc-footer-col3 footer-badges wc-badges">
				<div class="footer-trustmark smdWrapperTag"></div>
				<?php echo Arr::get($cmslabel, 'footer_badges'); ?>
			</div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>