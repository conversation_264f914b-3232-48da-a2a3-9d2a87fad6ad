<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-thank-you<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
<div class="thank-you-section">
	<div class="thank-you-box checkout-cnt">
		<h1 class="success-title">
            <?php if (Arr::get($_GET, 'hold') == "1"): ?>
                <span data-payment_gateway_check_order="<?php echo $order->id; ?>"></span>
                <?php echo Arr::get($cmslabel, 'transaction_on_hold'); ?>
            <?php else: ?>
			    <?php echo Arr::get($cms_page, 'seo_h1'); ?>
            <?php endif; ?>
			<?php if($user): ?>
				<?php echo $user->first_name; ?>
			<?php endif; ?>
		</h1>

		<?php if ($order): ?>
			<?php echo Arr::get($cms_page, 'content'); ?>
			
			<!-- Autogenerated terms of purchase -->
			<?php $terms_pdf = Cms::page($info['lang'], 'webshop_terms', 'code', 'content_pdf'); ?>
			<?php if (Arr::get($terms_pdf, 'content_pdf')): ?>
				<p><a class="btn btn-green btn-download btn-download-pdf" href="<?php echo Utils::file_url($terms_pdf['content_pdf']);; ?>" target="_blank"><span><?php echo Arr::get($cmslabel, 'thank_you_download_terms_pdf', 'Preuzmite predugovorne obavijesti'); ?></span></a></p>
			<?php endif; ?>

			<!-- Shipping info & Invoice download -->
			<div class="invoice-container">
				<p><?php echo Arr::get($cmslabel, 'choosen_shipping', 'Odabran način dostave'); ?> "<?php echo $order->shipping->description($info['lang']); ?>".<br><?php echo Arr::get($cmslabel, 'order_email', '"E-mail sa svim detaljima poslali smo na Vaš email'); ?> <strong><?php echo $order->email; ?></strong>.</p>
				<p>
					<?php $payment_label = ($order AND $order->show_payment_transfer()) ? Arr::get($cmslabel, 'order_download_invoice_payment', 'Preuzmite potvrdu narudžbe i upute za plaćanje') : Arr::get($cmslabel, 'order_download_invoice', 'Preuzmite potvrdu narudžbe'); ?>
					<a href="<?php echo $order->get_absolute_url(); ?>?mode=pdf" class="btn btn-green btn-big btn-download-invoice" target="_blank"><?php echo $payment_label; ?></a>
				</p>
			</div>

			<!-- Signup form -->
			<?php if (Kohana::config('app.auth.frontend') AND !$order->user_id AND !$user AND !User::user_exist($order->email)): ?>
				<div class="thank-you-wrapper">
					<div class="thank-you-content">
						<?php echo Arr::get($cmslabel, 'thank_you_signup'); ?>
						<br /><br /><a class="btn btn-medium btn-green" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><?php echo Arr::get($cmslabel, 'signup'); ?></a>
					</div>
				</div>
			<?php endif; ?>
		<?php endif; ?>

		<?php if ($order AND $order->show_payment_transfer()): ?>
			<div class="thank-you-table">
				<?php echo View::factory('webshop/widget/payment_transfer', ['order' => $order]); ?>
			</div>
			<p><a class="btn btn-green btn-print" href="<?php echo $order->get_absolute_url(); ?>?mode=payment" target="_blank"><span><?php echo Arr::get($cmslabel, 'print_payment', 'Ispiši uplatnicu'); ?></span></a></p>
		<?php endif; ?>

		<?php if ($order AND $order_first AND (Kohana::$environment === 1 OR $info['site_url'] === 'https://bigbangmarkerdev.info' OR $info['site_url'] === 'http://bigbang.marker')): ?>
			<?php if ($order->shipping->code == 'osobno_preuzimanje'): ?>
				<?php $survey = Sweepstake::sweepstakes([
						'lang' => Kohana::config('app.language'),
						'mode' => 'full',
						'filters' => [
								'code' => "thank_you_questionnaires",
							],
						'single' => true,
				]);
				?>
				<?php if (!empty($survey['vote_url'])): ?>
					<a class="fancybox thank-you-fancybox" href="#tab-thank_you_questionnaires" style="display: none;"></a>
					<div class="thank-you-questionnaires" id="tab-thank_you_questionnaires" style="display: none;">
						<form action="<?php echo $survey['vote_url']; ?>" method="POST" id="thank_you_questionnaires" name="thank_you_questionnaires" class="thank-you-questionnaires-form ajax_siteform" data-siteform_response="show_hide">
							<input type="hidden" name="email" id="field-email" value="<?php echo $order->email; ?>">
							<input type="hidden" name="webshoporder" id="field-webshoporder" value="<?php echo $order->id; ?>">
							<input type="hidden" name="full_name" id="field-full_name" value="<?php echo $order->full_name; ?>">
							<input type="hidden" name="first_name" id="field-first_name" value="<?php echo $order->first_name; ?>">
							<input type="hidden" name="last_name" id="field-last_name" value="<?php echo $order->last_name; ?>">
							<input type="hidden" name="address" id="field-address" value="<?php echo $order->address; ?>">
							<input type="hidden" name="phone" id="field-phone" value="<?php echo $order->phone; ?>">
							<?php foreach ($survey['questions'] as $question): ?>
								<div class="tyq-title"><?php echo Arr::get($question, 'title'); ?></div>
								<?php echo Form::select_as_radio2("question_{$question['id']}", $question['answers_option'], Arr::get($_POST, "question_{$question['id']}", '')); ?>
								<textarea name="message" id="field-message" placeholder="<?php echo Arr::get($cmslabel, 'thank_you_questionnaires_textarea'); ?>" form="thank_you_questionnaires"></textarea>
							<?php endforeach; ?>
							<button class="btn btn-lightBlue btn-medium thq-btn" type="submit"><?php echo Arr::get($cmslabel, 'submit_form'); ?></button>
						</form>
						<div id="thank_you_questionnaires_success" style="display:none;">
							<?php echo Arr::get($cmslabel, 'thank_you_questionnaires_success', 'Uspješno'); ?>
						</div>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		<?php endif; ?>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('extrabody_top'); ?>
<?php if ($order AND !$order->get_extra_data('ec_purchase')): ?>
    <?php $order->save_extra_data('ec_purchase'); ?>
    <?php echo View::factory('webshop/widget/ecommerce_ec', array('order' => $order, 'event' => 'purchase')); ?>
<?php endif; ?>
<?php $this->endblock('extrabody_top'); ?>

<?php $this->block('extrabody'); ?>
	<?php if ($order AND $order_first AND (Kohana::$environment === 1 OR $info['site_url'] === 'https://bigbangmarkerdev.info' OR $info['site_url'] === 'http://bigbang.marker')): ?>
		<script>
		 	setTimeout(function () {
				var smdObject = {
				Key: "Slo_862",
				Type: "order",
				OrderId: "",
				Products: []
				};
				var smdWrapper = document.createElement("script");
				smdWrapper.id = "_cpxTag";
				smdWrapper.type = "text/javascript";
				smdWrapper.src = "https://cpx.smind.si/Log/LogData?data=" + encodeURIComponent(JSON.stringify(smdObject));
				var smdScript = document.getElementsByTagName("script")[0];
				smdScript.parentNode.insertBefore(smdWrapper, smdScript);
			}, 5000);
		</script>
        <?php echo View::factory('webshop/widget/affiliate_lite', array('order' => $order)); ?>
	<?php endif; ?>
<?php $this->endblock('extrabody'); ?>