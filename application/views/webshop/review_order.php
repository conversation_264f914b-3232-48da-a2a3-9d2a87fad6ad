<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step3<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header">
		<div class="wc-header-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="wc-logo" id="logo"></a>
			<div class="wc-progress-section">
				<div class="wc-progress-title"><?php echo Arr::get($cmslabel, 'checkout_progress_title'); ?></div>
				<div class="wc-progress-bar">
					<div class="wc-progress-bar-bg step4"></div>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main'); ?>
<div class="wc-container" data-tracking_gtm_checkout="4|<?php echo Utils::token_id(); ?>">
	<form class="wc-col wc-col1 wc-step4-col1 checkout-form form-animated-label ajax_siteform ajax_siteform_loading" action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'create_order', FALSE); ?>" method="post" name="webshop" id="webshop_form">
		<div class="wc-steps-active">
			<a class="wc-step active step step1 step_link2" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping'); ?>">
				<span class="title">1. <?php echo Arr::get($cmslabel, 'step1'); ?></span>
				<span class="wc-step-change"><?php echo Arr::get($cmslabel, 'change'); ?><span>
			</a>
			<a class="wc-step active step step2 step_link2" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>">
				<span class="title">2. <?php echo Arr::get($cmslabel, 'step2'); ?></span>
				<span class="wc-step-change"><?php echo Arr::get($cmslabel, 'change'); ?><span>
			</a>
		</div>

		<div class="wc-subtitle special"><span class="wc-num">3.</span> <?php echo Arr::get($cmslabel, 'step3'); ?></div>
		<?php if (Arr::get($customer_data, 'b_same_as_shipping') != 1 AND Arr::get($customer_data, 'b_first_name')): ?>
			<div class="wc-bill-address">
				<strong><?php echo Arr::get($cmslabel, 'bill_address'); ?></strong>
				
				<?php if(Arr::get($customer_data, 'b_first_name') OR Arr::get($customer_data, 'b_last_name')): ?>
					<span><?php echo Arr::get($customer_data, 'b_first_name'); ?> <?php echo Arr::get($customer_data, 'b_last_name'); ?></span>
				<?php endif; ?>

				<?php if(Arr::get($customer_data, 'b_oib')): ?>
					<span><?php echo Arr::get($cmslabel, 'oib'); ?>: <?php Arr::get($customer_data, 'b_oib'); ?></span>
				<?php endif; ?>	

				<?php if(Arr::get($customer_data, 'b_address')): ?>
					<span><?php echo Arr::get($customer_data, 'b_address'); ?></span>
				<?php endif; ?>		

				<?php if(Arr::get($customer_data, 'b_zipcode') OR Arr::get($customer_data, 'b_city')): ?>
					<span><?php echo Arr::get($customer_data, 'b_zipcode'); ?> <?php echo Arr::get($customer_data, 'b_city'); ?></span>
				<?php endif; ?>

				<?php if(Arr::get($customer_data, 'b_country')): ?>
					<span><?php echo (Arr::get($customer_data, 'b_country')) ? Jelly::query('webshopcountry', Arr::get($customer_data, 'b_country'))->select()->description($info['lang']) : ''; ?></span>
				<?php endif; ?>

				<?php if(Arr::get($customer_data, 'b_email')): ?>
					<span><?php echo Arr::get($customer_data, 'b_email'); ?></span>
				<?php endif; ?>	
			</div>
		<?php endif; ?>

		<?php if (Arr::get($customer_data, 'b_r1') == 1 OR Arr::get($customer_data, 'b_company_name')): ?>
			<div class="wc-r1">
				<strong><?php echo Arr::get($cmslabel, 'r1_bill'); ?></strong>
				<?php if(!empty(Arr::get($customer_data, 'b_company_name'))): ?>
					<span><?php echo Arr::get($cmslabel, 'company_name'); ?>: <?php echo Arr::get($customer_data, 'b_company_name'); ?></span>
				<?php endif; ?>
				<?php if(!empty(Arr::get($customer_data, 'b_company_oib'))): ?>
					<span><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?></span>
				<?php endif; ?>
				<?php if(Arr::get($customer_data, 'b_company_address')): ?>
					<span><?php echo Arr::get($cmslabel, 'company_address'); ?>: <?php echo Arr::get($customer_data, 'b_company_address'); ?></span>
				<?php endif; ?>
			</div>
		<?php endif; ?>

		<div class="wc-cart-totals">
			<div class="clear cart-totals">
				<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'delivery_method'); ?></span>
				<span class="w-totals-value"><?php echo $shipping_display; ?></span>
			</div>

			<div class="clear cart-totals">
				<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'payment_solution'); ?></span>
				<span class="w-totals-value cart_info_total_items_ways_of_payment"><?php echo $payment_display; ?></span>
			</div>

            <?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info, 'accept_terms_field' => (isset($accept_terms_field) AND isset($accept_terms_error)))); ?>
		</div>

		<div class="wc-step3-col1-bottom">
			<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>			
				<div class="minprice-tooltip">	
				<?php echo str_replace(array(
						'%TOTAL_MIN%', 
						'%TOTAL_MISSING%', 
						), 
					array(
						Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']),
						Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']),
					),
					Arr::get($cmslabel, 'minimal_order_price_full'));
				?>
				</div>
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-large btn-finish"><?php echo Arr::get($cmslabel, 'edit_shopping_cart'); ?></a> 
			<?php else: ?>
				<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>

					<!--<div class="webshop-alert-terms" data-accept_terms="note">
						<span><?php echo Arr::get($cmslabel, 'terms_error', 'Before buying you must accept the BigBang Terms & conditions'); ?></span>
					</div>
					<div class="webshop-accept-terms-cnt">
						<?php echo View::factory('webshop/widget/accept_terms', array('accept_terms_field' => $accept_terms_field, 'accept_terms_error' => $accept_terms_error)); ?>
					</div>-->
				<?php endif; ?>

				<?php if (isset($accept_terms_2_field) AND isset($accept_terms_2_error)): ?>
					<?php echo View::factory('webshop/widget/accept_terms_2', array('accept_terms_field' => $accept_terms_2_field, 'accept_terms_error' => $accept_terms_2_error)); ?>
				<?php endif; ?>

				<?php if (!empty($gdpr_template['content'])): ?>
					<?php echo str_replace(['<p>', '</p>'], " ", $gdpr_template['content']); ?>
				<?php endif; ?>

				<button type="submit" class="btn btn-green btn-finish"><?php echo Arr::get($cmslabel, 'confirm_order'); ?></button>
			<?php endif; ?>
		</div>
	</form>

	<div class="wc-col wc-col2 wc-step3-col2">
		<div class="wc-col2-box">
			<!-- Coupons -->
			<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>

			<!-- Cart -->
			<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>
			
			<div class="wc-total">
				<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
			</div>

			<?php if(!empty($shopping_cart_info['forbid_r1'])): ?>
                <div>
                    <b><?php echo Arr::get($cmslabel, 'notification'); ?></b>
                    <p><?php echo Arr::get($cmslabel, 'forbid_r1_info'); ?></p>
                </div>
            <?php endif; ?>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('footer'); ?>
	<div class="wc-footer">
		<div class="wc-container">
			<div class="wc-footer-col1 support support-sidebar">
				<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
				<?php echo Arr::get($cmslabel, 'support'); ?>
			</div>
			<div class="wc-footer-col2">
				<div class="cards wc-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
				<div class="wc-copyright"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
				<span class="wc-copyright wc-copyright-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
			</div>
			<div class="wc-footer-col3 footer-badges wc-badges">
				<div class="footer-trustmark smdWrapperTag"></div>
				<?php echo Arr::get($cmslabel, 'footer_badges'); ?>
			</div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>