<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout-step1<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('main'); ?>
<div class="auth-box" data-tracking_gtm_checkout="1|<?php echo Utils::token_id(); ?>">
	<div class="wrapper">
		<?php if (!$user): ?>
			<div class="wcs-col wcs-col1 wcs-step1-col1">
				<h2 class="wc-subtitle auth-title"><?php echo Arr::get($cmslabel, 'guest_checkout', 'I want to buy as a guest'); ?></h2>
				<div class="wcs-cnt"><?php echo Arr::get($cms_page, 'content'); ?></div>
				<form class="form-animated-label wcs-guest-form ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					<input type="hidden" name="guest_checkout" value="1" />
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="fz0 field field-email">
						<label for="field-email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<input type="email" name="email" id="field-email" />
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<button class="btn btn-green btn-wcs-guest" type="submit" name="guest_checkout" value="1"><span><?php echo Arr::get($cmslabel, 'continue_without_signup', 'Nastavi kao gost'); ?></span></button>
				</form>
			</div>

			<div class="wcs-col wcs-col2 wcs-step1-col2">
				<h2 class="wcs-subtitle auth-title"><?php echo Arr::get($cmslabel, 'already_register', 'You already have an account?'); ?></h2>
				<div class="wcs-cnt"><?php echo Arr::get($cmslabel, 'login_to_buy', 'Prijavite se putem emaila za brzu kupnju'); ?></div>
				<a class="btn btn-medium btn-green" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><?php echo Arr::get($cmslabel, 'login_to_buy_button'); ?></a>
			</div>
		<?php else: ?>
			<form class="ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">	
				<p class="loggedin-next-step"><button class="btn-big" type="submit" name="already_login" value="1"><?php echo Arr::get($cmslabel, 'goto_step2_button', 'Nastavi na sljedeći korak'); ?></button></p>
			</form>
		<?php endif; ?>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>