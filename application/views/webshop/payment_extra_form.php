<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?>
<?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?>
    <meta http-equiv="content-language" content="sl" />
<?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step4 page-bankart-payjs <?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
<?php echo Html::media('/media/cart.css', 'css'); ?>
    <script data-main="payment-js" src="<?php echo $response_data['script_url'] ?>"></script>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('header'); ?>
    <div class="wc-header">
        <div class="wc-header-wrapper">
            <a href="<?php echo Utils::homepage($info['lang']); ?>" class="wc-logo" id="logo"></a>
            <div class="wc-progress-section">
                <div class="wc-progress-title wc-pef-title"><strong><?php echo Arr::get($cms_page, 'title'); ?> <?php echo $order->payment->description($info['lang']); ?></strong></div>
            </div>
        </div>
    </div>
<?php $this->endblock('header'); ?>

<?php $this->block('main'); ?>
    <div class="wc-container">
        <div class="wc-col wc-col1 wc-sŁ-col1">
            <div class="wc-col1-cnt wc-step-col1-cnt wc-step3-col1-cnt bankart_payment_js_form">
                <div class="wc-global-error global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>

                <?php
                $selected_payment_config = Text::config_to_array($order->payment->credit_card_config);
                $exp_field_merge = Arr::get($selected_payment_config, 'exp_field_merge', 0);
                if (!empty(Arr::get($_GET, 'errorcode', ''))): ?>
                    <div class="wc-global-error error global_error global-error">
                        <?php
                        $error_code = Arr::get($_GET, 'errorcode', '');
                        $error_message = '';

                        $payment_errors = Arr::get($cmslabel, 'bankart_error_codes', '');
                        $payment_errors_exploded = explode("\n", $payment_errors);
                        foreach ($payment_errors_exploded as $payment_error) {
                            if (strpos($payment_error, '|') === false) {
                                continue;
                            }
                            list($payment_error_code, $payment_error_message) = explode('|', $payment_error);
                            if ($payment_error_code == $error_code) {
                                $error_message = $payment_error_message;
                                break;
                            }
                        }
                        if (empty($error_message)) {
                            $error_message = Arr::get($cmslabel, 'bankart_error_code_generic');
                        }
                        ?>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif;
                ?>

                <form class="form-animated-label wc-payment-extra-form" id="payment_form" method="POST" action="#" onsubmit="interceptSubmit(); return false;">
                    <input type="hidden" name="transaction_token" id="transaction_token"/>
                    <input type="hidden" name="order_id" id="order_id" value="<?php echo $response_data['order_id']; ?>"/>
                    <input type="hidden" name="hold_url" id="hold_url" value="<?php echo $response_data['payment_hold_url']; ?>"/>
                    <input type="hidden" name="cancel_url" id="cancel_url" value="<?php echo $response_data['cancel_url']; ?>"/>
                    <input type="hidden" name="shared_key" id="shared_key" value="<?php echo $response_data['shared_key']; ?>"/>

                    <div class="wc-pef-row">
                        <p class="field field-first_name">
                            <label for="first_name"><?php echo Arr::get($cmslabel, 'first_name', 'Ime'); ?></label>
                            <input type="text" id="first_name" name="first_name"  data-siteform_event="2" value="<?php echo $order->first_name; ?>"/>
                            <span class="error error_first_name" style="display: none"><?php echo Arr::get($cmslabel, 'bankart_error_name'); ?></span>
                        </p>
                        <p class="field field-last_name">
                            <label for="last_name"><?php echo Arr::get($cmslabel, 'last_name', 'Priimek'); ?></label>
                            <input type="text" id="last_name" name="last_name"  data-siteform_event="2" value="<?php echo $order->last_name; ?>"/>
                        </p>
                    </div>
                    <p class="pef-cc-field field-cc_number">
                        <label for="number_div"><?php echo Arr::get($cmslabel, 'cc_number', 'Številka kartice'); ?></label>
                        <div class="pef-cc-field-container" id="cc_number"></div>
                        <span class="error error_cc_number" style="display: none"><?php echo Arr::get($cmslabel, 'bankart_error_cc_number'); ?></span>
                    </p>
                    <div class="wc-pef-row">
                        <?php if (!empty($exp_field_merge)): ?>
                            <p class="field wc-pef" data-exp_field_merge="<?php echo $exp_field_merge ?>">
                                <label for="exp_month_year"><?php echo Arr::get($cmslabel, 'cc_expire'); ?></label>
                                <input type="text" id="exp_month_year" name="exp_month_year" data-siteform_event="2" class="" maxlength="7" autocomplete="cc-exp" autocorrect="off" spellcheck="false" value="">
                                <span class="error error_exp_month_year" style="display: none"><?php echo Arr::get($cmslabel, 'bankart_error_cc_expire'); ?></span>
                                <input type="hidden" id="exp_month" name="exp_month" autocomplete="off" maxlength="2" min="1" max="12" " />
                                <input type="hidden" id="exp_year" name="exp_year" autocomplete="off" maxlength="2" min="<?php echo date('y'); ?>" max="<?php echo date('y') + 10; ?>" />
                            </p>
                        <?php else: ?>
                            <p class="field wc-pef-date">
                                <input type="number" onkeypress="return isNumber(this,event)" id="exp_month" name="exp_month" autocomplete="off" maxlength="2" min="1" max="12" oninvalid="setCustomValidity('Število mora biti večje od 0 in največ 12');" oninput="setCustomValidity('');" />
                                <span class="pef-date-label"><?php echo Arr::get($cmslabel, 'cc_expire'); ?></span>
                                <span class="pef-date-separator">/</span>
                                <input type="number" onkeypress="return isNumber(this,event)" id="exp_year" name="exp_year" autocomplete="off" maxlength="2" min="<?php echo date('y'); ?>" max="<?php echo date('y') + 10; ?>"  oninvalid="setCustomValidity('Število mora biti večje ali enako <?php echo date('y'); ?> in največ <?php echo date('y') + 10; ?>');" oninput="setCustomValidity('');" />
                            </p>
                        <?php endif; ?>
                        <div class="wc-pef-cvv">
                            <p class="pef-cc-field field-cc_cvv">
                                <label for="cvv_div"><?php echo Arr::get($cmslabel, 'cc_cvv', 'CVV'); ?></label>
                                <div class="pef-cc-field-container" id="cc_cvv"></div>
                                <span class="error error_cvv" style="display: none"><?php echo Arr::get($cmslabel, 'bankart_error_cc_cvv'); ?></span>
                                <div class="cvv-info">
                                    <div class="cvv-label"><?php echo Arr::get($cmslabel, 'cvv_info'); ?><span class="info-icon"></span></div>
                                    <div class="cvv-tooltip">
                                        <span class="cvv-info-close"></span>
                                        <?php echo Arr::get($cmslabel, 'cvv_info_tooltip'); ?>
                                    </div>
                                </div>
                            </p>
                        </div>
                    </div>
                    <input type="hidden" id="email" name="email" value="<?php echo $order->email; ?>"/>
                    <div class="wc-pef-bottom">
                        <button class="btn btn-green btn-checkout" type="submit"><?php echo Arr::get($cmslabel, 'paid', 'Plačaj'); ?></button>
                        <a class="btn btn-white" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'failed_payment'); ?>?order_identificator=<?php echo $order_identificator; ?>" class="btn btn-white button4 float-right"><?php echo Arr::get($cmslabel, 'canceled'); ?></a>
                        <div class="wc-pef-bottom-info"><?php echo Arr::get($cmslabel, 'payment_extra_description'); ?></div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Column 2 -->
        <div class="wc-col wc-col2 wc-step-col2 wc-step3-col2">
            <div class="wc-col2-box">
                <?php if (!empty($content_category) AND $content_category == 'additpayment'): ?>
                    <div class="shoppingcart_items_small wc-cart-small">
                        <div class="w-cart-header w-pef-cart-header">
                            <h2 class="w-cart-title"><?php echo Arr::get($cmslabel, 'your_order_additpayment', 'Doplačilo'); ?></h2>
                        </div>
                    </div>

                    <div class="wc-total wc-pef-total wc-step-3-total">
                        <div class="clear cart-totals">
                            <div class="ww-total cart-total">
                                <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay_additpayment', 'Znesek za doplačilo'); ?>:</span>
                                <span class="w-totals-value cart-info-special-total value"><?php echo Utils::currency_format($order->total * $order->exchange, $order->currency->display); ?></span>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="shoppingcart_items_small wc-cart-small">
                        <div class="w-cart-header w-pef-cart-header">
                            <h2 class="w-cart-title"><?php echo Arr::get($cmslabel, 'your_order', 'Tvoje naručilo'); ?></h2>
                        </div>
                    </div>

                    <div class="wc-total wc-pef-total wc-step-3-total">
                        <div class="clear cart-totals">
                            <div class="ww-total cart-total">
                                <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>:</span>
                                <span class="w-totals-value value"><?php echo $order->id; ?></span>
                            </div>
                            <div class="ww-total cart-total">
                                <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay', 'Skupaj'); ?>:</span>
                                <span class="w-totals-value cart-info-special-total value"><?php echo Utils::currency_format($order->total * $order->exchange, $order->currency->display); ?></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('footer'); ?>
    <div class="wc-footer">
        <div class="wc-container">
            <div class="wc-footer-col1 support support-sidebar">
                <div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
                <?php echo Arr::get($cmslabel, 'support'); ?>
            </div>
            <div class="wc-footer-col2">
                <div class="cards wc-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
                <div class="wc-copyright"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
                <span class="wc-copyright wc-copyright-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
            </div>
            <div class="wc-footer-col3 footer-badges wc-badges">
                <div class="footer-trustmark smdWrapperTag"></div>
                <?php echo Arr::get($cmslabel, 'footer_badges'); ?>
            </div>
        </div>
    </div>
<?php $this->endblock('footer'); ?>