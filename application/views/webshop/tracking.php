<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>">
<head>
    <?php if ($order): ?>
        <script>
            var dataLayer = window.dataLayer || [];
            <?php if (!empty($gdpr_objects)): ?>
            <?php
            $cookie_gdpr_all = array_column($gdpr_objects, 'code');
            $cookie_gdpr_approved = array_filter(explode('|', Arr::get($_COOKIE, "gdpr_cookie")));
            $cookie_gdpr_approved_total = (!empty($cookie_gdpr_all) AND !empty($cookie_gdpr_approved)) ? count(array_intersect($cookie_gdpr_all, $cookie_gdpr_approved)) : 0;
            if ($cookie_gdpr_approved_total == 0 AND empty(Arr::get($_COOKIE, "gdpr_cookie"))) {
                array_push($cookie_gdpr_approved, 'analytics');
            }

            $cookie_gdpr_statuses = [];
            foreach ($cookie_gdpr_all AS $cookie_gdpr_all_single) {
                $cookie_gdpr_statuses[$cookie_gdpr_all_single] = (in_array($cookie_gdpr_all_single, $cookie_gdpr_approved)) ? 'true' : 'false';
            }
            $cookie_gdpr_statuses['all_cookies'] = ($cookie_gdpr_approved_total == count($cookie_gdpr_all)) ? 'true' : 'false';
            ?>
            dataLayer.push({<?php echo Arr::implode_r(', ', $cookie_gdpr_statuses, true, ':'); ?>});
            <?php endif; ?>
        </script>
        <?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
    <?php endif; ?>
    <title><?php echo Arr::get($cmslabel, 'waiting_messages', 'Pričekajte trenutak...'); ?>...</title>
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
</head>
<body>

<?php if ($order AND $order_first): ?>
    <?php echo View::factory('webshop/widget/ecommerce_ec', array('order' => $order, 'event' => 'purchase')); ?>
    <?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
<?php endif; ?>
<?php if ($order): ?>
    <?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
<?php endif; ?>

<div style="text-align: center;"><br><img src="/media/images/logo-mail.png"><br><br>
    <form action="<?php echo $redirect_url; ?>" method="post" name="redirect_confirm">
        <input name="" type="submit" value="<?php echo Arr::get($cmslabel, 'waiting_messages', 'Pričekajte trenutak...'); ?>..." style="border: 0; background: #fff" />
    </form>
</div>
<?php echo Html::media('jquery', 'js', false); ?>
<script>
    $(function(){
        setTimeout(function () {
            $('form[name="redirect_confirm"]').submit();
        }, 2000);
    });
</script>
</body>
</html>