<?php $this->extend('default'); ?>

<?php $this->block('page_class'); ?> page-checkout page-checkout-step2<?php $this->endblock('page_class'); ?>
<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits'); ?> <?php $this->endblock('benefits'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header wc-header-special">
		<div class="wc-header-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="wc-logo" id="logo"></a>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('main'); ?>
<?php if (count($payment_second_options)): ?>
    <?php
    $error_code = $order->error_paid_code ?? '';
    $error_message = '';
    if ($error_code) {
        $payment_errors = Arr::get($cmslabel, 'bankart_error_codes', '');
        $payment_errors_exploded = explode("\n", $payment_errors);

        foreach ($payment_errors_exploded as $payment_error) {
            if (strpos($payment_error, '|') === false) {
                continue;
            }
            list($payment_error_code, $payment_error_message) = explode('|', $payment_error);
            if ($payment_error_code == $error_code) {
                $error_message = $payment_error_message;
                break;
            }
        }
    }
    ?>
    <?php if (!empty($content_category) AND $content_category == 'additpayment'): ?>
        <div class="wc-container">
            <div class="wc-col1-cnt wc-col-second-form">
                <form class="checkout-form ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form">
                    <div class="wc-subtitle"><?php echo Arr::get($cmslabel, 'new_payment_h1_additpayment', 'new_payment_h1_additpayment'); ?></div>
                    <?php if ($error_message): ?>
                        <div class="global-error wc-global-error"><?php echo $error_message; ?></div>
                    <?php endif; ?>

                    <div class="wc-order-info">
                        <span><?php echo Arr::get($cmslabel, 'order_id'); ?>: <strong><?php echo $order->number; ?></strong></span>
                        <span><?php echo Arr::get($cmslabel, 'total'); ?>: <strong><?php echo Utils::currency_format($order->total * $order->exchange, $order->currency->display); ?></strong></span>
                    </div>

                    <div class="wc-subtitle wc-subtitle-label"><?php echo Arr::get($cmslabel, 'new_payment_content_additpayment', 'new_payment_content_additpayment'); ?></div>
                    <div class="wc-second-payment-new"><?php echo Form::select_as_radio2('payment', $payment_second_options, Arr::get($customer_data, 'payment')); ?></div>
                    <div class="clear"></div>

                    <div class="wc-second-payment-btns">
                        <button class="btn btn-green" type="submit" name="new_payment" value="1"><?php echo Arr::get($cmslabel, 'save_new_payment'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="wc-container">
            <div class="wc-col1-cnt wc-col-second-form">
                <form class="checkout-form ajax_siteform ajax_siteform_loading" action="#webshop_form" method="post" name="webshop" id="webshop_form" data-webshop_second_form="1">
                    <div class="wc-subtitle"><?php echo Arr::get($cmslabel, 'new_payment_h1'); ?></div>
                    <?php if ($error_message): ?>
                        <div class="global-error wc-global-error"><?php echo $error_message; ?></div>
                    <?php endif; ?>

                    <div class="wc-subtitle wc-subtitle-label"><?php echo Arr::get($cmslabel, 'new_payment_info'); ?></div>
                    <div class="wc-order-info">
                        <span><?php echo Arr::get($cmslabel, 'order_id'); ?>: <strong><?php echo $order->id; ?></strong></span>
                        <span><?php echo Arr::get($cmslabel, 'total'); ?>: <strong data-installments_price="<?php echo Utils::currency_format($order->total * $order->exchange, $order->currency->display); ?>" data-total_only="1"><?php echo Utils::currency_format($order->total * $order->exchange, $order->currency->display); ?></strong></span>
                    </div>

                    <div class="wc-subtitle wc-subtitle-label"><?php echo Arr::get($cmslabel, 'new_payment_content'); ?></div>
                    <div class="wc-second-payment-new"><?php echo Form::select_as_radio2('payment', $payment_second_options, Arr::get($customer_data, 'payment')); ?></div>
                    <div class="clear"></div>

                    <div class="wc-second-payment-btns">
                        <button class="btn btn-green" type="submit" name="new_payment" value="1"><?php echo Arr::get($cmslabel, 'save_new_payment'); ?></button>
                        <button class="btn btn-white" type="submit" name="canceled" value="1" class="button4 float-right"><?php echo Arr::get($cmslabel, 'canceled_order'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

<?php else: ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>
<?php endif; ?>

<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>

<?php $this->block('footer'); ?>
	<div class="wc-footer">
		<div class="wc-container">
			<div class="wc-footer-col1 support support-sidebar">
				<div class="support-title"><?php echo Arr::get($cmslabel, 'customer_support'); ?></div>
				<?php echo Arr::get($cmslabel, 'support'); ?>
			</div>
			<div class="wc-footer-col2">
				<div class="cards wc-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
				<div class="wc-copyright"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></div>
				<span class="wc-copyright wc-copyright-extra"><?php echo Arr::get($cmslabel, 'copyright_extra'); ?></span>
			</div>
			<div class="wc-footer-col3 footer-badges wc-badges">
				<div class="footer-trustmark smdWrapperTag"></div>
				<?php echo Arr::get($cmslabel, 'footer_badges'); ?>
			</div>
		</div>
	</div>
<?php $this->endblock('footer'); ?>