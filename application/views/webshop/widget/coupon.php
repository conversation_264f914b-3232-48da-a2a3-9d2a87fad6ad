<?php if (Kohana::config('app.webshop.use_coupons')): ?>
	<?php $have_coupon = (isset($shopping_cart_info['total_extra_coupon_code']) AND count($shopping_cart_info['total_extra_coupon_code'])); ?>
	<?php $have_coupon_product = (isset($shopping_cart_info['total_extra_coupon_product_code']) AND count($shopping_cart_info['total_extra_coupon_product_code'])); ?>
	<div class="ww-coupons<?php if ($have_coupon OR $have_coupon_product): ?> active<?php endif; ?>" data-coupon_active="webshop_coupon">
		<!-- Add coupon form -->
		<div class="ww-coupons-form">
			<label for="coupon_code" class="ww-coupons-label"><?php echo Arr::get($cmslabel, 'coupon_have_coupon', 'Imaš kupon ili poklon bon'); ?></label>
			<div class="ww-coupons-add">
				<input type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
				<a class="btn btn-white ww-btn-add ww-btn-add-special" href="javascript:cmscoupon.set('webshop_coupon', '', true)"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
				<div class="coupon_message" style="display: none"></div>
			</div>
		</div>

		<!-- Used coupon -->
		<?php $active_coupons = Arr::get($shopping_cart_info, 'total_extra_coupon_code'); ?>
		<div class="ww-coupons-active">
			<span class="ww-coupons-title"><?php echo str_replace('%COUPON_CODE%', implode(',', $shopping_cart_info['total_extra_coupon_code']), Arr::get($cmslabel, 'coupon_included_code')); ?></span>
			<a class="ww-coupon-delete" href="javascript:cmscoupon.remove('webshop_coupon', '_all', true);"><span><?php echo Arr::get($cmslabel, 'coupon_remove', 'Ukloni'); ?></span></a>
		</div>

		<!-- List of available coupons -->
		<?php $coupons = Webshop::coupons(array('lang' => $info['lang'], 'only_my' => TRUE, 'user_email' => $info['user_email'])); ?>
		<?php if($coupons): ?>
			<div class="ww-coupons-list">
				<h5 class="ww-coupons-list-title"><?php echo Arr::get($cmslabel, 'coupon_available', 'Dostupni kuponi'); ?></h5>
				<table class="ww-coupons-table">
				<?php foreach ($coupons AS $coupon): ?>	
					<tr class="<?php if ($active_coupons AND in_array($coupon['code'], $active_coupons)): ?>active<?php endif; ?>" data-coupon_active="webshop_coupon_<?php echo $coupon['code']; ?>">
						<td class="col-ccode"><?php echo $coupon['code']; ?></td>
						<td class="col-type"><strong><?php echo ($coupon['type'] == 'f') ? Utils::currency_format($coupon['coupon_price'],$currency['display']) : ($coupon['coupon_percent']*100).'%'; ?></strong></td>
						<td class="col-link">
							<a class="btn-coupon-add" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $coupon['code']; ?>', true);"><?php echo Arr::get($cmslabel, 'coupon_use', 'Koristi'); ?></a>
						</td>
					</tr>
				<?php endforeach; ?>
				</table>
			</div>	
		<?php endif; ?>	
	</div>
<?php endif; ?>