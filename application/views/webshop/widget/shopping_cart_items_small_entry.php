<div class="wp" id="product-<?php echo $product_code; ?>">
	<div class="wp-image">
		<figure>
			<img loading="lazy" <?php echo Thumb::generate($product_data['main_image'], array('width' => 90, 'height' => 90, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-70.webp', 'srcset' => '180c 2x')); ?> alt="<?php echo $product_data['title']; ?>" />
		</figure>
	</div>
	<div class="wp-cnt wp-inner">
		<div class="wp-category"><?php echo $product_data['category_title']; ?></div>
		<div class="wp-title"><?php echo $product_data['title']; ?></div>
		<div class="wp-code"><?php echo Arr::get($cmslabel, 'id'); ?>: <span><?php echo (!empty($product_data['variation'])) ? $product_data['variation_code'] : $product_data['code']; ?></span></div>
		<?php if (!empty($product_status['services'])): ?>
			<div class="wp-services">	
				<?php foreach ($product_status['services'] AS $item_service): ?>
					<?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
						<?php if (empty($item_service_item['selected'])) {continue;} ?> 
						<div class="wp-service">
							+ <?php echo $item_service_item['title']; ?> (<strong><span class="product_qty" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> x <?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?>)</strong>
						</div>
					<?php endforeach; ?>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
		<?php if (!empty($product_data['coupon_price'])): ?>
			<?php echo Arr::get($cmslabel, 'coupon_value'); ?>: <?php echo $product_data['coupon_price']; ?>
		<?php elseif (!empty($product_data['bonus_total'])): ?>
			<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
		<?php endif; ?>					
		<?php if ($product_data['type'] == 'coupon'): ?>
			<div class="wp-coupon">
				<?php echo str_replace('%TOTAL%', (int) $product_status['qty'], Arr::get($cmslabel, 'coupon_product_intro')); ?>
				<input type="hidden" name="personmessage[<?php echo $product_code; ?>][title]" value="<?php echo Text::meta($product_data['title']); ?>">
				<div class="couponrecipients">
					<?php $couponrecipients = ((isset($customer_data['couponrecipient'][$product_code])) ? $customer_data['couponrecipient'][$product_code] : []); ?>
					<?php for ($i = 1; $i <= $product_status['qty']; $i++): ?>
					<div class="wp-coupon-item">
						<div class="wp-coupon-title"><?php echo Arr::get($cmslabel, 'coupon_delivery'); ?> #<?php echo $i; ?></div>
						<?php $couponrecipient = ((isset($couponrecipients[$i])) ? $couponrecipients[$i] : []); ?>
						<?php $couponrecipienttype = Arr::get($couponrecipient, 'type', 'email'); ?>

						<input type="radio" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][type]" data-couponrecipient="<?php echo $product_code; ?>-<?php echo $i; ?>" id="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-email" value="email" <?php if ($couponrecipienttype == 'email'): ?>checked<?php endif; ?>> <label for="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-email">Emailom</label>
						<input type="radio" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][type]" data-couponrecipient="<?php echo $product_code; ?>-<?php echo $i; ?>" id="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-address" value="address" <?php if ($couponrecipienttype == 'address'): ?>checked<?php endif; ?>> <label for="field-couponrecipient-<?php echo $product_code; ?>-<?php echo $i; ?>-address">Poštom</label>
						<span data-couponrecipient_extra="<?php echo $product_code; ?>-<?php echo $i; ?>-email" <?php if ($couponrecipienttype == 'email'): ?>class="active"<?php endif; ?>>
							<input type="text" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][email]" maxlength="60" placeholder="Email adresa" value="<?php echo Arr::get($couponrecipient, 'email'); ?>">
							<span class="coupon-product-note"><?php echo Arr::get($cmslabel, 'coupon_email_note'); ?></span>
						</span>
						<span data-couponrecipient_extra="<?php echo $product_code; ?>-<?php echo $i; ?>-address" <?php if ($couponrecipienttype == 'address'): ?>class="active"<?php endif; ?>>
							<input type="text" name="couponrecipient[<?php echo $product_code; ?>][<?php echo $i; ?>][address]" maxlength="220" placeholder="Ime i adresa primatelja" value="<?php echo Arr::get($couponrecipient, 'address'); ?>">
							<span class="coupon-product-note"><?php echo Arr::get($cmslabel, 'coupon_address_note'); ?></span>
						</span>
					</div>
					<?php endfor; ?>
				</div>
			</div>
		<?php endif; ?>
	</div>
	<div class="wp-total wp-inner">
		<div class="wp-price">
            <?php if (($product_status['total_basic'] > $product_status['total']) > 0.01): ?>
                <div <?php if(!$product_status['discount']): ?>style="display: none"<?php endif; ?> class="wp-price-old line-through product_total_basic" data-shoppingcart_product_total_basic="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></div>
				<div class="wp-price-discount wp-price-current red product_total" data-shoppingcart_product_total="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
			<?php else: ?>
				<div class="wp-price-current product_total" data-shoppingcart_product_total="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
			<?php endif ?>
		</div>					
		<?php /* ?>
		<div class="wp-qty-count wp-price-old">
			<span class="product_qty"><?php echo $product_status['qty']; ?></span> x <span data-shoppingcart_product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['price_without_service'] * $currency['exchange'], $currency['display']); ?></span>
		</div>
		<?php */ ?>
		<?php if (Kohana::config('app.catalog.multitax')): ?>
			<div class="wp-multitax"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php echo ($product_status['tax'] * 100); ?>%</div>
		<?php endif; ?>
	</div>
</div>