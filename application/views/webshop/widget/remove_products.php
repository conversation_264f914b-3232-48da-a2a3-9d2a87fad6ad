<?php
$products = Catalog::products([
    'lang' => $info['lang'],
    'filters' => [
        'id' => $products_ids,
    ],
    'mode' => 'shopping_cart',
    'limit' => count($products_ids),
    ]);
$remove_products = [];
?>

<div class="payment-disabled-info">
    <?php echo Arr::get($cmslabel, $code . '_disabled_title'); ?>
</div>
<div class="payment-disabled-section">
    <span class="payment-disabled-label"><?php echo Arr::get($cmslabel, $code . '_disabled_description'); ?></span>
    <div class="payment-disabled-items">
        <?php foreach ($products AS $product_missing): ?>
        <?php array_push($remove_products, $product_missing['shopping_cart_code']); ?>
            <div class="payment-disabled-item">
                <div class="pdi-image">
                    <figure>
                        <img <?php echo Thumb::generate($product_missing['main_image'], array('width' => 50, 'height' => 50, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.webp', 'srcset' => '180c 2x')); ?> alt="" />
                    </figure>
                </div>
                <div class="pdi-col">
                    <div class="pdi-cnt">
                        <div class="pdi-title"><?php echo $product_missing['title']; ?></div>
                        <div class="pdi-code"><?php echo Arr::get($cmslabel, 'id'); ?>: <span><?php echo $product_missing['code']; ?></span></div>
                    </div>
                    <div class="pdi-price"><?php echo Utils::currency_format($product_missing['price'] * $currency['exchange'], $currency['display']); ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="payment-disabled-buttons">
        <a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-lightBlue pdi-btn-change"><?php echo Arr::get($cmslabel, 'change_cart_short'); ?></a>
        <a href="javascript:void(0);" class="btn pdi-btn-remove" data-remove_products_shippingpayment="<?php echo implode(',', $remove_products); ?>"><?php echo Arr::get($cmslabel, $code . '_remove_items');  ?></a>
    </div>
</div>