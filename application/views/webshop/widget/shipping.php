<?php
$shippings = Webshop::shippings(['lang' => $info['lang']]);
$selected_shipping = $shopping_cart_info['selected_shipping'];

$shipping_address = Html::customer_address($customer_data, $info['lang'], Arr::extract($cmslabel, ['oib']));
$shipping_address .= '<br><a href="'.Utils::app_absolute_url($info['lang'], 'webshop', 'shipping').'#webshop_form" class="btn-change-address"><span>'.Arr::get($cmslabel, 'change_shipping_address', 'Promijeni adresu dostave').'</span></a>';

$shipping_service_enabled = Kohana::config('app.webshop.shipping_service_enabled');
?>
<?php foreach ($shippings AS $shipping): ?>
	<?php 
	if (!empty($shipping_service_enabled[$shipping['code']])) {
		$need_services = $shipping_service_enabled[$shipping['code']];
		$exist_services = 0;

		foreach ($need_services AS $need_service_code) {
			if (!empty($shopping_cart_info['products_service_item'][$need_service_code])) {
				$exist_services += $shopping_cart_info['products_service_item'][$need_service_code];
			}
			if ($shipping['code'] == 'hitra_dostava') {
                $exist_services += 1;
            }
		}

		if (empty($exist_services)) {
            unset($shippings[$shipping['id']]);
			continue;
		}
	}

    if ($shipping['code'] == 'osobno_preuzimanje') {
        $available_location_points = Location::points(['lang' => $info['lang'], 'check_forced' => true, 'available_pickup' => true, 'filters' => ['distance_point' => true]]);
        if (empty($available_location_points)) {
            continue;
        }
    }

    if (!in_array($selected_shipping, array_keys($shippings))){
        $selected_shipping = key($shippings);
    }	

	?>

	<div class="field-shipping-row" >
		<span class="field radio-field">
		<?php $shipping_selected = ($selected_shipping == $shipping['id'] AND isset($shopping_cart_info['available_shippings_option'][$shipping['id']])); ?>
		<input type="radio" name="shipping" value="<?php echo $shipping['id']; ?>" id="field-shipping-<?php echo $shipping['id']; ?>" <?php if ($shipping_selected): ?>checked<?php endif; ?><?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>disabled<?php endif; ?>>
		<label for="field-shipping-<?php echo $shipping['id']; ?>"><?php echo $shipping['title']; ?></label>
		</span>
		<span class="estimated-delivery">
			<?php if ($shipping['code'] == 'dostavna_sluzba' AND !empty($shopping_cart['selected_shipping_expect_date'])): ?>
				<?php 
				$calendar_month = Kohana::config('app.utils.calendar.si.month');
				$calendar_days = Kohana::config('app.utils.calendar.si.days_full');
				$shipping_date_day = date('w', $shopping_cart['selected_shipping_expect_date']);
				$shipping_date_month = (date('n', $shopping_cart['selected_shipping_expect_date']) - 1);

				if (!empty($calendar_days[$shipping_date_day]) AND !empty($calendar_month[$shipping_date_month])) {
					if (date('Y-m-d', $shopping_cart['selected_shipping_expect_date']) == date('Y-m-d', time())) {
						$shipping_date = date('d', $shopping_cart['selected_shipping_expect_date']).'.'.date('m', $shopping_cart['selected_shipping_expect_date']) . '.';
					} elseif (date('Y-m-d', $shopping_cart['selected_shipping_expect_date']) == date('Y-m-d', strtotime('+1day'))) {
						$shipping_date = date('d', $shopping_cart['selected_shipping_expect_date']).'.'.date('m', $shopping_cart['selected_shipping_expect_date']) . '.';
					} else {
						$shipping_date = $calendar_days[$shipping_date_day].', '.date('d', $shopping_cart['selected_shipping_expect_date']).'.'.date('m', $shopping_cart['selected_shipping_expect_date']) . '.';
					}
				} else {
					$shipping_date = strftime("%a %e %B", $shopping_cart['selected_shipping_expect_date']);
				}
				?>
				<?php echo Arr::get($cmslabel, 'estimated_delivery'); ?> 
				<?php if (date('Y-m-d', $shopping_cart['selected_shipping_expect_date']) == date('Y-m-d', time())):  ?>
					<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
				<?php elseif (date('Y-m-d', $shopping_cart['selected_shipping_expect_date']) == date('Y-m-d', strtotime('+1day'))):  ?>
					<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
				<?php else: ?>
					<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
				<?php endif; ?>
			<?php endif; ?>
		</span>
		<span class="price">
			<?php if ($shipping['code'] == 'dostavna_sluzba'): ?>
				<strong><!--€3,99 / €5,99--></strong>
			<?php elseif ($shipping['code'] == 'bigbang_dostavna'): ?>	
				<strong><!--€14,99 / €22,49--></strong>
			<?php elseif ($shipping['code'] == 'osobno_preuzimanje'): ?>
				<strong class="lightGreen"><?php echo Arr::get($cmslabel, 'free'); ?></strong>
			<?php endif; ?>
		</span>
		<div class="shipping-data shipping_info shipping_info_<?php echo $shipping['id']; ?>" <?php if (!$shipping_selected): ?>style="display: none"<?php endif; ?>>
			<?php if ($shipping['show_shipping_address']): ?>
				<?php if ($shipping['description']): ?>
					<?php echo $shipping['description']; ?><br/><br/>
				<?php endif; ?>
				<?php echo $shipping_address; ?>
			<?php else: ?>
				<?php echo $shipping['description']; ?>
			<?php endif; ?>
			<?php if (isset($shipping['widget_content']) AND $shipping['widget_content']): ?>
				<?php echo $shipping['widget_content']; ?>
				<div class="shipping-location-info" data-id="2500"><?php echo Arr::get($cmslabel, 'info_osebni_prevzem_2500'); ?></div>
			<?php endif; ?>
		</div>
        <?php /*if ($shipping['code'] == 'hitra_dostava' AND !empty($shopping_cart['available_shippings']) AND !in_array($shipping['id'], $shopping_cart['available_shippings'])): ?>
            <?php
            $products_badges_raw = DB::select_array(['id', 'badges_ids'])
                ->from('catalog_products')
                ->where('id', 'IN', array_column($shopping_cart_products, 'id'))
                ->execute()
                ->as_array('id', 'badges_ids');

            if (!empty($products_badges_raw)) {
                $products_not_delivery = [];
                $shipping_restricted_badge = Text::db_to_array($shipping['badges_ids']);
                foreach($products_badges_raw AS $product_id => $product_badges) {
                    $product_shipping_unavailable = true;
                    if (!empty($product_badges)) {
                        $product_badges = array_filter(explode(',', $product_badges));
                        foreach ($shipping_restricted_badge as $badge) {
                            if (in_array($badge, $product_badges)) {
                                $product_shipping_unavailable = false;
                            }
                        }
                    }

                    if ($product_shipping_unavailable) {
                        array_push($products_not_delivery, $product_id);
                    }
                }

                if (!empty($products_not_delivery) AND count($products_not_delivery) < count($shopping_cart_products)) {
                    echo View::factory('webshop/widget/remove_products', [
                        'code' => $shipping['code'],
                        'products_ids' => $products_not_delivery,
                    ]);
                }
            }
            ?>
        <?php endif;*/ ?>
	</div>
<?php endforeach; ?>