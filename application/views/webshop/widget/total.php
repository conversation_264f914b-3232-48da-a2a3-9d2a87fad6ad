<?php $mode = (isset($mode)) ? $mode : 'total'; ?>
<?php $mode_class = ($mode == 'preview') ? 'wwp-totals' : 'cart-totals'; ?>
<div class="clear <?php echo $mode_class; ?>">
	<?php if($mode == 'preview'): ?>
		<div class="ww-total cart-total">
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay_cart', 'Skupaj'); ?>:</span>
			<span class="w-totals-value value cart_info_total_items_total"><?php echo Utils::currency_format($shopping_cart_info['total_items_total'] * $currency['exchange'], $currency['display']); ?></span>
		</div>
		<span class="cart-shipping-tooltip"><?php echo Arr::get($cmslabel, 'total_without_shipping', '* bez troška dostave'); ?></span>
		<?php if (!empty($user->loyalty_code)): ?>
			<span class="cart-shipping-tooltip special"><?php echo str_replace('%LOYALTY_CODE%', $user->loyalty_code, Arr::get($cmslabel, 'total_with_loyalty', '')); ?></span>
		<?php endif; ?>
	<?php else: ?>
		<div class="cart-total-shipping">
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'shipping', 'Dostava'); ?>:</span>
			<span class="w-totals-value cart_info_total_extra_shipping"><?php if ($shopping_cart['total_extra_shipping'] == 0): ?><span class="w-totals-label-free"><?php echo Arr::get($cmslabel, 'free_shipping', 'Besplatna'); ?></span><?php else: ?><?php echo Utils::currency_format($shopping_cart['total_extra_shipping'] * $currency['exchange'], $currency['display']); ?><?php endif; ?></span>
		</div>
        <div class="cart-total-payment cart_info_total_extra_payment_box" <?php if (empty($shopping_cart_info['total_extra_payment'])): ?>style="display: none"<?php endif; ?>>
            <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'payment', 'Doplata za rate'); ?>:</span>
            <span class="w-totals-value cart_info_total_extra_payment"><?php if (empty($shopping_cart['total_extra_payment']) OR $shopping_cart['total_extra_payment'] == 0): ?><span class="w-totals-label-free">0</span><?php else: ?><?php echo Utils::currency_format(Arr::get($shopping_cart, 'total_extra_payment') * $currency['exchange'], $currency['display']); ?><?php endif; ?></span>
        </div>
		<div class="ww-total cart-total">
			<span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay', 'Skupaj'); ?>:</span>
			<span class="w-totals-value value cart_info_total"><?php echo Utils::currency_format($shopping_cart_info['total'] * $currency['exchange'], $currency['display']); ?></span>
		</div>
		<?php if (!empty($user->loyalty_code)): ?>
			<span class="cart-shipping-tooltip">
				<?php echo str_replace('%LOYALTY_CODE%', $user->loyalty_code, Arr::get($cmslabel, 'total_with_loyalty', '')); ?>
                <?php if (!empty($accept_terms_field)): ?>
                    <?php echo Arr::get($cmslabel, 'accept_terms_webshop_text', ''); ?>
                <?php endif; ?>
			</span>
        <?php else: ?>
            <?php if (!empty($accept_terms_field)): ?>
                <span class="cart-shipping-tooltip">
					<br/><?php echo Arr::get($cmslabel, 'accept_terms_webshop_text', ''); ?>
				</span>
            <?php endif; ?>
        <?php endif; ?>
	<?php endif; ?>
</div>