<script type="text/javascript">
    var dataLayer = window.dataLayer || [];
    dataLayer.push({
        <?php if (!empty($event)): ?>'event': '<?php echo $event; ?>',<?php endif; ?>
        'ecommerce': {
            <?php if (!empty($currency_info['exchange']) AND Google::valid_currency($currency_info['code'])): ?>
            'currencyCode': '<?php echo strtoupper($currency_info['code']); ?>',
            <?php elseif (Google::valid_currency($order->price_currency->code)): ?>
            'currencyCode': '<?php echo strtoupper($order->price_currency->code); ?>',
            <?php endif; ?>
            'purchase': {
                'actionField': {
                    'id': '<?php echo $order->id; ?>',
                    'affiliation': 'Web Shop',
                    'revenue': '<?php echo number_format((!empty($currency_info['exchange'])) ? ($order->total - $order->total_tax) * $currency_info['exchange'] : ($order->total - $order->total_tax), 2, '.', ''); ?>',
                    'tax': '<?php echo number_format((!empty($currency_info['exchange'])) ? $order->total_tax * $currency_info['exchange'] : $order->total_tax, 2, '.', ''); ?>',
                    'shipping': '<?php echo number_format((!empty($currency_info['exchange'])) ? $order->get_shipping_price(false) * $currency_info['exchange'] : $order->get_shipping_price(false), 2, '.', ''); ?>',
                    'coupon': '<?php echo number_format((!empty($currency_info['exchange'])) ? $order->get_coupon_price(false) * $currency_info['exchange'] : $order->get_coupon_price(false), 2, '.', ''); ?>' //
                },
                <?php $order_items_total = count($order->items); ?>
                'products': [
                    <?php foreach ($order->items as $item): ?>
                    {
                        'name': '<?php echo str_replace("'", '"', $item->title); ?>',
                        'id': '<?php echo str_replace("'", '"', $item->code); ?>',
                        'price': '<?php echo number_format((!empty($currency_info['exchange'])) ? ($item->price / (1 + $item->tax)) * $currency_info['exchange'] : ($item->price / (1 + $item->tax)), 2, '.', '');?>',
                        'brand': '<?php echo str_replace("'", '"', Text::replace_newline($item->manufacturer_title)); ?>',
                        'category': '<?php echo str_replace("'", '"', Text::replace_newline($item->category_title)); ?>',
                        'quantity': '<?php echo number_format($item->qty, 2, '.', ''); ?>',
                        'coupon': '<?php if (!empty($order->coupon_notes)): ?><?php echo Text::meta($order->coupon_notes); ?><?php endif; ?>'

                    }<?php $order_items_total--; ?><?php if ($order_items_total): ?>,<?php endif; ?>

                    <?php endforeach; ?>
                ]
            }
        }
    });
</script>