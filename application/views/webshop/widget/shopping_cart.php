<div class="ww cart<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1" data-shoppingcart_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>">
	<a class="btn-header ww-items" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>">
		<span class="btn-header-text"><?php echo Arr::get($cmslabel, 'header_btn_cart'); ?></span>
		<span class="ww-counter btn-header-counter">
			<span id="cartItemTotal" class="value total-items cart_info_item_count total_items"><?php echo $shopping_cart['item_count']; ?></span>
		</span>
	</a>

	<?php if ($info['user_device'] != 'm' AND $info['user_device'] != 't'): ?>
		<!-- Cart preview -->
		<div class="ww-preview shopping_cart_preview">
			<?php echo View::factory('webshop/widget/shopping_cart_preview', [
					'shopping_cart' => $shopping_cart,
					'shopping_cart_products' => $shopping_cart_products,
					'shopping_cart_status' => $shopping_cart_status,
					'shopping_cart_hapi' => $shopping_cart_hapi ?? null,
				]);
			?>
		</div>
	<?php endif; ?>
</div>