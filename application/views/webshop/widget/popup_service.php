<div data-shoppingcart_product_service="1">
    <?php if (!empty($item_services)): ?>
        <div class="modal-services">
            <?php $last_service_group_title = ''; ?>
            <?php $last_service_group_code = ''; ?>
            <?php $last_service_group_type = ''; ?>
            <?php $t = 0; ?>
            <?php foreach ($item_services AS $item_service_item_id => $item_service_item_data): ?>
                <?php if ($last_service_group_type != $item_service_item_data['group_type']): ?>
                    <?php if ($last_service_group_type == 's'): ?>
                        <div class="modal-service-item" data-service_extra_modal_none="<?php echo $product_shopping_code ?>_<?php echo $last_service_group_code; ?>" style="display: none;">
                            <div class="modal-service-row">
                                <input type="radio" name="services[]_modal" value="" data-service_code="0" id="service-modal-0" data-service_extra="<?php echo $product_shopping_code; ?>"  data-service_extra_category="<?php echo $last_service_group_code; ?>" data-service_added="<?php echo implode(',', $product_added_services); ?>" />
                                <label class="modal-service" for="service-modal-0">
                                    <div class="modal-service-title" data-service_extra_title="<?php echo $product_shopping_code; ?>">
                                        <?php echo Arr::get($cmslabel, 'no_warranty'); ?>
                                    </div>
                                </label>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php $last_service_group_code = $item_service_item_data['group_code']; ?>
                    <?php $last_service_group_type = $item_service_item_data['group_type']; ?>
                <?php endif; ?>

                <?php
                $change_title = false;
                if ($last_service_group_title != $item_service_item_data['group_title']) {
                    $last_service_group_title = $item_service_item_data['group_title'];
                    $change_title = true;
                }
                ?>
                <?php if ($change_title): ?>
                    <div class="modal-subtitle modal-service-subtitle<?php if ($t > 0): ?> special<?php endif; ?>"><?php echo $last_service_group_title; ?></div>
                <?php endif; ?>

                <div class="modal-service-item" data-service_extra_modal_form="<?php echo $item_service_item_data['extra_modal_form']; ?>">
                    <?php if ($item_service_item_data['group_type'] == 's'): ?>
                        <input type="radio" name="services[]_modal" value="<?php echo $item_service_item_id; ?>" data-service_code="<?php echo $item_service_item_id; ?>" id="service-modal-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_shopping_code; ?>" data-service_extra_category="<?php echo $item_service_item_data['group_code']; ?>" data-service_added="<?php echo implode(',', $product_added_services); ?>" />
                    <?php elseif ($item_service_item_data['group_type'] == 'c'): ?>
                        <input type="checkbox" name="services[]_modal" value="<?php echo $item_service_item_id; ?>" data-service_code="<?php echo $item_service_item_id; ?>" id="service-modal-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_shopping_code; ?>" data-service_extra_category="<?php echo $item_service_item_data['group_code']; ?>" data-service_added="<?php echo implode(',', $product_added_services); ?>" />
                    <?php endif; ?>
                    <label class="modal-service" for="service-modal-<?php echo $item_service_item_id; ?>">
                        <div class="modal-service-title">
                            <span data-service_extra_title="<?php echo $item_service_item_id; ?>"><?php echo $item_service_item_data['title']; ?></span>
                            <span class="modal-service-price"><span data-service_extra_price="<?php echo $item_service_item_id; ?>"><?php echo Utils::currency_format($item_service_item_data['price'] * $currency['exchange'], $currency['display']); ?></span></span>
                            <?php if($item_service_item_data['description']): ?>
                                <span class="modal-service-btn"></span>
                            <?php endif; ?>
                        </div>
                        <?php if($item_service_item_data['description']): ?>
                            <div class="modal-service-desc"><?php echo $item_service_item_data['description']; ?></div>
                        <?php endif; ?>
                    </label>
                </div>
                <?php $t++; ?>
            <?php endforeach; ?>

            <?php if ($last_service_group_type == 's'): ?>
                <div class="modal-service-item" data-service_extra_modal_none="<?php echo $product_shopping_code ?>_<?php echo $last_service_group_code; ?>" style="display: none;">
                    <div class="modal-service-row">
                        <input type="radio" name="services[]_modal" value="" data-service_code="0" id="service-modal-0" data-service_extra="<?php echo $product_shopping_code; ?>"  data-service_extra_category="<?php echo $last_service_group_code; ?>" data-service_added="<?php echo implode(',', $product_added_services); ?>" />
                        <label class="modal-service" for="service-modal-0">
                            <div class="modal-service-title" data-service_extra_title="<?php echo $product_shopping_code; ?>">
                                <?php echo Arr::get($cmslabel, 'no_warranty'); ?>
                            </div>
                        </label>
                    </div>
                </div>
            <?php endif; ?>

        </div>
    <?php endif; ?>

    <!-- Bought together -->
    <?php $list_recommendation = Widget_Catalog::products(['lang' => $lang, 'related_code' => 'bought_together', 'related_item_id' => $item['id'], 'related_item_add' => $item, 'limit' => 8, 'only_available' => TRUE]); ?>
    <?php $recommendation_total_selected = $item['price_custom']; ?>
    <?php $recommendation_products_selected = 1; ?>
    <?php $recommendation_products_total = 1; ?>
    <?php $total_items = sizeof($list_recommendation); ?>

    <?php if(!empty($list_recommendation)): ?>
        <?php $recommendation_total = $list_recommendation['_basic'];
        unset($list_recommendation['_basic']);
        $r = 1;
        ?>
        <div class="bought-together" id="bought-together">
            <div class="bought-related-title modal-subtitle"><?php echo Arr::get($cmslabel, 'bought_together'); ?></div>
            <div id="product_special_list_recommendation" class="bought-together-box" data-count-items="<?php echo count($list_recommendation); ?>">
                <div class="bought-together-images">
                    <?php foreach ($list_recommendation as $related_item): ?>
                        <?php if(!empty($related_item['main_image'])): ?>
                            <div class="bought-together-image">
                                <img loading="lazy" <?php echo Thumb::generate($related_item['main_image'], ['width' => 100, 'height' => 100, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-100.webp', 'srcset' => '200c 2x']); ?> alt="<?php echo $related_item['title']; ?>" title="<?php echo Text::meta($related_item['main_image_title']); ?>" />
                                <span class="bt-plus"></span>
                            </div>
                        <?php else: ?>
                            <img src="/media/images/no-image-100.jpg" width="100" height="100" alt="<?php echo $related_item['title']; ?>" title="<?php echo Text::meta($related_item['main_image_title']); ?>">
                            <span class="bt-plus"></span>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
                <div class="bought-together-items">
                    <?php foreach ($list_recommendation as $related_item): ?>
                        <?php $recommendation_total_selected += $related_item['price_custom']; ?>
                        <?php $recommendation_products_selected += 1; ?>
                        <?php $recommendation_products_total += 1; ?>

                        <div id="product_special_list_recommendation-descriptions-<?php echo $related_item['shopping_cart_code']; ?>" class="bt-item<?php if ($r == 1): ?> bt-main<?php endif; ?>">
                            <div class="bt-item-checkbox">
                                <?php if ($r != 1): ?>
                                    <input type="hidden" name="price[<?php echo $related_item['shopping_cart_code']; ?>]" value="<?php echo $related_item['price_custom']; ?>" />
                                    <input type="checkbox" name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>" value="<?php echo $related_item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" checked="checked" />
                                    <label for="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
                                <?php else: ?>
                                    <span class="bt-item-main-checkbox-icon"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></span>
                                <?php endif; ?>
                            </div>

                            <div class="bt-item-cnt">
                                <h2 class="bt-item-title" data-product_title="<?php echo $related_item['shopping_cart_code']; ?>">
                                    <?php if ($item['id'] == $related_item['id']): ?>
                                        <span><?php echo Arr::get($cmslabel, 'bought_together_item_main'); ?></span>
                                    <?php endif; ?>
                                    <a href="<?php echo $related_item['url']; ?>" target="_blank"><?php echo $related_item['title']; ?></a>
                                </h2>
                                <div class="bought-together-message bt-item-message product_message product_message_<?php echo $related_item['shopping_cart_code']; ?>" style="display: none"></div>
                            </div>

                            <div class="bt-item-price">
                                <?php if ($related_item['discount_percent_custom'] > 0): ?>
                                    <span class="bt-item-discount-price bt-item-current-price red" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                                    <span class="bt-item-old-price bt-item-old-price-small line-through"><?php echo Utils::currency_format($related_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                                <?php else: ?>
                                    <span class="bt-item-current-price" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php $r++; ?>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="bt-choosen-info-cnt">
                <div class="bt-choosen-info">
                    <div class="bt-choosen-info-total">
                        <span class="product_special_list_recommendation_products"><?php echo $recommendation_total['item_selected']; ?></span>
                        <span><?php echo Arr::get($cmslabel, 'choosen'); ?></span>
                    </div>

                    <input type="hidden" name="product_special_list_recommendation_item_price_basic" value="<?php echo $recommendation_total['item_price_basic']; ?>" />
                    <input type="hidden" name="product_special_list_recommendation_item_price_all" value="<?php echo $recommendation_total['item_price_all']; ?>" />
                    <input type="hidden" name="product_special_list_recommendation_related_discount_percent_general" value="<?php echo $recommendation_total['related_discount_percent']; ?>" />

                    <?php if ($recommendation_total['item_price_saving'] > 0): ?>
                        <div class="product_special_list_recommendation_discount_box active">
                            <div class="choosen-saving-old-price"><span class="label-total"><?php echo Arr::get($cmslabel, 'total_to_pay'); ?>:</span> <del class="product_special_list_recommendation_basic total"><?php echo Utils::currency_format($recommendation_total['item_price_basic'] * $currency['exchange'], $currency['display']); ?></del></div>
                            <div class="choosen-saving-new-price"><span class="label-total"><?php echo Arr::get($cmslabel, 'total_to_pay_discount'); ?>:</span> <strong class="product_special_list_recommendation_total total red"><?php echo Utils::currency_format($recommendation_total['item_price_all'] * $currency['exchange'], $currency['display']); ?></strong></div>
                            <div class="choosen-saving"><span class="label-total-saving"><?php echo Arr::get($cmslabel, 'saving'); ?></span> <span class="product_special_list_recommendation_item_price_saving saving-label"><?php echo Utils::currency_format($recommendation_total['item_price_saving'] * $currency['exchange'], $currency['display']); ?></span></div>
                        </div>
                        <div class="product_special_list_recommendation_nonediscount_box">
                            <div class="total-choosen-cnt">
                                <strong class="product_special_list_recommendation_total total bt-price-total"><?php echo Utils::currency_format($recommendation_total['item_price_all'] * $currency['exchange'], $currency['display']); ?></strong>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="total-choosen-cnt">
                            <strong class="product_special_list_recommendation_total total bt-price-total"><?php echo Utils::currency_format($recommendation_total['item_price_all'] * $currency['exchange'], $currency['display']); ?></strong>
                        </div>
                    <?php endif; ?>

                    <div class="bt-list-message product_message_list"></div>
                </div>
                <a href="javascript:cmswebshop.shopping_cart.add_special_list('recommendation:<?php echo $item['shopping_cart_code']; ?>|<?php echo $recommendation_total['list_id']; ?>')" class="btn btn-bt-add-to-cart">
                    <span>
                        <?php if($info['user_device'] == 'm'): ?>
                            <?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?>
                        <?php else: ?>
                            <?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart'); ?>
                        <?php endif; ?>
                    </span>
                </a>
            </div>
        </div>
    <?php endif; ?>

    <?php /*$related_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'related_widget_data' => Arr::get($item, 'related_widget_data'), 'only_available' => true, 'limit' => 10, 'always_to_limit' => TRUE)); ?>
    <?php if(empty($list_recommendation) AND !empty($related_items)): ?>
        <div class="modal-related">
            <div class="related-title modal-subtitle"><?php echo Arr::get($cmslabel, 'related_products'); ?></div>
            <!-- FIXME PROG ne ispisuju se rating ikone zvijezde -->
            <div class="modal-related-items">
                <?php echo View::factory('catalog/index_entry', array('items' => $related_items, 'class' => 'PDP-modal', 'mode' => 'related')); ?>
            </div>
        </div>
    <?php endif;*/ ?>
</div>

<script>
    //modal services
    $(".modal-service-btn").on('click', function (e) {
        e.preventDefault();
        $(this).parent().parent().parent().toggleClass("active-desc"); // select this element
    });

    $('.modal-service-desc a').click(function () {
        window.open(this.href);
        return false;
    });

    //item qty
    $('.product_qty_input').change(function(){
        if (this.value === 'toggle') {
            $(this).parent().parent().addClass('active');
            var selectId = $(this).attr('name');
            $('input[name="'+ selectId + '"]').focus();
            setTimeout(function() {
                $('.cp-qty-note[name="'+ selectId + '"]').addClass('hidden');
            }, 5000);
        }
    });
</script>