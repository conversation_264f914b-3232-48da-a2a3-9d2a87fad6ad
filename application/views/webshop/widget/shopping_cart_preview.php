<div class="shoppingcart_items_small clear">
    <?php 
    $parcels = [];
    if (!empty($shopping_cart_hapi['parcels'])) {
        foreach ($shopping_cart_hapi['parcels'] AS $parcel_i => $parcel) {
            if (!isset($parcels[$parcel_i])) {
                $parcels[$parcel_i] = [
                    'seller_title' => $parcel['parcel_name'],
                    'seller_corporate_name' => $parcel['parcel_corporate_name'] ?? '',
                    'items' => [],
                ];
            }

            foreach ($parcel['items'] AS $parcel_item) {
                $parcels[$parcel_i]['items'][] = $parcel_item['shopping_cart_code'];
            }
        }
    } else {
        $parcels[0] = [
            'seller_title' => 'Big Bang',
            'seller_corporate_name' => '',
            'items' => [],
        ];
        foreach ($shopping_cart_products as $product) {
            $parcels[0]['items'][] = $product['shopping_cart_code'];

        }
    }

    ?>

    <!-- Cart items -->
    <div class="ww-preview-items shopping_cart_preview_items">
        <table class="ww-preview-table">
            <?php foreach($parcels as $parcel): ?>
                <div class="wwp-seller">
                    <span class="title">
                        <?php echo Arr::get($cmslabel, 'seller_item_title'); ?> 
                        <?php if(!empty($parcel['seller_corporate_name'])): ?>
                            <span><strong><?php echo $parcel['seller_corporate_name']; ?></strong></span>
                        <?php endif; ?>
                        <?php if(!empty($parcel['seller_title'])): ?>
                            <span class="extra-name">(<?php echo $parcel['seller_title']; ?>)</span>
                        <?php endif; ?>
                    </span>
                </div>

                <?php foreach ($parcel['items'] as $product_code): ?>
                    <?php $product_data = $shopping_cart_products[$product_code]; ?>
                    <?php $product_status = $shopping_cart_status[$product_code]; ?>
                    <div class="wwp" id="product-<?php echo $product_code; ?>">
                        <div class="wwp-image">
                            <a href="<?php echo $product_data['url']; ?>">
                                <img loading="lazy" <?php echo Thumb::generate($product_data['main_image'], array('width' => 70, 'height' => 70, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-50.webp', 'srcset' => '140c 2x')); ?> alt="<?php echo $product_data['title']; ?>" />
                            </a>
                        </div>
                        <div class="wwp-cnt">
                            <div class="wwp-cnt-col1">
                                <div class="wwp-title"><a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>                            </div>
                                <?php if (!empty($product_status['services'])): ?>
                                    <div class="wpp-services">
                                        <?php foreach ($product_status['services'] AS $item_service): ?>
                                            <?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
                                                <?php if (empty($item_service_item['selected'])) {continue;} ?>
                                                <div class="wpp-service">
                                                    + <?php echo $item_service_item['title']; ?> (<strong><span class="product_qty" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> x <?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?>)</strong>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="wwp-cnt-col2">
                                <a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove', 6);" class="wwp-remove"></a>
                                <input class="wp-input-qty product_qty_input" type="hidden" name="qty_webshop_preview[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>">
                                <div class="wwp-price">
                                    <span class="product_qty"><?php echo $product_status['qty']; ?> x </span><span class="value"><?php echo Utils::currency_format($product_status['price_without_service'] * $currency['exchange'], $currency['display']); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </table>
    </div>

    <div class="wwp-footer">
        <!-- Totals -->
        <?php echo View::factory('webshop/widget/total', ['shopping_cart_info' => $shopping_cart, 'mode' => 'preview']); ?>

        <!-- Min order alert -->
        <?php if (isset($shopping_cart['total_extra_shipping_min_total_error']) AND $shopping_cart['total_extra_shipping_min_total_error']): ?>
            <div class="wwp-minprice minprice-tooltip" style="display:none;">
                <?php
                echo str_replace(array(
                    '%TOTAL_MIN%',
                    '%TOTAL_MISSING%',
                        ), array(
                    Utils::currency_format($shopping_cart['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']),
                    Utils::currency_format($shopping_cart['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']),
                        ), Arr::get($cmslabel, 'minimal_order_price_full'));

                ?>
            </div>
        <?php endif; ?>

        <a class="btn btn-green wwp-btn-view btn-icon-arrow" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'view_shopping_cart'); ?></span></a>

    <div class="ww-preview-missing-container cart_info_total_extra_shipping_to_free_box"<?php if ($shopping_cart['total_extra_shipping_to_free'] == 0): ?> style="display: none"<?php endif; ?>>

        <div class="free-delivery-missing cart_info_total_extra_shipping_to_free_box" <?php if ($shopping_cart['total_extra_shipping_to_free'] == 0): ?> style="display: none"<?php endif; ?>>
            <span class="free-delivery-missing-bg cart_info_total_extra_shipping_to_free_percent" style="width: <?php echo Arr::get($shopping_cart, 'total_extra_shipping_to_free_percent'); ?>;"></span>
            <span class="free-delivery-missing-num">Samo&nbsp;<strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free"><?php echo Utils::currency_format($shopping_cart['total_extra_shipping_to_free'] * $currency['exchange'], $currency['display']); ?></strong>&nbsp;<?php echo Arr::get($cmslabel, 'min_total_missing'); ?></span>
        </div>

    </div>
    </div>
</div>