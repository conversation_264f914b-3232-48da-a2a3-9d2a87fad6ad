<div class="ww-coupons ww-auth-coupons">
	<!-- Add Coupon Form -->
	<div class="ww-coupons-form ww-auth-coupons-form">
		<label class="ww-coupons-label" for="coupon_code"><?php echo Arr::get($cmslabel, 'coupon_have_coupon'); ?></label>
		<div class="ww-coupons-add ww-auth-coupons-add">
			<input class="ww-coupons-input" type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
			<a class="btn btn-blue ww-btn-add" href="javascript:cmscoupon.save_for_later('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
			<div class="coupon_message" style="display: none"></div>
		</div>
	</div>

	<!-- List of available coupons -->
	<?php if (sizeof($items)): ?>
		<div class="clear ww-auth-coupons-list">
			<div class="orders-head coupons-head">
				<div class="table-col-coupon col-coupon-num"><?php echo Arr::get($cmslabel, 'coupon_code'); ?></div>
				<div class="table-col-coupon col-coupon-desc"><?php echo Arr::get($cmslabel, 'coupon_desc'); ?></div>
				<div class="table-col-coupon col-coupon-total"><?php echo Arr::get($cmslabel, 'coupons_total'); ?></div>
				<div class="table-col-coupon col-coupon-date"><?php echo Arr::get($cmslabel, 'coupons_date'); ?></div>
			</div>
			<div class="ww-auth-coupons-table" id="webshop_coupon_list" <?php if (!count($items)): ?>style="display: none"<?php endif; ?>>
				<?php foreach ($items as $item): ?>
					<?php echo View::factory('webshop/widgetlist/coupon_entry', ['item' => $item]); ?>
				<?php endforeach; ?>
			</div>
		</div>
	<?php endif; ?>
</div>