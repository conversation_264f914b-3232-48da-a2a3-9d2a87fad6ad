<div class="ww-coupons-table-row">
	<div class="table-col-coupon col-code">Kupon-<?php echo $item['code']; ?></div>
	<div class="table-col-coupon col-description"><?php echo ($item['description']) ? $item['description'] : '-'; ?></div>
	<div class="table-col-coupon col-value"><strong><?php echo ($item['type'] == 'f') ? Utils::currency_format($item['coupon_price'],$currency['display']) : ($item['coupon_percent']*100).'%'; ?></strong></div>
	<?php if ($item['active']): ?>
		<div class="table-col-coupon col-valid">
			<?php if ($item['datetime_expire']): ?>
				<span class="label"><?php echo Arr::get($cmslabel, 'coupon_valid_until'); ?></span>
				<?php echo date('d.m.Y, H:i', $item['datetime_expire']); ?>
			<?php else: ?>
				-
			<?php endif; ?>
		</div>
	<?php else: ?>
		<div class="table-col-coupon col-valid">
			<?php if ($item['used']): ?>
				<?php echo Arr::get($cmslabel, 'coupon_used'); ?>
			<?php else: ?>
				<?php if ($item['datetime_expire'] AND $item['datetime_expire'] < time()): ?>istekao <?php echo date('d.m.Y', $item['datetime_expire']); ?><?php else: ?>-<?php endif; ?>
			<?php endif; ?>
		</div>
	<?php endif; ?>
</div>
