<?php $dynamicprice_form = Widget_Cms::form($info['lang'], 'code:dynamicprice_form', $info); ?>
<?php if (!empty($dynamicprice_form['fields'])): ?>
	<div id="dynamicprice_form_layout" class="cd-dynamicprice">
		<div class="cd-dynamicprice-desc"><?php echo Arr::get($cmslabel, 'dynamicprice_desc_lowest_price'); ?></div>
		<?php if (!empty($product_shopping_cart_code) AND isset($product_status)): ?>
			<div class="cd-btn-container-dynamicprice">
				<a class="btn btn-green cd-btn-add-dynamicprice" href="javascript:cmswebshop.shopping_cart.add('<?php echo $product_shopping_cart_code; ?>', '_tracking:detail', 'simple_loader', 'input', 3)">
					<?php if ($product_status == '5'): ?>
						<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
					<?php else: ?>
						<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart'); ?></span>
					<?php endif; ?>
				</a>
			</div>
		<?php endif; ?>

		<?php /* ?>
		<div class="cd-dynamicprice-desc"><?php echo Arr::get($cmslabel, 'dynamicprice_desc'); ?></div>
		<form action="<?php echo $dynamicprice_form['form_url']; ?>#dynamicprice_form" method="post" name="dynamicprice_form" id="dynamicprice_form" class="ajax_siteform form-animated-label" data-siteform_response="show_hide">
			<?php foreach ($dynamicprice_form['fields'] as $field => $field_element): ?>
				<?php if($field == 'product_title'): ?>
					<input type="hidden" name="product_title" value="<?php if(!empty($product_title)) echo $product_title; ?>">
				<?php elseif($field == 'product_url'): ?>
					<input type="hidden" name="product_url" value="<?php if(!empty($product_url)) echo $product_url; ?>">
				<?php elseif($field == 'accept_terms'): ?>
					<p class="field field-<?php echo $field; ?>">
						<input type="checkbox" name="<?php echo $field; ?>" id="field-<?php echo $field; ?>" value="1">
						<label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_dynamicprice_'.$field, Arr::get($cmslabel, $field)); ?><?php if (!empty($dynamicprice_form['requested']) AND !in_array($field, $dynamicprice_form['requested'])): ?><span class="not-mandatory"><?php echo Arr::get($cmslabel, 'not_mandatory'); ?></span><?php endif; ?></label>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</p>
				<?php else: ?>
					<?php $error = (!empty($dynamicprice_form['errors'][$field][0])) ? $dynamicprice_form['errors'][$field][0] : ''; ?>
					<p class="field field-<?php echo $field; ?>">
						<label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_dynamicprice_'.$field, Arr::get($cmslabel, $field)); ?><?php if (!empty($dynamicprice_form['requested']) AND !in_array($field, $dynamicprice_form['requested'])): ?><span class="not-mandatory"><?php echo Arr::get($cmslabel, 'not_mandatory'); ?></span><?php endif; ?></label>
						<?php echo $field_element; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</p>
				<?php endif; ?>
			<?php endforeach; ?>
			<button type="submit" class="btn btn-cd-dynamicprice"><span><?php echo Arr::get($cmslabel, 'dynamicprice_send', 'Pošalji'); ?></span></button>
		</form>
		<div id="dynamicprice_form_success" class="cd-dynamicprice-success" style="display:none;">
			<?php echo Arr::get($cmslabel, 'dynamicprice_success', 'Uspješno slanje'); ?>
		</div>
		<?php */ ?>
	</div>
<?php endif; ?>