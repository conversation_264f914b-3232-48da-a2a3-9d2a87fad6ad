<?php $notifymelast = Widget_Cms::form($info['lang'], 'code:notifymelast_form', $info); ?>
<?php if ($notifymelast): ?>
    <div class="cd-unavailable">
        <div id="contact_form_layout" class="cd-inquiry">
            <div class="cd-unavailable-title"><?php echo Arr::get($cmslabel, 'form_available_last'); ?></div>
            <div class="cd-unavailable-note"><?php echo Arr::get($cmslabel, 'form_available_last_note'); ?></div>
            <form action="<?php echo $notifymelast['form_url']; ?>#notifymelast_form" method="post" name="notifymelast_form" id="notifymelast_form" class="ajax_siteform form-animated-label" data-siteform_response="show_hide">
                <?php foreach (@$notifymelast['fields'] as $field => $field_element): ?>
                    <?php if($field == 'product'): ?>
                        <input type="hidden" name="product" value="<?php if(isset($product_id) AND $product_id) echo $product_id; ?>">
                    <?php else: ?>
                        <?php $error = @$notifymelast['errors'][$field][0]; ?>
                        <p class="field field-<?php echo $field; ?>">
                            <label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_inquiry_'.$field, Arr::get($cmslabel, $field)); ?><?php if (!in_array($field, @$notifymelast['requested'])): ?><span class="not-mandatory"><?php echo Arr::get($cmslabel, 'not_mandatory'); ?></span><?php endif; ?></label>
                            <?php echo $field_element; ?>
                            <span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
                        </p>
                    <?php endif; ?>
                <?php endforeach; ?>
                <button type="submit" class="btn btn-medium btn-cd-inquiry"><span><?php echo Arr::get($cmslabel, 'inquiry_send', 'Pošalji'); ?></span></button>
            </form>
            <div id="notifymelast_form_success" class="cd-inquiry-success global-success" style="display:none;">
                <?php echo Arr::get($cmslabel, 'inquiry_success', 'Uspješno slanje'); ?>
            </div>
        </div>
    </div>
<?php endif; ?>