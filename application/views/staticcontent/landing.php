<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> shortnl page-landing<?php if (!empty($cms_page['menu']) AND $info['user_device'] == 'm'): ?> page-landing-menu<?php elseif(!empty($cms_page['menu'])): ?> page-landing-menu<?php endif; ?><?php $this->endblock('page_class'); ?>
<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>

<?php if(!empty($cms_page['color_2'])): ?>
	<?php $this->block('extrahead'); ?>
		<style>
			.signup-note a, .signup-gdpr-btn-more{color: <?php echo($cms_page['color_2']); ?>;}
			.signup-note a:hover{text-decoration: none;}
		</style>
	<?php $this->endblock('extrahead'); ?>
<?php endif; ?>

<?php if (!empty($cms_page['menu'])): ?>
	<?php $this->block('header_landing'); ?>
		<div class="ln-nav-container">
			<div class="wrapper">
				<ul class="ln-nav scroll">
					<?php $i = 0; ?>
					<?php foreach ($cms_page['menu'] AS $menu_id => $menu_title): ?>
						<li><a class="<?php if($i == 0): ?>active<?php endif; ?>" href="#<?php echo $menu_id; ?>"><span><?php echo $menu_title; ?></span></a></li>
						<?php $i++; ?>
					<?php endforeach; ?>
				</ul>
			</div>
		</div>
	<?php $this->endblock('header_landing'); ?>
<?php endif; ?>

<?php $this->block('main'); ?>
	<div class="landing-main" <?php if(!empty($cms_page['color_1']) OR !empty($cms_page['color_2'])): ?> style="<?php if(!empty($cms_page['color_1'])): ?>background-color: <?php echo($cms_page['color_1']); ?>;<?php endif; ?><?php if(!empty($cms_page['color_2'])): ?> color: <?php echo($cms_page['color_2']); ?>;<?php endif; ?>"<?php endif; ?>>
		<div class="landing-wrapper">
			<?php echo Arr::get($cms_page, 'content'); ?>
		</div>
	</div>
<?php $this->endblock('main'); ?>