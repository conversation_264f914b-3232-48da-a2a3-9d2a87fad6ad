<?php $item = reset($items); ?>
<?php if($item['image']): ?>
	<?php $item_title = str_replace('"', "'", Text::meta($item['title'])); ?>
	<?php $item_title_promo = str_replace('"', "'", Text::meta(!empty($page_title) ? $page_title : $item['title'])); ?>
	<section class="ln-section ln-section-promo ln-section-promo-big" id="position<?php echo $position; ?>"<?php if($item['menu_visible'] == 1): ?> data-anchor="position<?php echo $position; ?>"<?php endif; ?>>
		<?php if(!empty(Arr::get($item, 'url'))): ?><a href="<?php echo Arr::get($item, 'url'); ?>" class="ln-promo ln-promo-link ln-promo-big" data-tracking_gtm_promo_click="<?php echo $item['id']; ?>|<?php echo $item_title; ?>|<?php echo $item['image']; ?>|landing - <?php echo $item_title_promo; ?> - big"><?php else: ?><div class="ln-promo ln-promo-big"><?php endif; ?>
			<picture data-tracking_gtm_promo_view="<?php echo $item['id']; ?>|<?php echo $item_title; ?>|<?php echo $item['image']; ?>|landing - <?php echo $item_title_promo; ?> - big">
				<?php if(!empty($item['image2'])): ?>
					<source srcset="<?php echo Thumb::generate($item['image2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
				<?php endif; ?>
				<img <?php echo Thumb::generate((!empty($item['image']) ? $item['image'] : Arr::get($item, 'main_image')), array('width' => 1480, 'default_image' => '/media/images/no-image-1480.webp', 'html_tag' => TRUE, 'srcset' => '2960c 2x')); ?> alt="<?php echo Arr::get($item, 'title'); ?>" />
			</picture>
		<?php if(!empty(Arr::get($item, 'url'))): ?></a><?php else: ?></div><?php endif; ?>
	</section>
<?php endif; ?>