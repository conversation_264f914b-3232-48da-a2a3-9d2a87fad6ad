<section class="ln-section ln-section-publish" id="position<?php echo $position; ?>"<?php if($item['menu_visible'] == 1): ?> data-anchor="position<?php echo $position; ?>"<?php endif; ?>>
	<?php $i = 1; ?>
	<?php foreach ($items AS $item): ?>
		<div class="pp pp-small">
			<figure class="pp-image lloader">
				<a href="<?php echo $item['url']; ?>"><img <?php echo Thumb::generate((!empty($item['image']) ? $item['image'] : Arr::get($item, 'main_image')), array('width' => 475, 'height' => 230, 'crop' => true, 'default_image' => '/media/images/no-image-475.webp', 'placeholder' => '/media/images/no-image-475.webp')); ?>  alt="<?php echo Arr::get($item, 'title'); ?>" /></a>
			</figure>
			<div class="pp-cnt">
				<div class="pp-category">				
					<a href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
				</div>
				<?php if(Arr::get($item, 'title')): ?>
					<h2 class="pp-title"><?php echo Arr::get($item, 'title'); ?></h2>
				<?php endif; ?>
				<?php if(Arr::get($item, 'short_description')): ?>
					<div class="pp-short-desc">
						<?php echo Text::limit_words(strip_tags($item['short_description']), 18, '...'); ?>	
					</div>
				<?php endif; ?>
			</div>
		</div>
		<?php $i++; ?>
	<?php endforeach; ?>
</section>