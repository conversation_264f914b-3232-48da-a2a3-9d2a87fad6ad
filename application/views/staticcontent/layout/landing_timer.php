<?php $item = reset($items); ?>
<?php if(!empty(Arr::get($item, 'color_1'))): ?>
	<?php $this->block('extrahead'); ?>
		<style>
			<?php if(!empty(Arr::get($item, 'color_1')) AND !empty(Arr::get($item, 'color_2'))): ?>
				.ln-timer-<?php echo $position; ?>>div>span{background: linear-gradient(38.75deg, <?php echo ($item['color_1']); ?> 0.88%, <?php echo ($item['color_2']); ?> 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;}
			<?php elseif(!empty(Arr::get($item, 'color_1'))): ?>
				.ln-timer{color: <?php echo ($item['color_1']); ?>;}
			<?php endif; ?>
		</style>
	<?php $this->endblock('extrahead'); ?>
<?php endif; ?>
<section class="ln-section ln-section-timer ln-section-img" id="position<?php echo $position; ?>"<?php if($item['menu_visible'] == 1): ?> data-anchor="position<?php echo $position; ?>"<?php endif; ?>>
	<?php if(!empty(Arr::get($item, 'date_active_to'))): ?>
		<div class="ln-timer ln-timer-<?php echo $position; ?>" data-static-countdown="<?php echo date('Y/m/d H:i:s', $item['date_active_to']); ?>"></div>
	<?php endif; ?>
</section>