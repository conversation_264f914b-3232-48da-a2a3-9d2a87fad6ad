<?php $item = reset($items); ?>
<?php if(!empty(Arr::get($item, 'color_1')) AND !empty(Arr::get($item, 'color_2'))): ?>
	<?php $this->block('extrahead'); ?>
		<style>
			.ln-timer-<?php echo $position; ?>{background: linear-gradient(44.91deg, <?php echo ($item['color_1']); ?> 0%, <?php echo ($item['color_2']); ?> 100%);}
		</style>
	<?php $this->endblock('extrahead'); ?>
<?php endif; ?>
<section class="ln-section ln-section-timer ln-section-img" id="position<?php echo $position; ?>"<?php if($item['menu_visible'] == 1): ?> data-anchor="position<?php echo $position; ?>"<?php endif; ?>>
	<?php if(!empty(Arr::get($item, 'date_active_to'))): ?>
		<div class="ln-timer ln-timer-<?php echo $position; ?>" data-static-countdown="<?php echo date('Y/m/d H:i:s', $item['date_active_to']); ?>"></div>
	<?php endif; ?>
</section>