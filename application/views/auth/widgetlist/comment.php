<?php if (count($items)): ?>
	<?php if (isset($pagination)): ?><?php echo $pagination; ?><div class="clear"></div><?php endif; ?>
	<div class="table-auth a-comments-table">
		<div class="orders-head comments-head">
			<div class="table-col-comment col-com-item"><?php echo Arr::get($cmslabel, 'auth_comment_item'); ?></div>
			<div class="table-col-comment col-com-rate"><?php echo Arr::get($cmslabel, 'auth_comment_rate'); ?></div>
			<div class="table-col-comment col-com-btn"></div>
		</div>
		<?php foreach ($items as $item): ?>
			<?php $object = $item->get_object(); ?>
			<div class="com-ctn-section" id="com-<?php echo $item->id; ?>">
				<div class="com-header">
					<?php if ($object): ?>
						<div class="col-com-item">
							<div class="com-img">
								<figure>
									<img loading="lazy" <?php echo Thumb::generate($object->main_image, array('width' => 80, 'height' => 80, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-140.webp', 'srcset' => '160c 2x')); ?> alt="<?php echo $object->title; ?>" />
								</figure>
							</div>
							<a class="com-title" href="<?php echo $object->get_absolute_url(); ?>"><?php echo $object->title_si; ?></a>
						</div>
						<div class="col-com-rate">							
							<?php if(!empty($item->rate)): ?>
				                <span class="cp-rate comment-rate fz0">
				                    <?php for ($rate = 1; $rate <=5; $rate++): ?>
				                        <span class="icon-star-empty <?php if ($rate <= $item->rate): ?> icon-star<?php endif ?>"></span>     
				                    <?php endfor; ?>
				                </span>

							<?php else: ?>
								-
							<?php endif; ?>
						</div>
						<div class="table-col-comment col-com-btn">
							<a class="btn-com-details"id="btn-com-<?php echo $item->id; ?>" href="javascript:toggleBox(['div#com-<?php echo $item->id; ?>', 'a#btn-com-<?php echo $item->id; ?>']);">
								<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'webshop_btn_com', 'Pokaži podrobnosti'); ?></span>
								<span class="btn-active"><?php echo Arr::get($cmslabel, 'webshop_btn_hide_com', 'Zapri podrobnosti'); ?></span>
							</a>
						</div>
					<?php else: ?>
						<?php echo $item->title; ?>
					<?php endif; ?>
				</div>
				<div class="com-cnt">
					<div class="com-cnt-col1 col-com-item">
						<div class="com-date"><?php echo Date::humanize_span($item->datetime_created, 'archive'); ?></div>
						<div class="com-text"><?php echo $item->message; ?></div>
					</div>
					<div class="col-com-rate">
						<div class="com-rate-positive"><?php echo $item->review_positive; ?></div>
						<div class="com-rate-negative"><?php echo $item->review_negative * -1; ?></div>
					</div>					
				</div>
				<div>
					<?php if ($object): ?>
						<a href="<?php echo $object->get_absolute_url(); ?>" class="button button2"><?php echo Arr::get($cmslabel, 'view_object'); ?></a>
					<?php endif; ?>
				</div>
			</div>
		<?php endforeach; ?>
	</div>

	<div class="a-com-special">
		<div class="a-com-special-title"><?php echo Arr::get($cmslabel, 'auth_special_items'); ?></div>
		<div class="a-com-special-items-section">
		</div>
	</div>

	<?php if (isset($pagination)): ?><?php echo $pagination; ?><div class="clear"></div><?php endif; ?>
	<?php if ((isset($pagination) AND $pagination->total_pages > 1) OR !isset($items_total)): ?>
		<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_comment', FALSE); ?>?all=1"><?php echo Arr::get($cmslabel, 'all_comment'); ?></a>
	<?php endif; ?>
<?php else: ?>
	<div class="auth-no-content">
		<?php echo Arr::get($cmslabel, 'no_my_comments'); ?>
	</div>
<?php endif; ?>