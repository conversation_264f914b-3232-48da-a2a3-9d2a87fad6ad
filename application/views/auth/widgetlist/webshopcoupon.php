<?php if (count($items)): ?>
	<div class="clear ww-auth-coupons-list">
		<div class="orders-head coupons-head">
			<div class="table-col-coupon col-coupon-num"><?php echo Arr::get($cmslabel, 'coupon_code'); ?></div>
			<div class="table-col-coupon col-coupon-desc"><?php echo Arr::get($cmslabel, 'coupon_desc'); ?></div>
			<div class="table-col-coupon col-coupon-total"><?php echo Arr::get($cmslabel, 'coupons_total'); ?></div>
			<div class="table-col-coupon col-coupon-date"><?php echo Arr::get($cmslabel, 'coupons_date'); ?></div>
		</div>
		<div class="ww-auth-coupons-table" id="webshop_coupon_list" <?php if (!count($items)): ?>style="display: none"<?php endif; ?>>
			<?php echo View::factory('auth/widgetlist/webshopcoupon_entry', ['items' => $items]); ?>
		</div>
	</div>

	<?php if(isset($dashboard) AND count($items) > 0): ?>
		<a class="btn btn-white btn-all-orders" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE); ?>?all=1"><?php echo Arr::get($cmslabel, 'all_webshoporder', 'Sve narudžbe'); ?></a>
	<?php endif; ?>
<?php else: ?>
	<div class="auth-no-orders auth-no-content"><?php echo Arr::get($cmslabel, 'auth_no_coupons', 'Nemate zabilježen nijedan kupon.'); ?></div>
<?php endif; ?>