<div class="orders-head">
	<div class="table-col-order col-order-num"><?php echo Arr::get($cmslabel, 'order_no'); ?></div>
	<div class="table-col-order col-order-date"><?php echo Arr::get($cmslabel, 'order_date'); ?></div>
	<div class="table-col-order col-order-total"><?php echo Arr::get($cmslabel, 'order_total'); ?></div>
	<div class="table-col-order col-order-status"><?php echo Arr::get($cmslabel, 'order_status'); ?></div>
	<div class="table-col-order col-order-btns"></div>
</div>
<?php foreach ($items as $item): ?>
	<div class="order-row clear" id="order-<?php echo $item->number; ?>">
		
		<!-- Order Item -->
		<div class="table-order" onclick="toggleBox(['div#order-<?php echo $item->number; ?>', 'a#btn-order-<?php echo $item->number; ?>']);">
            <div class="table-col col-order-num"><?php echo $item->id; ?></div>
            <div class="table-col col-order-date"><?php echo Date::humanize($item->datetime_created); ?></div>
			<div class="table-col col-order-total"><strong><?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?></strong></div>
			<div class="table-col col-order-status">
				<div class="col-order-status-label"><?php echo $item->status->title($item->lang); ?></div>
				<div class="col-order-status-bar">
					<?php $w = 30; ?>
					<?php $c; ?>
					<?php 
						if($item->status->code == 'ceka_uplatu'){
							$w = 40;
						}
						if($item->status->code == 'placeno'){
							$w = 55;
						}
						if($item->status->code == 'poslano'){
							$w = 75;
						}
						if($item->status->code == 'dostavljeno' OR $item->status->code == 'otkazano'){
							$w = 99;
						}
						if($item->status->code == 'otkazano'){
							$c = "#E21937";
						}	
					?>
					<div class="col-order-status-bar-bg" style="width: <?php echo $w; ?>%;<?php if($item->status->code == 'otkazano'): ?> background: <?php echo $c; ?><?php endif; ?>"></div>
				</div>
			</div>
			<?php /*
			<div class="table-col col-order-terms">
				<?php $terms_pdf = Utils::file_url($item->terms_pdf); ?>
				<?php if($terms_pdf): ?>
					<a class="btn-download btn-download-pdf btn-download-link" href="<?php echo $terms_pdf; ?>" target="_blank" title="<?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?>"><span><?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?></span></a>
				<?php endif; ?>
			</div>
			*/ ?>
			<div class="table-col col-order-btns">
				<a class="btn-order-details" id="btn-order-<?php echo $item->number; ?>" href="javascript:">
					<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'webshop_btn_order_details', 'Pokaži podrobnosti'); ?></span>
					<span class="btn-active"><?php echo Arr::get($cmslabel, 'webshop_btn_hide_order_details', 'Zapri podrobnosti'); ?></span>
				</a>
			</div>
		</div>

		<!-- Order Item Details (subitems) -->
		<?php if ($item->items): ?>
			<?php $product_ids = $item->items->as_array(NULL, 'product_id'); ?>
			<?php $items_qty = ($product_ids) ? Catalog::products(array('lang' => $info['lang'], 'filters' => array('id' => $product_ids))) : []; ?>
			<div class="order-details clear">
				<div class="w-table w-table-details">
					<?php foreach ($item->items AS $item_item): ?>
						<?php $item_shopping_cart_code = (!empty($items_qty[$item_item->product_id]['shopping_cart_code'])) ? $items_qty[$item_item->product_id]['shopping_cart_code'] : '' ?>
						<div class="wp wp-details">
							<div class="wp-image">
								<figure>
									<img loading="lazy" <?php echo Thumb::generate($item_item->main_image, array('width' => 140, 'height' => 140, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-140.webp', 'srcset' => '280c 2x')); ?> alt="<?php echo $item_item->title; ?>" data-product_main_image="<?php echo $item_shopping_cart_code; ?>" />
								</figure>
							</div>
							<div class="wp-cnt">
								<div class="wp-category"><?php echo $item_item->category_title; ?></div>
								<div class="wp-title" data-product_title="<?php echo $item_shopping_cart_code; ?>"><?php echo $item_item->title; ?></div>
								<div class="wp-code"><?php echo Arr::get($cmslabel, 'id'); ?>: <span data-product_code="<?php echo $item_shopping_cart_code; ?>"><?php echo $item_item->code; ?></span></div>
								<?php if($item_item->services_text): ?>
									<div class="wp-service" data-product_title="<?php echo $item_shopping_cart_code; ?>"><?php echo nl2br(trim($item_item->services_text, ', ')); ?></div>
								<?php endif; ?>
							</div>
							<div class="wp-total">
								<div class="wp-price">
									<?php if ($item_item->basic_price > $item_item->price): ?>
										<div class="wp-price-old line-through product_total_basic" data-product_basic_price="<?php echo $item_shopping_cart_code; ?>"><?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $item->currency->display); ?></div>
										<div class="wp-price-current wp-price-discount red product_total" data-product_price="<?php echo $item_shopping_cart_code; ?>"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?></div>
									<?php else: ?>
										<div class="wp-price-current product_total" data-product_price="<?php echo $item_shopping_cart_code; ?>"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?></div>
									<?php endif ?>
								</div>
								<div class="wp-qty-count wp-price-old">
									<span class="product_qty"><?php echo $item_item->qty; ?></span> x <?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?>
								</div>
							</div>
							
							<!-- Repeat Product Order -->
							<?php if (!empty($items_qty[$item_item->product_id]['is_available'])): ?>
								<a class="btn-order-again" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item_shopping_cart_code; ?>', '_tracking:detail', 'simple', 'simple', 3)"><?php echo Arr::get($cmslabel, 'order_again', 'Ponovi narudžbu'); ?></a>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
					<div class="wp-total-sum">
						<div class="wp-total-sum-col wp-total-sum-col1">
							<?php  /* ?>
							<span><?php echo Arr::get($cmslabel, 'shipping'); ?>:</span> 
							<?php */ ?>
							<span><?php echo Arr::get($cmslabel, 'payment_solution'); ?>:</span>
							<span><?php echo Arr::get($cmslabel, 'total_to_pay'); ?>:</span>
						</div>
						<div class="wp-total-sum-col wp-total-sum-col2">
							<?php  /* ?>
							<span><?php echo $item->shipping->description($info['lang']); ?> <strong>(<?php echo Utils::currency_format($item->shipping_total * $item->exchange, $item->currency->display); ?>)</strong></span> 
							<?php */ ?>
							<span><?php echo $item->payment->description($info['lang']); ?></span> 
							<span><strong><?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?></strong>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php endforeach; ?>