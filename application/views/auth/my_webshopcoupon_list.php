<?php $this->extend('auth/default'); ?>

<?php $this->block('auth_title'); ?>
	<div class="auth-coupons-list-header">
		<h1 class="auth-main-title">
			<?php echo Arr::get($cms_page, 'seo_h1'); ?>
			<span class="auth-title-counter">(<?php echo $items_total; ?>)</span>
		</h1>

		<!-- Add Coupon Form -->
		<div class="ww-coupons-form ww-auth-coupons-form">
			<label class="ww-coupons-label" for="coupon_code"><?php echo Arr::get($cmslabel, 'coupon_have_coupon'); ?></label>
			<div class="ww-coupons-add ww-auth-coupons-add">
				<input class="ww-coupons-input" type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
				<a class="btn btn-lightBlue ww-btn-add" href="javascript:cmscoupon.save_for_later('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
				<div class="coupon_message" style="display: none"></div>
			</div>
		</div>
	</div>
<?php $this->endblock('auth_title'); ?>

<?php $this->block('content2'); ?>
<div id="items_widgetlist_webshop_layout">
	<?php echo View::factory('auth/widgetlist/webshopcoupon', ['items' => $items, 'pagination' => $pagination, 'items_total' => $items_total, 'items_filtered_total' => $items_filtered_total]); ?>
</div>
<?php $this->endblock('content2'); ?>

<?php $this->block('extrabody'); ?>
<?php echo Html::media('cmsinfinitescroll', 'js'); ?>
<script type="text/javascript">
	$(function(){
		$('#items_widgetlist_webshop').CmsInfiniteScroll();
	});
</script>
<?php $this->endblock('extrabody'); ?>
