<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
<div class="auth-box">
	<div class="wrapper">
		<div class="a-col a-col1">
			<h2 class="auth-title"><?php echo Arr::get($cmslabel, 'create_account'); ?></h2>
			<div class="auth-registration-info"><?php echo Arr::get($cmslabel, 'auth_signup_info'); ?></div>

			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
                    <?php $message_text = Arr::get($cmslabel, "{$message_type}_{$message}"); ?>
                    <?php if ($message_text): ?>
					    <p class="global-<?php echo $message_type; ?>"><?php echo $message_text; ?></p>
                    <?php endif; ?>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success'): ?>
				<!-- Signup form -->
				<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="signup" class="ajax_siteform auth-form form-animated-label auth-signup-form" data-location_request_country="<?php echo (!empty($autocomplete_countries)) ? $autocomplete_countries : "" ?>" data-webshop_autocomplete="zipcode_city_location">
					<h3 class="a-subtitle"><?php echo Arr::get($cmslabel, 'signup_data_title', 'Sign in details'); ?></h3>
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<fieldset>
						<?php foreach ($customer_fields as $field): ?>
							<?php if ($field == 'loyalty_code') {continue;} ?>
							<?php $error = Valid::get_error($field, $message); ?>
							<?php if($field == 'first_name'): ?>
								<h3 class="a-subtitle a-subtitle-special"><?php echo Arr::get($cmslabel, 'signup_personal_data', 'Personal details'); ?></h3>
							<?php endif; ?>

							<?php if($field == 'loyalty_request'): ?>
								<div class="signup-loyalty-container">
									<h3 class="a-subtitle a-subtitle-special"><?php echo Arr::get($cmslabel, 'loyalty_signup', 'Big Bang Loyalty Club'); ?></h3>
								</div>
							<?php endif; ?>

							<div class="field field-<?php echo $field; ?>">
							<p class="field field-<?php echo $field; ?>">
								<?php if (in_array($field, ['accept_terms'])): ?>
									<?php if (!empty($gdpr_template_api)): ?>
										<div class="signup-note">
											<?php echo $gdpr_template_api['content']['header']; ?>
										</div>
										<div class="signup-gdpr-checkbox">
											<p class="field field-gdpr_template_api">
												<input type="checkbox" name="gdpr_template_api_all" value="1" id="gdpr_template_api_all" />
												<label for="gdpr_template_api_all"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?></label>
												<span class="signup-desc active">
													<span class="signup-desc-cnt signup-desc-cnt-special"><?php echo Arr::get($cmslabel, 'gdpr_select_all_tips'); ?></span>
												</span>
											</p>

											<div class="signup-gdpr-checkbox-wrapper">
												<?php foreach ($gdpr_template_api['objects'] AS $gdpr_template_api_object): ?>
													<p class="field field-gdpr_template_api">
														<input type="checkbox" name="gdpr_template_api[]" value="<?php echo $gdpr_template_api_object['code']; ?>" id="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>" />
														<label for="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>"><?php echo $gdpr_template_api_object['title']; ?></label>
														<span class="signup-desc">
															<span class="signup-desc-link">
																<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'gdpr_show_more'); ?></span>
																<span class="btn-active"><?php echo Arr::get($cmslabel, 'gdpr_hidde_more'); ?></span>													
															</span>
															<span class="signup-desc-cnt"><?php echo $gdpr_template_api_object['content']; ?></span>
														</span>
													</p>
												<?php endforeach; ?>
											</div>
										</div>
										<div class="signup-note">
											<?php echo $gdpr_template_api['content']['footer']; ?>
										</div>
									<?php endif; ?>

									<?php echo $item->input($field, 'form'); ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? ' ' : ''); ?></label>
								<?php elseif ($field == 'loyalty_request'): ?>	
									<?php $options = Kohana::config('app.loyalty.user_join'); ?>
									<?php foreach ($options AS $option_id => $option_title): ?>
										<span class="field field-loyalty field-loyalty_request-<?php echo $option_id; ?>">
											<input type="radio" id="field-loyalty_request-<?php echo $option_id; ?>" name="loyalty_request" value="<?php echo $option_id; ?>" <?php if ($option_id == 'n'): ?>checked="checked"<?php endif; ?>>
											<label for="field-loyalty_request-<?php echo $option_id; ?>"><?php echo Arr::get($cmslabel, $field.'_'.$option_id, $option_title); ?></label>
											<span class="note">
												<span><?php echo Arr::get($cmslabel, $field.'_'.$option_id.'_note'); ?></span>
												<?php if ($option_id == 'e'): ?>
													<label for="field-loyalty_code"><?php echo Arr::get($cmslabel, 'signup_loyalty_code'); ?></label>
													<?php echo $item->input('loyalty_code', 'form'); ?>
													<span class="forgotten-loyalty-number"><?php echo Arr::get($cmslabel, 'forgotten_loyalty_number'); ?></span>
													<span id="field-error-loyalty_code" class="field_error error" style="display: none"></span>
												<?php elseif ($option_id == 'n'): ?>
	                                                <label for="field-gender" class="label-gender"><?php echo Arr::get($cmslabel, 'gender', $item->meta()->field('gender')->label); ?><?php echo (in_array('gender', $request_fields) ? ' ' : ''); ?></label>
	                                                <?php echo $item->input('gender', 'form'); ?>
	                                                <span id="field-error-gender" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
	                                                
	                                                <label for="field-birth_year" class="label-birth_year"><?php echo Arr::get($cmslabel, 'birth_year', $item->meta()->field('birth_year')->label); ?><?php echo (in_array('birth_year', $request_fields) ? ' ' : ''); ?></label>
	                                                <?php $current_year = date("Y"); ?>
	                                                <select id="field-birth_year" name="birth_year" class="select-birth_year">
	                                                	<option value=""><?php echo Arr::get($cmslabel, 'birth_year', 'Leto'); ?></option>
	                                                	<?php $max_year = ($current_year - Kohana::config('app.loyalty.adult_year')); ?>
	                                                    <?php for ($i = $max_year; $i >= $current_year - 100; $i--) { ?>
	                                                    	<option value="<?php echo $i; ?>"><?php echo $i; ?></option>
	                                                    <?php } ?>
	                                                </select>
	                                                <span id="field-error-birth_year" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
                                                <?php endif; ?>
											</span>
										</span>
									<?php endforeach; ?>
                                <?php elseif (in_array($field, ['gender', 'birth_year'])): ?>
                                    <?php continue; ?>
                                <?php elseif ($field == 'location'): ?>
									<input type="text" name="location" id="field-location" value="<?php echo (!empty($customer_data['zipcode']) ? $customer_data['zipcode'] . ' ' : '') . Arr::get($customer_data, 'city'); ?>" data-autocomplete_module="auth" data-display_format="3" />
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
								<?php else: ?>
									<?php if($field == 'password'): ?>
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'password', 'Lozinka'); ?></label>
                                    <?php elseif($field == 'address'): ?>
                                        <label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'address_street', 'Ulica'); ?></label>
									<?php else: ?>
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?><?php echo (in_array($field, $request_fields) ? ' ' : ''); ?></label>
									<?php endif; ?>
									<?php echo $item->input($field, 'form'); ?>
								<?php endif; ?>
								<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
							</p>
							</div>
						<?php endforeach; ?>
					</fieldset>

					<div class="submit">
						<button class="btn btn-green btn-registration-confirm"><span><?php echo Arr::get($cmslabel, 'confirm_registration'); ?></span></button>
					</div>
				</form>
				<!-- / Signup form -->

				<!-- Social auth -->
				<?php if (Kohana::config('app.auth.use_usersocials') === TRUE): ?>
					<div class="auth-social">
						<?php $usersocial_base = Utils::app_absolute_url($info['lang'], 'auth', 'hybridauth', FALSE); ?>
						<?php foreach (Kohana::config('authsocial.providers') as $provider => $provider_config): ?>
							<?php if (Arr::get($provider_config, 'enabled')): ?>
								<div class="auth-social-<?php echo strtolower($provider); ?>"><a href="<?php echo $usersocial_base; ?>?provider=<?php echo $provider; ?>">Sign-in with <?php echo $provider; ?></a></div>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
				<!-- / Social auth -->

			<?php endif; ?>
		</div>
		<div class="a-col a-col2">
			<?php echo Arr::get($cms_page, 'content'); ?>
			<h2 class="auth-title auth-title-special"><?php echo Arr::get($cmslabel, 'already_register'); ?></h2>
			<div class="submit-container">
				<a class="btn btn-green btn-medium submit-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'register', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></a>
				<p class="auth-links">
					<a class="button2 forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
				</p>
			</div>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>