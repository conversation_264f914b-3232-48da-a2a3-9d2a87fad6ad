<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>

<?php $this->block('main'); ?>
<div class="auth-section wrapper">
	<?php if ($user): ?>
		<?php echo View::factory('auth/widget/auth_sidebar'); ?>
	<?php endif; ?>
	<div class="auth-content">
		<?php $message_type = (isset($message_type) AND $message_type) ? $message_type : Arr::get($info, 'message_type', ''); ?>
		<?php $message = (isset($message) AND $message) ? $message : Arr::get($info, 'message', ''); ?>
		<?php if ($message_type AND $message): ?>
			<?php if (is_array($message)): ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php else: ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
			<?php endif; ?>
		<?php endif; ?>

		<?php $this->block('auth_title'); ?>
			<h1 class="auth-main-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
		<?php $this->endblock('auth_title'); ?>
		<?php echo Arr::get($cms_page, 'content'); ?>

		<?php $this->block('content2'); ?><?php $this->endblock('content2'); ?>
	</div>
</div>

<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>