<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
<div class="auth-box">
	<div class="wrapper">
		<div class="a-col a-col1">
			<div class="auth-title"><?php echo Arr::get($cmslabel, 'login_title', 'Sign in'); ?></div>
			<div class="auth-registration-info"><?php echo Arr::get($cmslabel, 'login_note'); ?></div>

			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success' OR (in_array($message, ['confirm_signup', 'reset_password', 'logout', 'forgotten_password']))): ?>
				<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="ajax_siteform auth-form form-animated-label auth-login-form">
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>
					<fieldset>
						<?php $error = ($message_type AND $message_type != 'success' AND $message) ? "{$message_type}_{$message}" : ""; ?>
						<p class="email field">
							<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
							<input type="email" name="email" id="id_email" />
							<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
						</p>
						<p class="password field"><label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label><input type="password" name="password" id="id_password" /></p>
						<p class="remember"><input type="checkbox" name="remember" id="id_remember" value="1" checked /><label for="id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label></p>
					</fieldset>
					<div class="submit-container">
						<p class="submit"><button class="submit-btn btn-lightBlue btn-medium" type="submit"><?php echo Arr::get($cmslabel, 'login'); ?></button></p>
						<p class="auth-links">
							<a class="button2 forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</p>
					</div>
				</form>

				<?php if (Kohana::config('app.auth.use_usersocials') === TRUE): ?>
					<div class="auth-social">
						<?php $usersocial_base = Utils::app_absolute_url($info['lang'], 'auth', 'hybridauth', FALSE); ?>
						<?php foreach (Kohana::config('authsocial.providers') as $provider => $provider_config): ?>
							<?php if (Arr::get($provider_config, 'enabled')): ?>
								<div class="auth-social-<?php echo strtolower($provider); ?>"><a href="<?php echo $usersocial_base; ?>?provider=<?php echo $provider; ?>">Sign-in with <?php echo $provider; ?></a></div>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		</div>
		<div class="a-col a-col2">
			<?php echo Arr::get($cms_page, 'content'); ?>
			<p><a class="btn btn-lightBlue btn-medium registration-btn" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'signup'); ?></span></a></p>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>