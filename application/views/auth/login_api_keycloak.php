<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Login</title>
</head>
<body>

<img src="/shared/images/loader.gif" alt="" />

<script src="/shared/jquery/1.8.3.min.js" ></script>
<script src="<?php echo $data['url']; ?>/js/keycloak.js"></script>
<script>
var keycloak = Keycloak({
    url: '<?php echo $data['url']; ?>',
    realm: '<?php echo $data['username']; ?>',
    clientId: '<?php echo $data['client_id']; ?>'
});

keycloak.init({
    onLoad: 'check-sso',
    silentCheckSsoRedirectUri: window.location.origin + '/sso/silent-check-sso.html'
}).then(function(authenticated) {
    console.debug('keycloak.authenticated', authenticated);
    // detect login
    if (authenticated) {

        // redirect to auth
        keycloak.loadUserProfile().then(function(profile) {
            console.debug('keycloak authenticated, tokenParsed', keycloak.tokenParsed, 'idToken', keycloak.idToken);

            jQuery.ajax({
                url: '/api/auth/login/?redirect=<?php echo Arr::get($_GET, 'redirect'); ?>',
                data: {'jwtToken': keycloak.idToken, 'tokenParsed': keycloak.tokenParsed},
                dataType: 'json',
                cache: false,
                method: 'POST',
                type: 'POST', // For jQuery < 1.9
                async: false,
                success: function (data) {
                    console.debug('response_data', data);
                    if (data.response == 'ok' && data.redirect_url) {
                        document.location = data.redirect_url;
                    } else if (data.response == 'error' && data.redirect_url) {
                        document.location = data.redirect_url;
                        keycloak.logout({
                            locale: 'sl',
                            redirectUri: data.redirect_url + '&logout=1'
                        });

                    }

                }
            });
        }).catch(function() {
            console.debug('keycloak - failed to load user profile');
        });
    } else {
        window.location = keycloak.createLoginUrl({locale: 'sl'});
    }
}).catch(function() {
    console.debug('keycloak - failed to initialize');
});

</script>
</body>
</html>