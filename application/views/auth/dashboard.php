<?php $this->extend('auth/default'); ?>
<?php $this->block('page_class'); ?> page-dashboard<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<style>
		<?php $loyalty_bg = Html::image_from_string(Arr::get($cmslabel, 'dashboard_loyalty_background')); ?>
		<?php if (!empty($loyalty_bg)): ?>
			.a-box-loyalty{background: url(<?php echo $loyalty_bg; ?>) no-repeat; background-size: cover; background-position: top right;}
		<?php endif; ?>
	</style>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('content2'); ?>

<?php $this->block('auth_title'); ?> <?php $this->endblock('auth_title'); ?>

	<div class="clear a-box-loyalty">
		<?php if ($user->loyalty_code): ?>
			<div class="a-title-loyalty"><?php echo Arr::get($cmslabel, 'auth_loyalty_title', 'Informacije o članu'); ?></div>
			<div class="a-btn-loyalty-more-about"><?php echo Arr::get($cmslabel, 'auth_loyalty_more_about'); ?></div>
		<?php else: ?>
			<div class="a-title-loyalty"><?php echo Arr::get($cmslabel, 'auth_loyalty_title', 'Ekskluzivne ponudbe samo za člane Kluba'); ?></div>
			<?php if (!empty($user->config['loyalty_cart_request_api']) AND !empty($user->config['loyalty_cart_request_loyalty_code'])): ?>
				<div id="loyalty_request_e_success" class="global-success auth-loyalty-success"><?php echo Arr::get($cmslabel, 'auth_loyalty_attach_card', 'Kartica vjernosti je uspješno pridružena.'); ?></div>
			<?php elseif (!empty($user->config['loyalty_cart_request_api'])): ?>
				<div id="loyalty_request_n_success" class="global-success auth-loyalty-success"><?php echo Arr::get($cmslabel, 'auth_loyalty_signup_success', 'Postali ste član kluba vjernosti.'); ?></div>
			<?php else: ?>
				<div class="a-loyalty-nocard" data-loyalty_request_hide="1">
					<div class="a-loyalty-request-card">
						<span class="a-loyalty-request-card-label a-loyalty-label"><?php echo Arr::get($cmslabel, 'request_card_title'); ?></span>
						<a class="btn a-loyalty-request-card" href="javascript:cmsauth.loyalty_request('n');"><?php echo Arr::get($cmslabel, 'request_new_card'); ?></a>
						<span id="field-error-loyalty_code_fields" class="field_error global-error field_error-loyalty-box a-loyalty-request-card-error" style="display: none;"></span>
					</div>
					<div class="a-loyalty-field-container">
						<span class="a-loyalty-label" for="loyalty_code"><?php echo Arr::get($cmslabel, 'auth_add_loyalty_card'); ?></span>
						<div class="a-loyalty-field-card">
							<input class="auth-loyalty-input" type="text" name="loyalty_code" id="loyalty_code" value="" placeholder="<?php echo Arr::get($cmslabel, 'enter_card'); ?>" >
							<a class="btn btn-attach-card" href="javascript:cmsauth.loyalty_request('e');"><?php echo Arr::get($cmslabel, 'attach_card', 'Pridruži'); ?></a>
							<span id="field-error-loyalty_code" class="error a-loyalty-error" style="display: none"></span>
						</div>
					</div>
				</div>
				<div id="loyalty_request_n_success" class="auth-loyalty-success" style="display: none"><?php echo Arr::get($cmslabel, 'auth_loyalty_signup_success', 'Postali ste član kluba vjernosti.'); ?></div>
				<div id="loyalty_request_e_success" class="global-success auth-loyalty-success" style="display: none"><?php echo Arr::get($cmslabel, 'auth_loyalty_attach_card', 'Kartica vjernosti je uspješno pridružena.'); ?></div>
			<?php endif; ?>
		<?php endif; ?>
	</div>

<div class="clear a-intro">
	<div class="a-intro-left">
		<div class="a-intro-title"><?php echo Arr::get($cmslabel, 'you_can'); ?>...</div>
		<ul class="a-menu">
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false), Arr::get($cmslabel, 'auth_view_orders')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE), Arr::get($cmslabel, 'auth_view_coupons')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_comment', FALSE), Arr::get($cmslabel, 'auth_my_comment')); ?></li>
			<?php /*if (Kohana::config('app.catalog.use_wishlists') AND $wishlist AND $wishlist['total_items'] > 0): ?>
				<li><?php echo str_replace("%LINK%", $wishlist['url'], Arr::get($cmslabel, 'auth_my_wishlist')) ; ?></li>
			<?php endif;*/ ?>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE), Arr::get($cmslabel, 'auth_edit_profile')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE), Arr::get($cmslabel, 'auth_change_password')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE).'?redirect='.$info['redirect_url'], Arr::get($cmslabel, 'auth_logout')); ?></li>
		</ul>
	</div>

	<div class="a-intro-user">
		<div class="a-intro-title"><?php echo $user->first_name; ?> <?php echo $user->last_name; ?></div>
		<?php if($user->email): ?>
			<p>
				<span class="a-intro-icon a-intro-email"><?php echo $user->email; ?></span>
			</p>
		<?php endif; ?>
		<?php if($user->phone): ?>
			<p>
				<span class="a-intro-icon a-intro-tel"><?php echo $user->phone; ?></span>
			</p>
		<?php endif; ?>
		<?php if($user->address): ?>
		<p>
			<span class="a-intro-icon a-intro-address">
				<?php echo $user->address; ?><?php echo (!empty($user->house_number) ? ' ' . $user->house_number : ''); ?><br>
				<?php echo $user->zipcode; ?> <?php echo $user->city; ?>
			</span>
		</p>
		<?php endif; ?>
		
		<?php /*if ($user->loyalty_code): ?>
			<div class="a-intro-loyalty">
				<strong><?php echo Arr::get($cmslabel, 'card_number'); ?>:</strong> <?php echo $user->loyalty_code; ?> 
			</div>
		<?php endif;*/ ?>
		
		<a class="btn btn-white btn-medium btn-auth-edit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a>
	</div>	
</div>

<!-- Orders -->
<?php $orders = $user->get_webshoporder(TRUE, 1); ?>
<?php if(count($orders)): ?>
	<div class="orders-container" id="orders">
		<h2 class="a-section-title"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><?php echo Arr::get($cmslabel, 'last_order'); ?></a></h2>
		<?php echo View::factory('auth/widgetlist/webshoporder', array('items' => $orders, 'dashboard' => true)); ?>
	</div>
<?php endif; ?>
<?php $this->endblock('content2'); ?>


<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmsauth,barcode', 'js'); ?>
	<script>
		$(function(){
			<?php if (!empty($loyalty_display)): ?>$("#loyalty_code").barcode("<?php echo $loyalty_display[0]; ?> ", "<?php echo $loyalty_display[1]; ?>", {barWidth:2, barHeight:50});<?php endif; ?>
		});
	</script>
<?php $this->endblock('extrabody'); ?>