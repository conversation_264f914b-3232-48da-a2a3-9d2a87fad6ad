<?php $this->extend('auth/default'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-animated-label auth-form auth-edit-profile-form" data-location_request_country="<?php echo (!empty($autocomplete_countries)) ? $autocomplete_countries : "" ?>" data-webshop_autocomplete="zipcode_city_location">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<fieldset>          
			<?php foreach ($customer_fields as $field): ?>
				<?php $error = Valid::get_error($field, $message); ?>
				<div class="field field-<?php echo $field; ?>">
					<p>
						<?php if($field == 'newsletter'): ?>
							<?php echo $item->input($field, 'form'); ?>	
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
						<?php elseif ($field == 'location'): ?>
							<input type="text" name="location" id="field-location" value="<?php echo (!empty($item->zipcode) ? $item->zipcode . ' ' : '') . (!empty($item->city) ? $item->city : ''); ?>" data-display_format="3" />
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
						<?php elseif ($field == 'phone' AND (!empty($user->loyalty_code) OR (!empty($user->onboard_status) AND in_array($user->onboard_status, ['1', '2', '3', '4', '5'])))): ?>
                            <label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
                            <?php echo $item->input($field, 'form', ['attributes' => ['disabled']]); ?>
                            <input type="hidden" id="field-phone" name="phone" value="<?php echo $item->phone; ?>">
                        <?php elseif ($field == 'address'): ?>
                            <label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field . '_street'); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
                            <?php echo $item->input($field, 'form'); ?>
                        <?php else: ?>
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
							<?php echo $item->input($field, 'form'); ?>		
						<?php endif; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</p>
				</div>
			<?php endforeach; ?>

			<?php if (!empty($gdpr_template_api)): ?>
				<div class="signup-note">
					<?php echo $gdpr_template_api['content']['header']; ?>
				</div>
				<div class="signup-gdpr-checkbox">
					<p class="field field-gdpr_template_api">
						<input type="checkbox" name="gdpr_template_api_all" value="1" id="gdpr_template_api_all" />
						<label for="gdpr_template_api_all"><?php echo Arr::get($cmslabel, 'gdpr_select_all'); ?></label>
						<span class="signup-desc active">
							<span class="signup-desc-cnt signup-desc-cnt-special"><?php echo Arr::get($cmslabel, 'gdpr_select_all_tips'); ?></span>
						</span>
					</p>

					<div class="signup-gdpr-checkbox-wrapper">
						<?php foreach ($gdpr_template_api['objects'] AS $gdpr_template_api_object): ?>
							<p class="field field-gdpr_template_api">
								<input type="checkbox" name="gdpr_template_api[]" value="<?php echo $gdpr_template_api_object['code']; ?>" id="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>" <?php if (!empty($gdpr_template_api_object['subscribed'])): ?>checked<?php endif; ?> />
								<label for="gdpr_template_api-<?php echo $gdpr_template_api_object['code']; ?>"><?php echo $gdpr_template_api_object['title']; ?></label>
								<span class="signup-desc">
									<span class="signup-desc-link">
										<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'gdpr_show_more'); ?></span>
										<span class="btn-active"><?php echo Arr::get($cmslabel, 'gdpr_hidde_more'); ?></span>
									</span>
									<span class="signup-desc-cnt"><?php echo $gdpr_template_api_object['content']; ?></span>
								</span>
							</p>
						<?php endforeach; ?>
					</div>
				</div>
				<div class="signup-note">
					<?php echo $gdpr_template_api['content']['footer']; ?>
				</div>
			<?php endif; ?>

		</fieldset>
		<button class="btn-lightBlue btn-medium" type="submit"><?php echo Arr::get($cmslabel, 'save'); ?></button>
	</form>
<?php $this->endblock('content2'); ?>
