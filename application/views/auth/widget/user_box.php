<div class="aw">
	<a class="btn-header btn-aw<?php if ($user): ?> logedin<?php endif; ?>" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>">
		<span class="btn-header-text"><?php echo Arr::get($cmslabel, 'header_btn_my_acc'); ?></span>
	</a>
	<?php if ($user): ?>
		<div class="aw-popup">
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'my_profile'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder'); ?>"><span><?php echo Arr::get($cmslabel, 'my_orders'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon'); ?>"><span><?php echo Arr::get($cmslabel, 'my_coupons'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_comment'); ?>"><span><?php echo Arr::get($cmslabel, 'my_webshop_comment'); ?></span></a>	
			<?php /*if (Kohana::config('app.catalog.use_wishlists') AND $wishlist AND $wishlist['total_items'] > 0): ?>
				<a href="<?php echo $wishlist['url']; ?>"><span><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></span></a>
			<?php endif;*/ ?>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'change_password'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><span><?php echo Arr::get($cmslabel, 'logout'); ?></span></a>
		</div>
	<?php else: ?>
		<div class="aw-popup user-logout">
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', false); ?>"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'signup'); ?></span></a>
			<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" class="button" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password_userbox'); ?></a>
		</div>
	<?php endif; ?>
	<?php /*
		<div class="aw-login-section">
			<div class="aw-popup aw-login">
				<div class="aw-login-title"><?php echo Arr::get($cmslabel, 'login_title', 'Sign in'); ?></div>
				<div class="aw-login-note"><?php echo Arr::get($cmslabel, 'login_note'); ?></div>
				<div class="aw-login-close"></div>
				
				<form class="aw-login-form form-animated-label ajax_siteform ajax_siteform_loading" action="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>" method="post" accept-charset="utf-8" enctype="multipart/form-data" name="login">
					<div id="field-error-email" class="field_error global-error aw-error" style="display: none"></div>
					<p class="field aw-field-email">
						<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<input type="email" name="email" id="id_email" />
					</p>
					<p class="field password"><label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label><input type="password" name="password" id="id_password" /></p>
					<p class="remember"><input type="checkbox" name="remember" id="id_remember" value="1" checked /><label for="id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label></p>
					<div class="submit-container">
						<button class="btn-lightBlue btn-medium aw-btn-login" type="submit" tabindex="3"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></button>	
						<p class="auth-links">
							<a class="button2 forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</p>
					</div>
				</form>
				
					<div class="submit-container">
						<a class="btn btn-medium btn-lightBlue aw-btn-login aw-btn-signin" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>" tabindex="3"><span><?php echo Arr::get($cmslabel, 'popup_login'); ?></span></a>	
						<p class="auth-links">
							<a class="button2 forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</p>
					</div>
				<div class="aw-bottom">
					<div class="aw-login-title"><?php echo Arr::get($cmslabel, 'aw_signup_title'); ?></div>
					<a class="btn btn-white btn-medium aw-login-signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><?php echo Arr::get($cmslabel, 'signup'); ?></a>
				</div>
			</div>
		</div>
	*/ ?>
</div>
