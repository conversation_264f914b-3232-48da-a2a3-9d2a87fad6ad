<style>
	<?php $loyalty_sidebar_bg = Html::image_from_string(Arr::get($cmslabel, 'dashboard_loyalty_sidebar_background')); ?>
	<?php if (!empty($loyalty_sidebar_bg)): ?>
		.auth-box-loyalty{background: url(<?php echo $loyalty_sidebar_bg; ?>) no-repeat; background-size: cover; background-position: top right;}

		@media screen and (max-width: 980px) {
			.auth-box-loyalty{background: #0092A0;}
		}

		@media screen and (max-width: 760px) {
			.auth-box-loyalty{background: url(<?php echo $loyalty_sidebar_bg; ?>) no-repeat; background-size: cover; background-position: right;}
		}
	<?php endif; ?>
</style>

<div class="auth-sidebar">
	<div class="auth-sidebar-top">
		<h2 class="a-dashboard-title">
			<span><?php echo Arr::get($cmslabel, 'hello', 'Pozdrav'); ?>,</span> <?php echo $user->first_name; ?>			
		</h2>
	</div>
	<ul class="auth-menu">
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', '', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>" class="am-user<?php if (Utils::app_absolute_url($info['lang'], 'auth', '', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'my_profile'); ?></span></a></li>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>#main" class="am-orders<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'my_orders'); ?></span></a></li>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE); ?>" class="am-coupon <?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'my_coupons'); ?></span></a></li>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_comment', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_comment', false); ?>#main" class="am-comment<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_comment', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'my_webshop_comment'); ?></span></a></li>
		<?php /*if (Kohana::config('app.catalog.use_wishlists') AND $wishlist AND $wishlist['total_items'] > 0): ?>
			<li<?php if ($wishlist['url'] == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo $wishlist['url']; ?>" class="am-wishlist <?php if ($wishlist['url'] == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></span></a></li>
		<?php endif;*/ ?>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>" class="am-edit <?php if (Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a></li>
		<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE) == $info['url']): ?> class="active"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>" class="am-password <?php if (Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE) == $info['url']): ?> active<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'change_password'); ?></span></a></li>
		<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>" class="am-logout"><span><?php echo Arr::get($cmslabel, 'logout'); ?></span></a></li>
	</ul>	
	<!-- Loyalty -->
	<?php if ($user->loyalty_code): ?>
		<div class="clear auth-box-loyalty">
			<div class="auth-title-loyalty"><?php echo Arr::get($cmslabel, 'auth_loyalty_sidebar_title', 'Informacije o članu'); ?></div>
			<?php
			$loyalty_display = Utils::barcode($user->loyalty_code);
			$barcode = (!empty($loyalty_display)) ? Html::barcode(true, $loyalty_display[0], $loyalty_display[1], 50, 'h', 2, true, true, '', $info['lang']) : '';
			?>
			<div class="auth-loyalty-cnt">
				<span class="label"><?php echo Arr::get($cmslabel, 'card_number'); ?></span><span class="value"><?php echo $user->loyalty_code; ?></span>
			</div>
			<div class="auth-loyalty-cnt">
				<span class="label"><?php echo Arr::get($cmslabel, 'full_name', 'Ime i prezime'); ?></span><span class="value"><?php echo $user->first_name; ?>  <?php echo $user->last_name; ?></span>
			</div>
		</div>
	<?php endif; ?>
</div>