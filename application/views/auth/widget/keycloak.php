<?php
$service_keycloak = Text::enviroment_config(Kohana::config('app.services.keycloak'));
$service_keycloak_disabled_routes = Kohana::config('app.services.keycloak.disabled_routes');
$active_route = Arr::get($info, 'controller') . '.' . Arr::get($info, 'action');
?>
<?php if (!empty($service_keycloak) AND !in_array($active_route, $service_keycloak_disabled_routes)): ?>
    <script src="<?php echo $service_keycloak['url']; ?>/js/keycloak.js"></script>
    <script>
        var keycloak = Keycloak({
            url: '<?php echo $service_keycloak['url']; ?>',
            realm: '<?php echo $service_keycloak['username']; ?>',
            clientId: '<?php echo $service_keycloak['client_id']; ?>'
        });
        console.debug('CALL keycloak');

        keycloak.init({
            onLoad: 'check-sso',
            silentCheckSsoRedirectUri: window.location.origin + '/sso/silent-check-sso.html',
            checkLoginIframe: true,
        }).then(function (authenticated) {
            console.debug('keycloak.authenticated', authenticated);

            <?php if (empty($info['user_id'])): ?>
            // detect login - svugdje osim na /korisnik/login/?
            if (authenticated) {
                keycloak.loadUserProfile().then(function (profile) {
                    console.debug('keycloak authenticated, tokenParsed', keycloak.tokenParsed, 'idToken', keycloak.idToken);
                    // redirect to auth
                    keycloak_user_login(keycloak);
                }).catch(function () {
                    console.debug('keycloak - failed to load user profile');
                });
            }
            <?php elseif(!empty($info['user_id'])): ?>
            // detect logout - svugdje osim na /korisnik/logout/?
            if (authenticated) {
                keycloak.loadUserProfile().then(function (profile) {
                    console.debug('keycloak authenticated 2, tokenParsed.exp', keycloak.tokenParsed.exp);
                }).catch(function () {
                    console.debug('keycloak - failed to load user profile');
                });
            } else {
                keycloak_user_logout(keycloak);
            }
            <?php endif; ?>
        }).catch(function () {
            console.debug('keycloak - failed to initialize');
        });

        keycloak.onTokenExpired = () => {
            keycloak_update_token(keycloak);
        }

        keycloak.onAuthLogout = () => {
            keycloak_user_logout(keycloak);
        }

        function keycloak_user_login(keycloak) {
            jQuery.ajax({
                url: '/api/auth/login/',
                data: {'jwtToken': keycloak.idToken, 'tokenParsed': keycloak.tokenParsed},
                dataType: 'json',
                cache: false,
                method: 'POST',
                type: 'POST', // For jQuery < 1.9
                async: false,
                success: function (data) {
                    console.debug('response_data', data);
                    if (data.response == 'ok') {
                        location.reload();
                    }
                }
            });
        }

        function keycloak_user_logout(keycloak) {
            jQuery.ajax({
                url: '/api/auth/logout/',
                data: '',
                cache: false,
                method: 'POST',
                type: 'POST', // For jQuery < 1.9
                async: false,
                success: function (data) {
                    location.reload();
                }
            });
        }

        function keycloak_update_token(keycloak) {
            console.log('token expired, called update');
            //Update the token when will last less than 50 seconds
            keycloak.updateToken(50).then(function(refreshed) {
                if (refreshed) {
                    console.log('refreshed ' + new Date());
                } else {
                    console.log('not refreshed ' + new Date());
                    // keycloak_user_logout(keycloak);
                }
            }).catch(function() {
                console.debug('keycloak - failed to updateToken');
            });
        }
    </script>
<?php endif; ?>