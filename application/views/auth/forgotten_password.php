<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('benefits_class'); ?> special<?php $this->endblock('benefits_class'); ?>

<?php $this->block('main'); ?>
<div class="auth-box">
	<div class="wrapper">
		<div class="forgotten_password-container">
			<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
			<?php echo Arr::get($cms_page, 'content'); ?>

			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-error global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success'): ?>
				<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="forgotten_password" class="ajax_siteform form-animated-label auth-form auth-forgotten-password-form">
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<fieldset>
						<p class="field">
							<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
							<input type="text" name="email" id="id_email" />
							<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
						</p>
					</fieldset>
					<div class="submit-container">
						<p class="submit"><button class="submit-btn btn-lightBlue btn-medium" type="submit"><?php echo Arr::get($cmslabel, 'send'); ?></button></p>
						<p class="auth-links">
							<a class="button2 back" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'back_to_login'); ?></span></a>
							<a class="button2 signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'add_new_account'); ?></span></a>
						</p>
					</div>
				</form>
			<?php endif; ?>
		</div>
	</div>
</div>
<?php $this->endblock('main'); ?>

<?php $this->block('nw'); ?> <?php $this->endblock('nw'); ?>