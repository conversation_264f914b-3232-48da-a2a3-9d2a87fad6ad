<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo $item['title']; ?> od <?php echo trim($item['user_first_name'].' '.$item['user_last_name']); ?><?php $this->endblock('title'); ?>
<?php $this->block('page_class'); ?> page-wishlist<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
<div class="<?php if ($user): ?>auth-section <?php else: ?>wishlist-row <?php endif; ?>wrapper">
	<?php if (!$user): ?><div class="ci-categories wishlist-categories"><?php endif; ?>
		<?php echo View::factory('catalog/widget/wishlist_categories'); ?>
	<?php if (!$user): ?></div><?php endif; ?>
	<?php if ($item['total_items'] > 0): ?>
		<div class="wishlist-section<?php if ($user): ?> auth-content<?php endif; ?>" id="view_wishlist">
			<div class="wishlist-title-container<?php if ($user): ?> auth-wishlist-title-container<?php endif; ?>">
				<h1 class="<?php if ($user): ?>auth-main-title <?php if ($item['total_items'] > 0): ?>auth-main-title-special<?php endif; ?><?php else: ?>wishlist-title<?php endif; ?>">
					<?php if($user): ?>
						<span><?php echo Arr::get($cmslabel, 'auth_wishlist_title'); ?></span>
					<?php else: ?>
						<span><?php echo Arr::get($cmslabel, 'wishlist_products_title'); ?></span>
					<?php endif; ?>
					<span class="<?php if ($user): ?>auth-title-counter<?php else: ?>wishlist-detail-counter<?php endif; ?> wishlist_count">(<?php echo $item['total_items']; ?>)</span>
				</h1>	
				<?php if (!$user): ?>
					<div class="m-wishlist-btn btn btn-white btn-mobile"><span><?php echo Arr::get($cmslabel, 'm_categories'); ?></span></div>	
				<?php endif; ?>
				
				<?php if ($item['total_items'] > 0): ?>
					<a class="btn btn-white btn-wishlist-delete btn-wishlist-delete-top" href="<?php echo $wishlist['url_delete']; ?>"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>				
				<?php endif; ?>			 	
			</div>
			<div class="wrapper-big">
				<div class="cart-wishlist-items wishlist-detail-items<?php if (!$user): ?> auth-wishlist-detail-items<?php endif; ?>">
					<?php if($info['user_device'] == 'm'): ?>
						<?php echo View::factory('catalog/index_entry', array('items' => $item['items'], 'mode' => 'wishlist')); ?>
					<?php else: ?>
						<?php echo View::factory('catalog/index_entry_list', array('items' => $item['items'], 'mode' => 'wishlist')); ?>
					<?php endif; ?>

					<div class="btn-wishlist-section">
						<a class="btn btn-white btn-wishlist-delete btn-wishlist-delete-bottom" href="<?php echo $wishlist['url_delete']; ?>"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
					</div>
				</div>
			</div>
		</div>
		<div class="wishlist-empty" id="empty_wishlist" style="display: none;">
			<div class="title-container cart-title-container wishlist-title-container<?php if ($user): ?> auth-wishlist-title-container<?php endif; ?>">
				<div class="wrapper-big wishlist-tc-wrapper">
					<h1 class="<?php if ($user): ?>auth-main-title<?php else: ?>wishlist-title<?php endif; ?>">
						<?php if($user): ?>
							<span><?php echo Arr::get($cmslabel, 'auth_wishlist_title'); ?></span>
						<?php else: ?>
							<span><?php echo Arr::get($cmslabel, 'wishlist_products_title'); ?></span>
						<?php endif; ?>
						<span class="<?php if ($user): ?>auth-title-counter<?php else: ?>wishlist-detail-counter<?php endif; ?> wishlist_count">(<?php echo $item['total_items']; ?>)</span>
					</h1>
					<?php if (!$user): ?>
						<div class="m-wishlist-btn btn btn-white btn-mobile"><span><?php echo Arr::get($cmslabel, 'm_categories'); ?></span></div>	
					<?php endif; ?>
				</div>
			</div>
			<div class="cart-wishlist-items">
				<p class="wishlist-empty-desc"><?php echo Arr::get($cmslabel, 'no_wishlists'); ?></p>	
			</div>
		</div>
	<?php else: ?>
		<div class="wishlist-section<?php if ($user): ?> auth-content<?php endif; ?>">
			<h1 class="<?php if ($user): ?>auth-main-title<?php else: ?>wishlist-title<?php endif; ?> wishlist-title-empty">
				<?php if($user): ?>
					<span><?php echo Arr::get($cmslabel, 'auth_wishlist_title'); ?></span>
				<?php else: ?>
					<span><?php echo Arr::get($cmslabel, 'wishlist_products_title'); ?></span>
				<?php endif; ?>
				<span class="<?php if ($user): ?>auth-title-counter<?php else: ?>wishlist-detail-counter<?php endif; ?> wishlist_count">(<?php echo $item['total_items']; ?>)</span>
			</h1>	
			<p class="wishlist-empty-desc"><?php echo Arr::get($cmslabel, 'no_wishlists'); ?></p>
		</div>
	<?php endif; ?>
</div>
<?php $this->endblock('content_layout'); ?>