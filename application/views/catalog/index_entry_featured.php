<?php
$mode = (isset($mode)) ? $mode : '';
$product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities');
$i = (!empty($pagination)) ? (($pagination->current_page - 1) * $pagination->items_per_page) + 1 : 1;

// coupon
list($list_coupons, $item_coupon_ids) = Catalog::listitems_coupon(array_keys($items));
$is_loyalty = (!empty($user->loyalty_code));
?>

<?php foreach ($items as $item): ?>
    <?php
    $availability_status = $item['availability_info']['status'] ?? null;
    $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : ''));
    $badges_special = (!empty($item['badges'])) ? $item['badges'] : [];
    $badges_special_1 = [];

    if (!in_array($item['type'], ['advanced', 'configurable']) AND ((!empty($priority['code']) AND $item['discount_percent_custom'] >= 2) OR $item['discount_percent_custom'] >= 2 OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']))) {
        $badges_special_1[0]['category'] = '0';
    }

    $badges_special_2 = [];
    if (!empty($badges_special)) {
        foreach ($badges_special AS $badge_special_id => $badge_special_data) {
            if ((int)$badge_special_data['category'] <= 5) {
                if ((int)$badge_special_data['category'] == 5 AND !empty($priority['code']) AND $priority['code'] == 'new') {
                    $badges_special_1[4]['category'] = '4';
                }

                if ($badge_special_data['category'] == 1) {
                    // preorder
                    if (!empty($item['status']) AND !empty($item['date_available']) AND $item['status'] == '5') {
                        $badges_special_1[$badge_special_id] = $badge_special_data;
                    }
                } else if ($badge_special_data['category'] == 2) {
                    // coupon
                    if ($list_coupons) {
                        $coupon_ids = Arr::get($item_coupon_ids, $item['id']);
                        if (!empty($coupon_ids)) {
                            $coupon_ids = array_filter(explode(',', $coupon_ids));
                            $item_coupons = Arr::extract($list_coupons, $coupon_ids);
                            $item_coupon = reset($item_coupons);
                            $badge_special_data['coupon_code'] = $item_coupon['coupon_code'];
                            $badge_special_data['coupon_discount_percent'] = (int) $item_coupon['coupon_discount_percent'];
                            $badge_special_data['coupon_active_to'] = $item_coupon['coupon_active_to'];

                            $badges_special_1[$badge_special_id] = $badge_special_data;
                        }
                    }
                } else {
                    $badges_special_1[$badge_special_id] = $badge_special_data;
                }
            } else {
                $badges_special_2[$badge_special_id] = $badge_special_data;
            }
        }

        if (!empty($priority['code']) AND $priority['code'] == 'new' AND !empty($badges_special_1[4])) {
            $badges_special_1[4]['category'] = '4';
        }

        if (count($badges_special_1) > 2) {
            $badges_special_1 = array_slice($badges_special_1, 0, 2, true);
        }
        if (count($badges_special_2) > 3) {
            $badges_special_2 = array_slice($badges_special_2, 0, 3, true);
        }
    } else {
        if (!empty($priority['code']) AND $priority['code'] == 'new') {
            $badges_special_1[4]['category'] = '4';
        }
    }

    $badge_uau = (Kohana::$environment === 1) ? 447785 : 1299393;
    $badge_uau_exist = false;

    if(!empty($item['badges'])) {
        foreach ($item['badges'] as $badge) {
            if ($badge['code'] == $badge_uau) {
                $badge_uau_exist = true;
                break;
            }
        }
    }

    $priceRecommended = false;
    if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))) {
        $priceRecommended = true;
    }
    ?>

    <article class="clear cpf" data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo $mode; ?>">
        <span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
        <span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
        <span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'title')); ?></span>
        <span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'code'); ?></span>
        <?php if ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']): ?>
            <span style="display: none;" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
            <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
            <span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">loyalty</span>
        <?php else: ?>
            <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
            <?php if (!in_array($item['type'], ['advanced', 'configurable']) AND ((!empty($item['discount_percent']) AND $item['discount_percent'] > 0) OR $item['price_custom'] < $item['basic_price'])): ?>
                <span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">action</span>
            <?php endif; ?>
        <?php endif; ?>

        <div class="cpf-top">
            <div class="cpf-image-container">
                <figure class="cpf-image<?php if($mode != 'menu'): ?> lloader<?php endif; ?>">
                    <a href="<?php echo $item['url']; ?>">
                        <?php if($mode == 'menu'): ?>
                            <img <?php echo Thumb::generate(Arr::get($item, 'main_image'), array('width' => 90, 'height' => 90, 'default_image' => '/media/images/no-image-50.webp', 'html_tag' => true)); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
                        <?php else: ?>
                            <img data-lazy="<?php echo Thumb::generate(Arr::get($item, 'main_image'), 120, 120, false, 'thumb', TRUE, '/media/images/no-image-120.webp'); ?>" <?php echo Thumb::generate(Arr::get($item, 'main_image'), array('width' => 120, 'height' => 120, 'default_image' => '/media/images/no-image-120.webp', 'placeholder' => '/media/images/no-image-120.webp', 'srcset' => '240c 2x')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" />
                        <?php endif; ?>
                    </a>
                </figure>

                <?php if(!isset($badges) OR !empty($badges_special)): ?>
                    <?php if (!empty($badges_special_1)): ?>
                        <?php foreach($badges_special_1 as $badge_special): ?>
                            <?php if ($badge_special['category'] == 0 AND $priceRecommended == false): ?>
                                <div class="cpf-badge cpf-badge-discount<?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom']): ?> cpf-badge-discount-loyalty<?php endif; ?>">
                                    <?php
                                    $discount_percent = $item['discount_percent_custom'];
                                    $price_saved = ($item['basic_price_custom'] - $item['price_custom']);
                                    if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom'])) {
                                        $discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['price_custom'])) * 100, 0);
                                        $price_saved = ($item['price_custom'] - $item['loyalty_price_custom']);
                                    } else if (($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom'])) {
                                        $discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['basic_price_custom'])) * 100, 0);
                                        $price_saved = ($item['basic_price_custom'] - $item['loyalty_price_custom']);
                                    }
                                    ?>
                                    <span>-<?php echo $discount_percent; ?> %</span>
                                    <div class="cpf-badge-tooltip cpf-badge-tooltip-discount"><?php echo Arr::get($cmslabel, 'prihranek'); ?> <strong><?php echo Utils::currency_format($price_saved * $currency['exchange'], $currency['display']); ?></strong></div>
                                </div>
                            <?php elseif ($badge_special['category'] == 4): ?>
                                <div class="cpf-badge cpf-badge-new">
                                    <span><?php echo $priority['title']; ?></span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>

                <?php $attributes_special = $item['attributes_special']; ?>
                <?php if(!empty($item['attributes_special'])): ?>
                    <?php
                    $attr_title = [];
                    $attr_img = [];
                    foreach($item['attributes_special'] as $attr) {
                        if(in_array($attr['attribute_code'], ['razred-energijske-u-100215480'])){
                            $attr_title[0] = $attr['title'];
                            $attr_img[0] = $attr['image'];
                        }
                        if (in_array($attr['attribute_code'], ['razred-energijske-u-100176542'])) {
                            $attr_title[1] = $attr['title'];
                            $attr_img[1] = $attr['image'];
                        }
                        if (in_array($attr['attribute_code'], ['razred-energij-ucinkov-35'])) {
                            $attr_title[3] = $attr['title'];
                            $attr_img[3] = $attr['image'];
                        }
                    }
                    ?>

                    <?php if(!empty($attr_img) OR !empty($attr_title)): ?>
                        <div class="cpf-energy">
                            <?php
                            ksort($attr_img);
                            ksort($attr_title);
                            $attr_img = reset($attr_img);
                            $attr_title = reset($attr_title);
                            ?>
                            <?php if(!empty($attr_img)): ?>
                                <img loading="lazy" width="80" height="22" src="<?php echo Utils::file_url($attr_img); ?>" alt="<?php echo $attr_title; ?>">
                            <?php endif; ?>
                            <?php if(!empty($attr_title) AND empty($attr_img)): ?>
                                <?php echo $attr_title; ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <div class="clear cpf-cnt">
                <div class="cpf-info">
                    <div class="cp-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>
                    <?php if(!empty($item['category_title']) AND $mode != 'menu'): ?>
                        <div class="cpf-category"><a href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a></div>
                    <?php endif; ?>
                    <h2 class="cpf-title">
                        <a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>">
                            <?php echo $item['title']; ?>
                        </a>
                    </h2>
                    <div class="cpf-cnt-comment">
                        <!-- Rating -->
                        <?php if (isset($item['feedback_rate_widget'])): ?>
                            <?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
                        <?php endif; ?>
                        <!-- Comments -->
                        <?php if (isset($item['feedback_comment_widget'])): ?>
                            <div class="cp-comment-count comments-count catalog-comment-count">
								<span title="<?php echo Arr::get($cmslabel, 'comments_num'); ?>">
									(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
								</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($item['price_custom'] > 0): ?>
                    <div class="cpf-price<?php if(!empty($badge_uau_exist)): ?> uau-badge<?php endif; ?>">
                        <?php if (in_array($item['type'], ['advanced', 'configurable']) AND $item['basic_price_custom'] > $item['price_custom']): ?>
                            <div class="cpf-old-price cpf-price-label"><?php echo Arr::get($cmslabel, 'price_variation', 'Od'); ?></div>
                            <div class="cpf-current-price cp-variation-price">
                                <div data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                            </div>
                        <?php else: ?>
                            <?php
                            $installment_price = (!empty($item['installments_calculation']['regular']) ? $item['installments_calculation']['regular'] : 0);
                            if (is_array($installment_price)) {
                                $installment_price = reset($installment_price);
                            }
                            $installment_loyalty_price = (!empty($item['installments_calculation']['loyalty']) ? $item['installments_calculation']['loyalty'] : 0);
                            if (is_array($installment_loyalty_price)) {
                                $installment_loyalty_price = reset($installment_loyalty_price);
                            }
                            if (!empty($installment_loyalty_price) AND $is_loyalty) {
                                $installment_price = $installment_loyalty_price;
                            }

                            $installment_price = (!empty($installment_price) AND !empty($item['installments_calculation']['regular'])) ? Utils::currency_format($installment_price * $currency['exchange'], $currency['display']) : '';
                            // always remove installment price
                            $installment_price = '';
                            ?>

                            <?php if($priceRecommended == true): ?>
                                <div class="cpf-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($itinstallment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
                                <?php if(empty($item['loyalty_price_custom'])): ?>
                                    <div class="cpf-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                                <?php endif; ?>
                            <?php elseif (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
                                <div class="cpf-current-price cpf-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($itinstallment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
                                <?php if(empty($item['loyalty_price_custom']) OR ($is_loyalty AND !empty($item['loyalty_price_custom'] AND $item['loyalty_price_custom'] > $item['price_custom']))): ?>
                                    <div class="cpf-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                                <?php endif; ?>
                            <?php elseif ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']): ?>
                                <div class="cpf-current-price cp-discount-price blue" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($installment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
                                <div class="cpf-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
                                    <?php if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
                                        <?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
                                    <?php else: ?>
                                        <?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="cpf-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($installment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if(!$is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
                            <div class="cpf-loyalty-price"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if($info['user_device'] == 'm' && $mode != 'publish' AND $mode != 'menu'): ?>
                    <div class="cpf-available-qty">
                        <?php if (!empty($availability_status)): ?>
                            <?php if($item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
                                <span><?php echo Arr::get($cmslabel, 'na_zalogi_hitri_prevzem_cp'); ?></span>
                            <?php else: ?>
                                <?php if($availability_status == 1): ?>
                                    <?php if ($item['last_piece_sale'] AND !empty($item['warehouses_single_pickup_display'])): ?>
                                        <span class="available-last"><?php echo Arr::get($cmslabel, 'odprodaja'); ?></span>
                                    <?php else: ?>
                                        <span><?php echo Arr::get($cmslabel, 'na_zalogi'); ?></span>
                                    <?php endif; ?>
                                <?php elseif($availability_status == 2): ?>
                                    <span><?php echo str_replace(['%MIN_DAY%','%MAX_DAY%'], [$item['availability_info']['min_days'] ?? 0, $item['availability_info']['max_days'] ?? 0], Arr::get($cmslabel, 'na_zalogi_dobavitelja')); ?></span>
                                <?php elseif($availability_status == 4): ?>
                                    <span class="available-last"><?php echo Arr::get($cmslabel, 'na_zalogi_ena'); ?></span>
                                <?php elseif($availability_status == 5): ?>
                                    <?php if(!empty($item['is_available'])): ?>
                                        <span><?php echo Arr::get($cmslabel, 'na_voljo'); ?> <?php echo date('d.m.Y', $item['shipping_date']); ?></span>
                                    <?php else: ?>
                                        <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi_preorder'); ?></span>
                                    <?php endif; ?>
                                <?php elseif($availability_status == 7): ?>
                                    <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi'); ?></span>
                                <?php elseif($availability_status == 9): ?>
                                    <span class="unavailable"><?php echo Arr::get($cmslabel, 'dalj_ni_na_zalogi'); ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if (!empty($item['extra_price_cart_dynamicprice']) AND !empty((float)$item['extra_price_dynamicprice']) AND !empty('extra_price_mode_dynamicprice')): ?>
                            <?php if (!empty($cmslabel['iznenaci_ceno'])): ?>| <?php echo Arr::get($cmslabel, 'iznenaci_ceno'); ?><?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($mode == 'publish'): ?>
                <div class="cpf-btns">
                    <!-- Compare -->
                    <?php if (isset($item['compare_widget']) AND empty($compare_page)): ?>
                        <?php echo View::factory('catalog/widget/set_compare', $item['compare_widget']); ?>
                    <?php endif; ?>

                    <?php if ((in_array($availability_status, [1, 2]) OR ($availability_status == 5 AND !empty($item['is_available'])) AND empty($item['variation_total']))): ?>
                        <?php
                        $add_to_cart_label = 'add_to_shopping_cart';
                        $add_to_cart_status = 'addtocart';
                        if (in_array($item['type'], ['advanced', 'configurable'])) {
                            $add_to_cart_label = 'add_to_shopping_cart_configurable';
                            $add_to_cart_status = 'details';
                        } elseif ($item['status'] == '5') {
                            $add_to_cart_label = 'add_to_shopping_cart_preorder';
                            $add_to_cart_status = 'details';
                        }
                        ?>
                        <a class="btn btn-green cpf-btn-addtocart <?php echo $add_to_cart_status; ?>" title="<?php echo Arr::get($cmslabel, $add_to_cart_label); ?>" href="<?php if (in_array($item['type'], ['advanced', 'configurable'])): ?><?php echo $item['url']; ?><?php else: ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'simple', 3)<?php endif; ?>">
                            <span><?php echo Arr::get($cmslabel, $add_to_cart_label); ?></span>
                        </a>
                    <?php else: ?>
                        <a class="btn btn-green cpf-btn-addtocart cpf-btn-details" title="<?php echo Arr::get($cmslabel, 'read_more'); ?>" href="<?php echo $item['url']; ?>">
                            <span><?php echo Arr::get($cmslabel, 'read_more'); ?></span>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <?php if($info['user_device'] != 'm' AND $mode != 'publish' AND $mode != 'menu'): ?>
            <div class="cpf-bottom">
                <div class="cpf-available-qty">
                    <?php if (!empty($availability_status)): ?>
                        <?php if($item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
                            <span><?php echo Arr::get($cmslabel, 'na_zalogi_hitri_prevzem_cp'); ?></span>
                        <?php else: ?>
                            <?php if($availability_status == 1): ?>
                                <?php if ($item['last_piece_sale'] AND !empty($item['warehouses_single_pickup_display'])): ?>
                                    <span class="available-last"><?php echo Arr::get($cmslabel, 'odprodaja'); ?></span>
                                <?php else: ?>
                                    <span><?php echo Arr::get($cmslabel, 'na_zalogi'); ?></span>
                                <?php endif; ?>
                            <?php elseif($availability_status == 2): ?>
                                <span><?php echo str_replace(['%MIN_DAY%','%MAX_DAY%'], [$item['availability_info']['min_days'] ?? 0, $item['availability_info']['max_days'] ?? 0], Arr::get($cmslabel, 'na_zalogi_dobavitelja')); ?></span>
                            <?php elseif($availability_status == 4): ?>
                                <span class="available-last"><?php echo Arr::get($cmslabel, 'na_zalogi_ena'); ?></span>
                            <?php elseif($availability_status == 5): ?>
                                <?php if(!empty($item['is_available'])): ?>
                                    <span><?php echo Arr::get($cmslabel, 'na_voljo'); ?> <?php echo date('d.m.Y', $item['shipping_date']); ?></span>
                                <?php else: ?>
                                    <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi_preorder'); ?></span>
                                <?php endif; ?>
                            <?php elseif($availability_status == 7): ?>
                                <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi'); ?></span>
                            <?php elseif($availability_status == 9): ?>
                                <span class="unavailable"><?php echo Arr::get($cmslabel, 'dalj_ni_na_zalogi'); ?></span>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if (!empty($item['extra_price_cart_dynamicprice']) AND !empty((float)$item['extra_price_dynamicprice']) AND !empty('extra_price_mode_dynamicprice')): ?>
                        <?php if (!empty($cmslabel['iznenaci_ceno'])): ?>| <?php echo Arr::get($cmslabel, 'iznenaci_ceno'); ?><?php endif; ?>
                    <?php endif; ?>
                </div>

                <?php if (!empty($item['seller_id'])): ?>
					<div class="cpf-seller">
                        <?php if(!empty($item['seller_corporate_name'])): ?>
                            <span><?php echo Arr::get($cmslabel, 'seller_item_title'); ?> <a href="<?php echo $item['seller_url']; ?>"><?php echo $item['seller_corporate_name']; ?></a></span>
                        <?php endif; ?>
                        <?php if(!empty($item['seller_title'])): ?>
                            <span class="extra-name">(<?php echo $item['seller_title']; ?>)</span>
                        <?php endif; ?>
                    </div>
				<?php endif; ?>
            </div>
        <?php endif; ?>
    </article>
    <?php $i++; ?>
<?php endforeach; ?>