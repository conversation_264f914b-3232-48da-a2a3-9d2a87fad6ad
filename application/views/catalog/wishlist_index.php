<?php $this->extend('auth/default'); ?>
<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('page_class'); ?> page-wishlist<?php $this->endblock('page_class'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>
<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('content2'); ?>
	<?php if (!$user): ?>
	<div class="wishlist-row">
		<div class="wishlist-section">
	<?php endif; ?>
			<?php $this->block('auth_title'); ?> <?php $this->endblock('auth_title'); ?>
			<h1 class="<?php if ($user): ?>auth-main-title auth-main-title-special<?php else: ?>wishlist-title<?php endif; ?>">
				<?php if($user): ?>
					<span><?php echo Arr::get($cmslabel, 'auth_wishlist_title'); ?></span>
				<?php else: ?>
					<span><?php echo Arr::get($cmslabel, 'wishlist_products_title'); ?></span>
				<?php endif; ?>
			</h1>		
			<p class="wishlist-empty-desc wrapper-big"><?php echo Arr::get($cmslabel, 'no_wishlists'); ?></p>
	<?php if (!$user): ?>
		</div>
	</div>
	<?php endif; ?>
<?php $this->endblock('content2'); ?>