<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] == 3): ?>
    <?php $cat_position = Utils::get_all_parents($kind['position_h'], '.', 2); ?>
    <?php $categories_special = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $cat_position, 'level_range' => '1.3']); ?>
<?php else: ?>
    <?php $cat_position = !empty($kind['parents']) ? reset($kind['parents'])['position_h'] : $kind['position_h']; ?>
    <?php $categories_special = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $kind['position_h'], 'level_range' => '3.3']); ?>
<?php endif; ?>
<?php if(!empty($categories_special)): ?>
	<?php $curr_cat = Utils::extract_segments($info['lang'], $info['basic_url'], 3, 0, TRUE); ?>
    <ul class="c-categories-m" data-current_url="<?php echo $curr_cat; ?>">
        <?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] == 2): ?>
            <li class="c-category-m active">
                <a href="<?php echo $curr_cat; ?>"><?php echo Arr::get($cmslabel, 'category_all_items'); ?></a>
            </li>
        <?php endif; ?>
        <?php foreach($categories_special as $category): ?>
            <li class="c-category-m<?php if($category['url'] == $info['url']): ?> active<?php endif; ?>">
                <a href="<?php echo $category['url']; ?>"><?php echo $category['title']; ?></a>
            </li>
        <?php endforeach; ?>
    </ul>
<?php endif; ?>