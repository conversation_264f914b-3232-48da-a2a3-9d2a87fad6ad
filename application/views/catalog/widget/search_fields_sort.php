<?php if ($items_total > 1 OR !empty($active_filters)): ?>
    <!-- Sort options -->
    <div class="sort c-sort">
        <?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort', null, '', false); ?>
        <?php $sort_base_url .= ($sort_base_url) ? '&' : '?'; ?>
        <?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
        <select name="sort" data-livesearch_direct="select">
            <option value="0"><?php echo Arr::get($cmslabel, 'ordering_priority', 'Priority'); ?></option>
            <option value="fastest_first"<?php if ($selected_sort == 'fastest_first'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_fastest_first', 'Najbrže dostupno'); ?></option>
            <option value="rates"<?php if ($selected_sort == 'rates'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_top_rated', 'Top rated'); ?></option>
            <option value="new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option>
            <option value="discount_priority"<?php if ($selected_sort == 'discount_priority'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'discount_priority', 'Višji popust naprej'); ?></option>
            <option value="expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
            <option value="cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
        </select>
    </div>
<?php endif; ?>