<!-- Filter -->
<?php if (!empty($search_fields['_basic']['available_fields'])): ?>
    <div class="cf">
        <div class="cf-header">
            <div class="cf-title"><?php echo Arr::get($cmslabel, 'filters'); ?></div>
            <a data-cmsfilter_cancel_url="1" href="?<?php echo $info['url_query_string']; ?>" class="cf-header-close" style="display:none;"></a>
            <div class="cf-filter-toggle">
                <span class="more"><?php echo Arr::get($cmslabel, 'catalog_filter_open_all'); ?></span>
                <span class="less"><?php echo Arr::get($cmslabel, 'catalog_filter_close_all'); ?></span>
            </div>
        </div>
        <form class="cf-form" action="" method="get" id="attribute_filters_select" data-search_seofriendly_basic_full_url="<?php echo $info['search_seofriendly_basic_full_url']; ?>" data-search_seofriendly_query_string="<?php echo urlencode($info['url_query_string']); ?>">
            <input type="hidden" name="search_q" value="<?php echo Arr::get($_GET, 'search_q', ''); ?>" />
            <?php echo $search_fields['_basic']['field']; ?>
            <?php foreach ($search_fields AS $search_filter_field => $search_filter): ?>
                <?php $options = Arr::get($search_filter, 'options_details'); ?>
                <?php $template = Arr::get($search_filter, 'template'); ?>
                <?php if (!$options AND $search_filter_field !== 'categories' OR (isset($search_filter['options_total']) AND $search_filter['options_total_items'] == 0)) {continue;} ?>
                <?php if ($search_filter_field == 'categories' AND $kind_content != 'manufacturer' AND !$q): ?>
                    <?php $categories_tree = Widget_Catalog::categories(['lang' => $info['lang'], 'generate_tree' => true]); ?>
                    <?php echo $categories_tree; ?>
                <?php else: ?>
                    <?php if (!$options AND $search_filter_field == 'categories' OR (isset($search_filter['options_total']) AND $search_filter['options_total_items'] == 0)) {continue;} ?>
                    <?php if ($search_filter_field == 'location'): ?>
                        <?php $options_total = count($options); ?>
                        <div style="display: none;">
                        <?php foreach ($options AS $option_id => $option): ?>
                            <?php if ( ! $option_id) {$options_total--; continue;} ?>
                            <!-- moved out of form -->
                            <div data-filteritem_active="<?php echo $option['unique_code']; ?>" class="cf-row <?php if($option['total_available'] <= 0): ?> cf-row-not-available<?php endif; ?>">
                                <input
                                    type="checkbox"
                                    id="search-<?php echo $search_filter_field; ?>-<?php echo $option['unique_code']; ?>"
                                    name="<?php echo $search_filter_field; ?>"
                                    value="<?php echo $option_id; ?>"
                                    data-filteritem_field="<?php echo $option['unique_code']; ?>"
                                    data-name_seo="<?php echo Arr::get($search_filter, 'slug'); ?>"
                                    data-name_ignore_seo="<?php echo Arr::get($search_filter, 'ignore_slug'); ?>"
                                    data-value_seo="<?php echo Arr::get($option, 'slug'); ?>"
                                    <?php if($option['total'] <= 0): ?> disabled<?php endif; ?>
                                    <?php if ($option['selected']): ?> checked<?php endif; ?>
                                    <?php if (Arr::get($search_filter, 'element_multivalue')): ?> data-element_multivalue="1"<?php endif; ?>
                                >
                                <label for="search-<?php echo $search_filter_field; ?>-<?php echo $option['unique_code']; ?>"><?php echo $option['title']; ?>
                                    <span data-filteritem_total="<?php echo $option['unique_code']; ?>" class="cf-counter ci-counter"><?php if($option['total_available'] > 0): ?>(<?php echo $option['total_available']; ?>)<?php endif; ?></span>
                                </label>
                            </div>
                        <?php endforeach; ?>
                        </div>
                        <?php continue; ?>
                    <?php endif; ?>
                    <div class="cf-item cf-item-<?php echo $search_filter_field; ?><?php if($kind_content == 'manufacturer' OR $q): ?> cf-item-notitle<?php endif; ?><?php if(!empty($search_filter['options_total_selected']) AND $search_filter['options_total_selected'] > 0): ?> active<?php endif; ?>">

                        <!-- Filter title  -->
                        <?php if($search_filter_field != 'categories'): ?>
                            <div class="cf-item-title" onClick="toggleBox('.cf-item-<?php echo $search_filter_field; ?>');">
                                <?php $filter_title = Arr::get($cmslabel, 'f_filter_'.$search_filter_field); ?>
                                <?php if($filter_title): ?>
                                    <?php echo $filter_title; ?>
                                <?php else: ?>
                                    <?php echo Arr::get($search_filter, 'label', Arr::get($cmslabel, $search_filter_field, $search_filter_field)); ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="cf-item-wrapper<?php if($search_filter['layout'] == 'sl' OR $search_filter['layout'] == 'sf'): ?> special<?php endif; ?>">
                            <?php if($search_filter['layout'] == 'sl' OR $search_filter['layout'] == 'sf'): ?>
                                <div class="cf-row cf-row-<?php echo $search_filter_field; ?>">
                                    <div class="cf-range">
                                        <?php echo $search_filter['field']; ?>
                                        <?php if($info['user_device'] != 'm'): ?>
                                            <a href="javascript:void(0);" data-cmsfilter_slider_submit="<?php echo $search_filter_field; ?>" class="btn btn-lightBlue cf-range-btn"><?php echo Arr::get($cmslabel, 'confirm_filters'); ?></a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <?php
                                foreach ($options AS $option_id => $option) {
                                    if (!$option_id OR ($search_filter_field == 'category' AND $option['level'] != '3')) {
                                        unset($options[$option_id]);
                                        continue;
                                    }
                                    if ($search_filter_field == 'category' AND $option['total'] <= 0) {
                                        unset($options[$option_id]);
                                        continue;
                                    }
                                    if ($search_filter_field == 'category') {
                                        $options[$option_id]['level'] = 1;
                                    }
                                }

                                $options_total = count($options);
                                $i = 1;
                                $visible_rows = 9;
                                ?>
                                <?php foreach ($options AS $option_id => $option): ?>
                                    <?php if($options_total >= $visible_rows AND $i == $visible_rows): ?><div class="cf-row-expand cf-row-expand-<?php echo $search_filter_field; ?>"><?php endif; ?>
                                    <?php if ($search_filter_field == 'category' AND $option['total'] <= 0): ?>
                                        <?php if($options_total >= $visible_rows AND $i == $options_total): ?></div><a class="btn-cf-expand btn-cf-expand-<?php echo $search_filter_field; ?> active" href="javascript:toggleBox(['.cf-row-expand-<?php echo $search_filter_field; ?>', '.btn-cf-expand-<?php echo $search_filter_field; ?>']);">
                                            <span class="more"><?php echo Arr::get($cmslabel, 'show_all'); ?></span>
                                            <span class="less"><?php echo Arr::get($cmslabel, 'show_less'); ?></span>
                                        </a>
                                        <?php endif; ?>
                                        <?php $i++; ?>
                                        <?php continue; ?>
                                    <?php endif; ?>
                                    <div data-filteritem_active="<?php echo $option['unique_code']; ?>" class="cf-row<?php if(!empty($option['level'])): ?> cf-row-level<?php echo $option['level']; ?><?php endif; ?><?php if($option['total_available'] <= 0): ?> cf-row-not-available<?php endif; ?>">
                                        <?php if(!empty($option['level']) AND $option['level'] > 1 OR $search_filter_field != 'categories'): ?>
                                            <input<?php if($option['total'] <= 0): ?> disabled<?php endif; ?> type="checkbox" id="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>" name="<?php echo $search_filter_field; ?>" value="<?php echo $option_id; ?>"
                                            data-filteritem_field="<?php echo $option['unique_code']; ?>"
                                            data-name_seo="<?php echo Arr::get($search_filter, 'slug'); ?>" data-name_ignore_seo="<?php echo Arr::get($search_filter, 'ignore_slug'); ?>" data-value_seo="<?php echo Arr::get($option, 'slug'); ?>"
                                                <?php if ($option['selected']): ?> checked<?php endif; ?><?php if (Arr::get($search_filter, 'element_multivalue')): ?> data-element_multivalue="1"<?php endif; ?><?php if ($search_filter_field == 'category' AND !empty($option['position'])): ?> data-filter_hierarhy_position="<?php echo $option['position']; ?>"<?php endif; ?> <?php if(count(explode('.', Arr::get($option, 'position'))) == 2): ?> data-filter_category_parent="1"<?php endif; ?>
                                            <?php if($search_filter_field == 'category' AND !empty(Arr::get($option, 'total'))): ?>data-total_items="<?php echo $option['total']; ?>"<?php endif; ?>>
                                        <?php endif; ?>
                                        <label class="<?php if($search_filter_field == 'a_barva-48' OR $search_filter_field == 'a_barva-pim-6698538'): ?>cf-label-color<?php endif; ?>" for="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>"><?php echo $option['title']; ?>
                                            <?php if($search_filter_field == 'a_barva-48' OR $search_filter_field == 'a_barva-pim-6698538'): ?>
                                                <span class="cf-color-img">
                                                    <img loading="lazy" <?php echo Thumb::generate($option['image'], ['width' => 20, 'height' => 20, 'crop' => true, 'default_image' => '/media/images/no-color.jpg', 'html_tag' => true, 'srcset' => '40c 2x']); ?> alt="" />
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($search_filter_field == 'category'): ?>
                                                <span data-filteritem_total="<?php echo $option['unique_code']; ?>" class="cf-counter ci-counter" <?php if(count(explode('.', Arr::get($option, 'position'))) == 2): ?> id="filter_category_parent-<?php echo $option['position']; ?>"<?php endif; ?>><?php if($option['total_available'] > 0): ?>(<?php echo $option['total_available']; ?>)<?php endif; ?></span>
                                            <?php else: ?>
                                                <span data-filteritem_total="<?php echo $option['unique_code']; ?>" class="cf-counter ci-counter"><?php if($option['total_available'] > 0): ?>(<?php echo $option['total_available']; ?>)<?php endif; ?></span>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                    <?php if($options_total >= $visible_rows AND $i == $options_total): ?>
                                        </div><a class="btn-cf-expand btn-cf-expand-<?php echo $search_filter_field; ?> active" href="javascript:toggleBox(['.cf-row-expand-<?php echo $search_filter_field; ?>', '.btn-cf-expand-<?php echo $search_filter_field; ?>']);">
                                            <span class="more"><?php echo Arr::get($cmslabel, 'show_all'); ?></span>
                                            <span class="less"><?php echo Arr::get($cmslabel, 'show_less'); ?></span>
                                        </a>
                                    <?php endif; ?>
                                    <?php $i++; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>

            <?php if (!empty($search_fields['discount']['field'])): ?>
                <div class="cf-item cf-item-sale">
                    <?php echo $search_fields['discount']['field']; ?> <label for="searchfield-discount"><?php echo Arr::get($cmslabel, 'sale_item'); ?></label>
                </div>
            <?php endif; ?>

            <?php if (!empty($search_fields['badges']['field'])): ?>
                <div class="cf-item cf-item-badges">
                    <?php echo $search_fields['badges']['field']; ?> <label for="searchfield-badges"><?php echo Arr::get($cmslabel, 'badges_447785'); ?></label>
                </div>
            <?php endif; ?>

            <div class="cf-btns<?php if(!empty($active_filters)): ?> active<?php endif; ?>">
                <?php $remove_all = '?' . (!empty($_GET['sort']) ? 'sort=' . $_GET['sort'] : ''); ?>
                <a data-filter_remove_all="0" data-livesearch_direct="none" href="<?php echo $remove_all; ?>" class="clear-filters cf-btn-clear">
                    <span><?php echo Arr::get($cmslabel, 'clear_filtering_btn'); ?></span>
                </a>
                <button class="confirm-filters cf-btn-confirm" data-cmsfilter_manual_submit="1" data-cmsfilter_element="1" data-cmsfilter_close_element=".cf-header-close" type="submit">
                    <span data-cmsfilter_manual_submit_label_empty="<?php echo Arr::get($cmslabel, 'confirm_filters_0'); ?>" data-cmsfilter_manual_submit_label_exist="<?php echo Arr::get($cmslabel, 'confirm_filters_1'); ?>" ><?php if (!empty($active_filters)): ?><?php echo Arr::get($cmslabel, 'confirm_filters_1'); ?><?php else: ?><?php echo Arr::get($cmslabel, 'confirm_filters_0'); ?><?php endif; ?></span>
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>