<?php if (!empty($item['type']) AND $item['type'] == 'advanced' AND !empty($item["type_config"])): ?>
    <?php $item_type_advanced_selected = key($item["type_config"]); ?>
	<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
        <a class="btn btn-green cd-btn-add cd-flyout-btn-add" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item_variation['shopping_cart_code']; ?>', '_tracking:detail,_service_extra,_service_always_visible', 'simple_loader', 'input', 3)" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>			<?php if ($item['status'] == '5'): ?>
				<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
			<?php else: ?>
				<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart'); ?></span>
			<?php endif; ?>
		</a>
	<?php endforeach; ?>
<?php else: ?>
	<a class="btn btn-green cd-btn-add cd-flyout-btn-add" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:detail,_service_extra,_service_always_visible', 'simple_loader', 'input', 3)">
		<?php if ($item['status'] == '5'): ?>
			<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
		<?php else: ?>
			<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart'); ?></span>
		<?php endif; ?>
	</a>
<?php endif; ?>