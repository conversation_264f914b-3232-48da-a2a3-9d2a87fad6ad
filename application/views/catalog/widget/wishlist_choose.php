<?php $this->extend('default_quick'); ?>

<?php $this->block('content_layout'); ?>
<div id="quick-wishlist">
	<h1><?php echo Arr::get($cmslabel, 'wishlist_added'); ?></h1>
	<p>"<?php echo $shopping_cart_title; ?>"</h1>
	<p>
		<strong><?php echo Arr::get($cmslabel, 'code'); ?>:</strong> <?php echo $shopping_cart_code; ?><br>
		<strong><?php echo Arr::get($cmslabel, 'quantity'); ?>:</strong> <?php echo $qty; ?>
	</p>

	<?php if (count($wishlists)): ?>
		<p>
			<label><?php echo Arr::get($cmslabel, 'wishlist_choose_exist'); ?>:</label>
			<?php $wishlists_list = array_map(function($element){return $element['title'];}, $wishlists); ?>
			<?php echo Form::select('wishlist_id', $wishlists_list, $default_wishlist_id); ?>
		</p>
		<p class="clear"><a class="button5" href="javascript:cmswishlist.set('<?php echo $shopping_cart_code; ?>', '+', 'select', '', '_tracking:index');"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a></p>
		<p>
			<label><?php echo Arr::get($cmslabel, 'wishlist_or_create_new'); ?></label>
	<?php else: ?>
		<p>
			<label><?php echo Arr::get($cmslabel, 'wishlist_none'); ?> <?php echo Arr::get($cmslabel, 'wishlist_title_first'); ?></label>
	<?php endif; ?>

		<input type="text" name="wishlist_id_new" value="<?php echo Kohana::config('app.webshop.wishlist.titles.'.$info['lang']); ?>">
	</p>

	<p class="buttons clear">
		<a class="button5" href="javascript:cmswishlist.set('<?php echo $shopping_cart_code; ?>', '+', 'new', '', '_tracking:index')">
			<span><?php echo Arr::get($cmslabel, 'wishlist_create_and_add'); ?></span>
		</a>
	</p>

	<p class="align-right padding-b-0"><a class="cancel" href="javascript:void(0);" onClick="parent.$.fancybox.close();"><?php echo Arr::get($cmslabel, 'wishlist_cancel'); ?></a></p>
</div>
<?php $this->endblock('content_layout'); ?>