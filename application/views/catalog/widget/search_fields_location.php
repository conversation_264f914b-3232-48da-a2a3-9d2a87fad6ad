<?php
$search_field_location = [];
if (!empty($search_fields['location'])) {
    $search_field_location = $search_fields['location'];
}
?>

<?php if (!empty($search_field_location['options_details']) AND !empty($search_field_location['options_total_items'])): // OR options_total_available? ?>
    <?php
    $search_filter_field = 'location';
    $search_filter = $search_field_location;
    $options = Arr::get($search_field_location, 'options_details');
    ?>

    <div class="ci-available-qty">
        <div class="ci-available-qty-btn"><span><?php echo Arr::get($cmslabel, 'available_store_filter'); ?> <span class="qty" data-filteritem_options_total_selected="<?php echo $search_filter_field; ?>"><?php if ($search_field_location['options_total_selected']): ?>(<?php echo $search_field_location['options_total_selected']; ?>)<?php endif; ?></span></span></div>
        <div class="ci-available-qty-cnt">
            <?php $options_total = count($options); ?>
            <?php $i = 1; ?>
            <?php foreach ($options AS $option_id => $option): ?>
                <?php if ( ! $option_id) {$options_total--; continue;} ?>
                <p data-filteritem_active="<?php echo $option['unique_code']; ?>">
                    <input
                        type="checkbox"
                        id="searchoutform-<?php echo $search_filter_field; ?>-<?php echo $option['unique_code']; ?>"
                        name="<?php echo $search_filter_field; ?>"
                        value="<?php echo $option_id; ?>"
                        data-filteritem_field="<?php echo $option['unique_code']; ?>"
                        data-livesearch_direct="searchoutform"
                        <?php if($option['total'] <= 0): ?> disabled<?php endif; ?>
                        <?php if ($option['selected']): ?> checked<?php endif; ?>
                    >
                    <label class="" for="searchoutform-<?php echo $search_filter_field; ?>-<?php echo $option['unique_code']; ?>"><?php echo $option['title']; ?>
                        <span data-filteritem_total="<?php echo $option['unique_code']; ?>" class="cf-counter ci-counter"><?php if($option['total_available'] > 0): ?>(<?php echo $option['total_available']; ?>)<?php endif; ?></span>
                    </label>
                    <?php $i++; ?>
                </p>
                <?php $i++; ?>
            <?php endforeach; ?>


        </div>
    </div>
<?php endif; ?>
