<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php $class = (isset($class)) ? $class : 'cp-btn-compare'; ?>
<?php if($class != 'cd-btn-compare'): ?><div class="cp-compare-container<?php if($mode == 'cd_list'): ?> cd-btn-compare-container<?php endif; ?>"><?php endif; ?>
    <a class="cp-compare cp-btn compare<?php if($mode == 'cd_list'): ?> cd-btn-compare-list<?php endif; ?> compare_set_<?php echo $content; ?><?php if ($active): ?> compare_active<?php endif; ?>" href="javascript:cmscompare.set('<?php echo $content; ?>', '<?php if ($active): ?>remove<?php else: ?>+<?php endif; ?>', '', '_tracking:index');" data-compare_operation="+|remove" <?php if (empty($mode)): ?> data-compare_change_tooltip="1" title="<?php echo ($active) ? Arr::get($cmslabel, 'remove_compare_product') : Arr::get($cmslabel, 'add_compare_product'); ?>" <?php endif; ?>>
        <span>
            <small class="l1"><?php echo Arr::get($cmslabel, 'add_compare_product'); ?></small>
            <small class="l2"><?php echo Arr::get($cmslabel, 'remove_compare_product'); ?></small>
        </span>
        <span class="cp-compare-info<?php if($mode == 'cd_list'): ?> cd-compare-info<?php endif; ?> compare_message compare_message_<?php echo $content; ?>" style="display:none;"></span>
    </a>
<?php if($class != 'cd-btn-compare'): ?></div><?php endif; ?>