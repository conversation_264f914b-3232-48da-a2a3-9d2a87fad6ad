<!-- Selected filters -->
<div class="cf-active" data-filter_remove_box="1" <?php if (!$active_filters): ?>style="display: none;"<?php endif; ?>>
    <?php $af = 0; ?>
    <?php foreach ($active_filters AS $active_filter_code => $active_filter): ?>
        <a data-filter_remove="<?php echo $active_filter_code; ?>" data-livesearch_direct="remove" class="cf-active-item <?php echo $active_filter['attribute_field']; ?> <?php echo $active_filter_code; ?>" href="<?php echo $active_filter['remove_url']; ?>">
            <?php /*if($active_filter['attribute_field'] == 'rates'): ?>
                <span class="cp-rate rates-container add_rate" data-rates="<?php echo $active_filter['slug']; ?>" title="" data-rates_stars="1">
                    <span data-score="1" class="icon-star-empty"></span>
                    <span data-score="2" class="icon-star-empty"></span>
                    <span data-score="3" class="icon-star-empty"></span>
                    <span data-score="4" class="icon-star-empty"></span>
                    <span data-score="5" class="icon-star-empty"></span>
                </span>
            <?php endif;*/ ?>
            <span class="icon"><?php echo $active_filter['title']; ?></span>
        </a>
        <?php $af++; ?>
    <?php endforeach; ?>
    <a data-filter_remove_all="2" data-livesearch_direct="hide" href="<?php echo (!empty($reset_url)) ? $reset_url : '?'; ?>" class="btn cf-active-item btn-cf-active-clear" <?php if($af < 2): ?>style="display: none;"<?php endif; ?>><?php echo Arr::get($cmslabel, 'clear_filtering'); ?></a>
</div>
