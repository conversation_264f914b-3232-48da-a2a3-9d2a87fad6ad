<?php if(empty($kind)): ?>
	<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.2', 'mode' => 'widget', 'hierarhy_by_position' => true)); ?>
<?php else: ?>
	<?php $cat_position = !empty($kind['parents']) ? reset($kind['parents'])['position_h'] : $kind['position_h']; ?>
	<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $cat_position, 'level_range' => '2.2']); ?>
	<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] == 2): ?>
		<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $kind['position_h'], 'level_range' => '3.3']); ?>
	<?php endif; ?>
<?php endif; ?>
<?php if(!empty($categories)): ?>
	<div class="c-categories-wrapper<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1): ?> special<?php endif; ?>">
		<?php if(empty($kind) OR (!empty($kind) AND !empty($kind['level']) AND $kind['level'] < 2)): ?>
			<div class="<?php if(empty($kind)): ?>c-title c-categories-title<?php else: ?> c-subtitle<?php endif; ?>"><?php echo Arr::get($cmslabel, 'landing_categories_title'); ?></div>
		<?php endif; ?>
		<?php $curr_cat = Utils::extract_segments($info['lang'], $info['basic_url'], 3, 0, TRUE); ?>
		<ul class="c-categories" data-current_url="<?php echo $curr_cat; ?>">
			<?php $c = 0; ?>
			<?php foreach($categories as $category): ?>
				<li class="c-category">
					<a href="<?php echo $category['url']; ?>">
						<figure><img <?php echo Thumb::generate($category['main_image_2'], array('width' => 70, 'height' => 70, 'crop' => true, 'default_image' => '/media/images/no-image-70.webp', 'html_tag' => TRUE, 'srcset' => '140 2x')); ?> alt="<?php echo $category['title']; ?>" /></figure>
						<span><?php echo $category['title']; ?></span>
					</a>
				</li>
				<?php $c++; ?>
			<?php endforeach; ?>
			<div class="c-category-btn-container" data-category-count="<?php echo $c ?>">
				<div class="c-category-btn">
					<span class="more"><?php echo Arr::get($cmslabel, 'category_item_more'); ?></span>
					<span class="less"><?php echo Arr::get($cmslabel, 'category_item_less'); ?></span>
				</div>
			</div>
		</ul>
	</div>
<?php endif; ?>