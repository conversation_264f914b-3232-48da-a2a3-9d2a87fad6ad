<?php $mode = (isset($mode)) ? $mode : ''; ?>
<?php $promotions = Widget_Publish::category($info['lang'], 'promotions'); ?>
<?php if($mode == 'catalog_landing'): ?>
	<?php $catalog_promo = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'catalog_landing_promo', 'limit' => 18]); ?>
<?php elseif($mode == 'catalog_category_top'): ?>
	<?php $catalog_promo = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'catalog_category_promo_top', 'limit' => 12, 'catalogcategory_id' => $kind['id'])); ?>
<?php elseif($mode == 'catalog_category_bottom'): ?>
	<?php $catalog_promo = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'catalog_category_promo_bottom', 'limit' => 12, 'catalogcategory_id' => $kind['id'])); ?>
<?php endif; ?>

<?php if($catalog_promo): ?>
	<div class="c-promo-wrapper">
		<?php if($mode == 'catalog_landing'): ?>
			<div class="c-promo-header">
				<div class="c-promo-title c-title"><?php echo Arr::get($cmslabel, 'best_offers'); ?></div>
				<a href="<?php echo $promotions['url']; ?>" class="btn btn-arrow"><span><?php echo Arr::get($cmslabel, 'show_all'); ?></span></a>
			</div>
		<?php endif; ?>
		<div class="c-promo">
			<?php $cpp_i = 1; ?>
			<?php foreach ($catalog_promo as $catalog_promo_item): ?>
				<?php $catalog_promo_item_title = str_replace('"', "'", Text::meta($catalog_promo_item['title'])); ?>
				<?php if(!empty($kind)): ?>
					<?php $kind_title = str_replace('"', "'", Text::meta($kind['title'])); ?>
				<?php endif; ?>
				<?php if($catalog_promo_item['link']): ?><a href="<?php echo $catalog_promo_item['link']; ?>" class="c-promo-item link<?php if(!empty($catalog_promo_item['template'])): ?> <?php echo $catalog_promo_item['template']; ?><?php endif; ?>" <?php if($catalog_promo_item['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php if($mode == 'catalog_landing'): ?>catalog landing - bestoffers<?php else: ?>category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_category_bottom'): ?>bottom<?php else: ?>top<?php endif; ?><?php endif; ?><?php if($catalog_promo_item['template'] == 'template_catalog_promo_big'): ?> - big<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_medium'): ?> - medium<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_small'): ?> - small<?php endif; ?> - <?php echo $cpp_i; ?>" data-tracking_gtm_promo="addPromo|<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php echo $catalog_promo_item_title; ?>"><?php else: ?><div class="c-promo-item<?php if(!empty($catalog_promo_item['template'])): ?> <?php echo $catalog_promo_item['template']; ?><?php endif; ?>" data-tracking_gtm_promo="addPromo|<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php echo $catalog_promo_item_title; ?>"><?php endif; ?>
					<span class="c-promo-item-img" data-tracking_gtm_promo_view="<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php if($mode == 'catalog_landing'): ?>catalog landing - bestoffers<?php else: ?>category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_category_bottom'): ?>bottom<?php else: ?>top<?php endif; ?><?php endif; ?><?php if($catalog_promo_item['template'] == 'template_catalog_promo_big'): ?> - big<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_medium'): ?> - medium<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_small'): ?> - small<?php endif; ?> - <?php echo $cpp_i; ?>">
						<?php if(!empty($catalog_promo_item['image'])): ?>
							<?php if($catalog_promo_item['template'] == 'template_catalog_promo_big'): ?>
								<picture>
									<?php if (!empty($catalog_promo_item['image_2'])): ?>
										<source srcset="<?php echo Thumb::generate($catalog_promo_item['image_2'], 724, 380, true, 'thumb', TRUE, '/media/images/no-image-724x380.webp'); ?>" media="(max-width: 760px)">
									<?php endif; ?>
									<img <?php echo Thumb::generate($catalog_promo_item['image'], array('width' => 1480, 'height' => 540, 'crop' => true, 'default_image' => '/media/images/no-image-1480x540.webp', 'html_tag' => TRUE)); ?> alt="<?php echo $catalog_promo_item['title']; ?>" />
								</picture>
							<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_medium'): ?>
								<img <?php echo Thumb::generate($catalog_promo_item['image'], array('width' => 724, 'height' => 379, 'crop' => true, 'default_image' => '/media/images/no-image-724x380.webp', 'html_tag' => TRUE)); ?> alt="<?php echo $catalog_promo_item['title']; ?>" />
							<?php elseif($catalog_promo_item['template'] == 'template_catalog_promo_small'): ?>
								<img <?php echo Thumb::generate($catalog_promo_item['image'], array('width' => 346, 'height' => 181, 'crop' => true, 'default_image' => '/media/images/no-image-346x180.webp', 'html_tag' => TRUE)); ?> alt="<?php echo $catalog_promo_item['title']; ?>" />
							<?php endif; ?>
						<?php endif; ?>
					</span>
					<?php if($catalog_promo_item['template'] == 'template_catalog_promo_small' AND (!empty($catalog_promo_item['title']) OR !empty($catalog_promo_item['element_content_small']) OR !empty($catalog_promo_item['element_button_text']))): ?>
						<span class="c-promo-item-cnt">
							<?php if(!empty($catalog_promo_item['title'])): ?>
								<span class="c-promo-item-title"><?php echo $catalog_promo_item['title']; ?></span>
							<?php endif; ?>
							<?php if(!empty($catalog_promo_item['element_content_small'])): ?>
								<span class="c-promo-item-desc"><?php echo $catalog_promo_item['element_content_small']; ?></span>
							<?php endif; ?>
							<?php if(!empty($catalog_promo_item['element_button']) AND !empty($catalog_promo_item['link'])): ?>
								<span class="c-promo-item-btn"><?php echo $catalog_promo_item['element_button']; ?></span>
							<?php endif; ?>
						</span>
					<?php endif; ?>
				<?php if($catalog_promo_item['link']): ?></a><?php else: ?></div><?php endif; ?>
				<?php $cpp_i++; ?>
			<?php endforeach; ?>
		</div>
	</div>
<?php endif; ?>