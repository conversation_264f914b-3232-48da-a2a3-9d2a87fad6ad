<?php $mode = (isset($mode)) ? $mode : '';  ?>
<?php $p_i = 1; ?>
<?php foreach ($items as $promo): ?>
	<?php $promo_title = str_replace('"', "'", Text::meta($promo['title'])); ?>
	<?php $kind_title = str_replace('"', "'", Text::meta($kind['title'])); ?>
	<?php if($promo['link'] AND $promo['template'] != 'template_catalog_promo_special'): ?><a href="<?php echo $promo['link']; ?>" data-tracking_gtm_promo_click="<?php echo $promo['id']; ?>|<?php echo $promo_title; ?>|<?php echo $promo['image']; ?>|category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_bottom'): ?>bottom<?php else: ?>top<?php endif; ?> - <?php if($promo['template'] == 'template_catalog_promo_big'): ?>big<?php elseif($promo['template'] == 'template_catalog_promo_medium'): ?>medium<?php elseif($promo['template'] == 'template_catalog_promo_special'): ?>special<?php endif; ?> - <?php echo $p_i; ?>" data-tracking_gtm_promo="addPromo|<?php echo $promo['id']; ?>|<?php echo $promo_title; ?>|<?php echo $promo['image']; ?>|<?php echo $promo_title; ?>" class="c-promo c-promo-link<?php if($promo['template'] == 'template_catalog_promo_big'): ?> c-promo-big<?php elseif($promo['template'] == 'template_catalog_promo_medium'): ?> c-promo-medium<?php elseif($promo['template'] == 'template_catalog_promo_small'): ?> c-promo-small<?php else: ?> c-promo-special<?php endif; ?>" <?php if($promo['link_target_blank']): ?> target="_blank" <?php endif; ?>><?php else: ?><div class="c-promo<?php if($promo['template'] == 'template_catalog_promo_big'): ?> c-promo-big<?php elseif($promo['template'] == 'template_catalog_promo_medium'): ?> c-promo-medium<?php elseif($promo['template'] == 'template_catalog_promo_small'): ?> c-promo-small<?php else: ?> c-promo-special<?php endif; ?>"><?php endif; ?>
		<?php if($promo['template'] == 'template_catalog_promo_special'): ?>
			<div class="col1" data-tracking_gtm_promo_view="<?php echo $promo['id']; ?>|<?php echo $promo_title; ?>|<?php echo $promo['image']; ?>|category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_bottom'): ?>bottom<?php else: ?>top<?php endif; ?> - special - <?php echo $p_i; ?>">
				<img loading="lazy" <?php echo Thumb::generate($promo['image'], array('width' => 300, 'height' => 300, 'default_image' => '/media/images/no-image-285.webp', 'html_tag' => TRUE, 'srcset' => '600c 2x')); ?> alt="<?php echo $promo_title; ?>" />
			</div>
			<div class="col2">
				<?php echo $promo['content']; ?>
			</div>
		<?php else: ?>
			<?php if($promo['template'] == 'template_catalog_promo_big'): ?>
				<picture data-tracking_gtm_promo_view="<?php echo $promo['id']; ?>|<?php echo $promo_title; ?>|<?php echo $promo['image']; ?>|category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_bottom'): ?>bottom<?php else: ?>top<?php endif; ?> - big - <?php echo $p_i; ?>">
					<?php if(!empty($promo['image_2'])): ?>
						<source srcset="<?php echo Thumb::generate($promo['image_2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
					<?php endif; ?>
					<img <?php echo Thumb::generate($promo['image'], array('width' => 1105, 'height' => 403, 'default_image' => '/media/images/no-image-1105.webp', 'placeholder' => '/media/images/no-image-1105.webp', 'srcset' => '1105c 2x')); ?> alt="<?php echo $promo_title; ?>" />
				</picture>
			<?php elseif($promo['template'] == 'template_catalog_promo_medium'): ?>
				<picture data-tracking_gtm_promo_view="<?php echo $promo['id']; ?>|<?php echo $promo_title; ?>|<?php echo $promo['image']; ?>|category - <?php echo $kind_title; ?> - <?php if($mode == 'catalog_bottom'): ?>bottom<?php else: ?>top<?php endif; ?> - medium - <?php echo $p_i; ?>">
					<?php if(!empty($promo['image_2'])): ?>
						<source srcset="<?php echo Thumb::generate($promo['image_2'], 760, 760, false, 'thumb', TRUE, '/media/images/no-image-760.webp'); ?>" media="(max-width: 760px)">	
					<?php endif; ?>
					<img <?php echo Thumb::generate($promo['image'], array('width' => 543,'height' => 284, 'default_image' => '/media/images/no-image-543.webp', 'placeholder' => '/media/images/no-image-543.webp', 'srcset' => '1100c 2x')); ?> alt="<?php echo $promo_title; ?>" />
				</picture>
			<?php endif; ?>
			<?php /*<img <?php echo Thumb::generate($promo['image'], array('width' => 355, 'height' => 255, 'html_tag' => TRUE, 'srcset' => '710c 2x')); ?> alt="<?php echo $promo_title; ?>" /> */ ?>
		<?php endif; ?>
	<?php if($promo['link'] AND $promo['template'] != 'template_catalog_promo_special'): ?></a><?php else: ?></div><?php endif; ?>
	<?php $p_i++; ?>
<?php endforeach; ?>