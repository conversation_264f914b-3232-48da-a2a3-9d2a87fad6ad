<?php $locations = Arr::get($item, 'warehouses_display'); ?>
<?php if ((!empty($item['shipping_options']) AND !empty($item['active_shipping_options_count'])) OR !empty($locations)): ?>
    <?php $calendar_days = Kohana::config('app.utils.calendar.si.days_full'); ?>

    <div class="cd-item-info-container">
        <?php if (!empty($item['shipping_options']) AND !empty($item['active_shipping_options_count'])): ?>
            <div class="cd-item-info cd-item-info-shipping cd-flyout-btn" data-flyout_code="shipping">
                <div class="cd-item-info-title shipping"><?php echo Arr::get($cmslabel, 'item_delivery_tab_title'); ?></div>
                <?php foreach ($item['shipping_options'] as $delivery_key => $delivery_data): ?>
                    <?php if ($item['status'] == '5' AND !empty($item['shipping_date'])): ?>
                        <!-- Preorder product -->
                        <?php if (in_array($delivery_key, ['s', 'e', 'bb', 'bb_xxl', 'bb_fast'])): ?>
                            <div class="cd-item-info-desc">
                                <?php if ($delivery_key == 's'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery'); ?>:
                                <?php elseif ($delivery_key == 'e'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_express_delivery'); ?>:
                                <?php elseif ($delivery_key == 'bb'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery'); ?>:
                                <?php elseif ($delivery_key == 'bb_xxl'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery'); ?>:
                                <?php elseif ($delivery_key == 'bb_fast'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_premium_title'); ?>:
                                <?php endif; ?>
                                <?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?> 
                                <?php /* ?>
                                <span class="shipping-price">- <strong><?php echo Utils::currency_format($delivery_data['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong></span>
                                <?php */ ?>
                            </div>
                        <?php endif; ?>
                    <?php elseif ($item['status'] != '5'): ?>
                        <!-- Not preorder product -->
                        <?php if (in_array($delivery_key, ['s', 'e', 'bb', 'bb_xxl', 'bb_fast'])): ?>
                            <?php
                                if ($delivery_key != 'bb_fast') {
                                    $calendar_month = Kohana::config('app.utils.calendar.si.month');
                                    $calendar_days = Kohana::config('app.utils.calendar.si.days_full');
                                    $shipping_date_day = date('w', $delivery_data['min_delivery_date']);
                                    $shipping_date_month = (date('n', $delivery_data['min_delivery_date']) - 1);

                                    if (!empty($calendar_days[$shipping_date_day]) AND !empty($calendar_month[$shipping_date_month])) {
                                        if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())) {
                                            $shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        } elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))) {
                                            $shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        } else {
                                            $shipping_date = $calendar_days[$shipping_date_day] . ', ' . date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        }
                                    } else {
                                        $shipping_date = strftime("%a %e %B", $delivery_data['min_delivery_date']);
                                    }
                                }
                            ?>

                            <div class="cd-item-info-desc">
                                <?php if ($delivery_key == 's'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery'); ?>
                                <?php elseif ($delivery_key == 'e'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_express_delivery'); ?>
                                <?php elseif ($delivery_key == 'bb'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery'); ?>
                                <?php elseif ($delivery_key == 'bb_xxl'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery'); ?>
                                <?php elseif ($delivery_key == 'bb_fast'): ?>
                                    <?php echo str_replace('%s%', implode($item['shipping_options']['bb_fast']['fast_shipping_titles']), Arr::get($cmslabel, 'item_delivery_premium')); ?>
                                <?php endif; ?>
                                
                                <?php if ($delivery_key != 'bb_fast'): ?>
                                    <?php if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())):  ?>
                                        <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
                                    <?php elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))):  ?>
                                        <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
                                    <?php else: ?>
                                        <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php /* ?>
                                <span class="shipping-price">- <strong><?php echo Utils::currency_format($delivery_data['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong></span>
                                <?php */ ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>

            <div class="cd-tab-info-body cd-tab-body-shipping cd-flyout" data-flyout="shipping">
                <div class="cd-flyout-close"></div>
                <div class="cd-flyout-content">
                    <?php foreach ($item['shipping_options'] as $delivery_key => $delivery_data): ?>
                        <?php if (in_array($delivery_key, ['s', 'e', 'bb', 'bb_xxl', 'bb_fast'])): ?>
                            <?php
                                if ($delivery_key != 'bb_fast') {
                                    $calendar_month = Kohana::config('app.utils.calendar.si.month');
                                    $calendar_days = Kohana::config('app.utils.calendar.si.days_full');
                                    $shipping_date_day = date('w', $delivery_data['min_delivery_date']);
                                    $shipping_date_month = (date('n', $delivery_data['min_delivery_date']) - 1);

                                    if (!empty($calendar_days[$shipping_date_day]) AND !empty($calendar_month[$shipping_date_month])) {
                                        if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())) {
                                            $shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        } elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))) {
                                            $shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        } else {
                                            $shipping_date = $calendar_days[$shipping_date_day] . ', ' . date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
                                        }
                                    } else {
                                        $shipping_date = strftime("%a %e %B", $delivery_data['min_delivery_date']);
                                    }
                                }
                            ?>

                            <div class="cd-flyout-info-row">
                                <div class="cd-flyout-info-title">
                                    <?php if ($delivery_key == 's'): ?>
                                        <?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery'); ?>
                                    <?php elseif ($delivery_key == 'e'): ?>
                                        <?php echo Arr::get($cmslabel, 'item_delivery_express_delivery'); ?>
                                    <?php elseif ($delivery_key == 'bb'): ?>
                                        <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery'); ?>
                                    <?php elseif ($delivery_key == 'bb_xxl'): ?>
                                        <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery'); ?>
                                    <?php elseif ($delivery_key == 'bb_fast'): ?>
                                        <?php echo Arr::get($cmslabel, 'item_delivery_premium_title'); ?>
                                    <?php endif; ?>
                                </div>
                                <div class="cd-flyout-info-desc">
                                    <?php if ($item['status'] == '5'): ?>
                                        <?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?>
                                    <?php else: ?>
                                        <?php if ($delivery_key == 'bb_fast'): ?>
                                            <?php echo str_replace('%s%', implode($item['shipping_options']['bb_fast']['fast_shipping_titles']), Arr::get($cmslabel, 'item_delivery_premium')); ?>
                                        <?php else: ?>
                                            <?php if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())):  ?>
                                                <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
                                            <?php elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))):  ?>
                                                <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
                                            <?php else: ?>
                                                <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php /* ?>
                                    <span class="shipping-price">- <strong><?php echo Utils::currency_format($delivery_data['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong></span>
                                    <?php */ ?>
                                </div>
                                <?php if ($delivery_key == 's'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery_flyout_desc'); ?>
                                <?php elseif ($delivery_key == 'e'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_express_delivery_flyout_desc'); ?>
                                <?php elseif ($delivery_key == 'bb'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery_flyout_desc'); ?>
                                <?php elseif ($delivery_key == 'bb_xxl'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery_flyout_desc'); ?>
                                <?php elseif ($delivery_key == 'bb_fast'): ?>
                                    <?php echo Arr::get($cmslabel, 'item_delivery_premium_flyout_desc'); ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>

                    <div class="cd-flyout-bottom">
                        <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Product / variations warehouse availability -->
        <?php if(!empty($item['shipping_options']['p']) AND $item['status'] != '7'): ?>
            <?php if(!empty($item['shipping_options']['p']['active'])): ?>
                <?php
                    $shipping_date_original = ($item['status'] == '5') ? $item['shipping_date'] : $item['shipping_options']['p']['min_delivery_date'];
                    $shipping_date_day = date('w', $shipping_date_original);
                    $shipping_date_month = (date('n', $shipping_date_original) - 1);

                    if (!empty($calendar_days[$shipping_date_day])) {
                        if (date('Y-m-d', $shipping_date_original) == date('Y-m-d', time())) {
                            $shipping_date = date('d', $shipping_date_original) . '.' . date('m', $shipping_date_original) . '.';
                        } elseif (date('Y-m-d', $shipping_date_original) == date('Y-m-d', strtotime('+1day'))) {
                            $shipping_date = date('d', $shipping_date_original) . '.' . date('m', $shipping_date_original) . '.';
                        } else {
                            $shipping_date = $calendar_days[$shipping_date_day].', '. date('d', $shipping_date_original) . '.' . date('m', $shipping_date_original) . '.';
                        }
                    } else {
                        $shipping_date = strftime("%a %e %B", $shipping_date_original);
                    }
                ?>
            <?php endif; ?>

            <div class="cd-item-info cd-item-info-stores cd-flyout-btn" data-flyout_code="stores">
                <div class="cd-item-info-title stores">
                    <?php if ($item['status'] == '4' AND !empty($locations)): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_tab_title_limited'); ?>
                    <?php else: ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_tab_title'); ?>
                    <?php endif; ?>
                </div>
                <div class="cd-item-info-desc">
                    <?php if ($item['status'] == '5'): ?>
                        <?php if (!empty($item['warehouses_single_pickup_display'])): ?>
                            <?php echo str_replace(['%s%', '%s2%'], [$item['warehouses_single_pickup_display']['title'], date('d.m.Y', $item['shipping_date'])], Arr::get($cmslabel, 'item_time_of_delivery_preorder_single')) ?>
                        <?php else: ?>
                            <?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?>
                        <?php endif; ?>
                        <?php /* ?>
                        <span class="shipping-price">- <strong><?php echo Arr::get($cmslabel, 'free'); ?></strong></span>
                        <?php */ ?>
                    <?php elseif(!empty($item['shipping_options']['p']['active'])): ?>
                        <?php if (!empty($item['warehouses_single_pickup_display'])): ?>
                            <?php echo str_replace('%s%', $item['warehouses_single_pickup_display']['title'], Arr::get($cmslabel, 'item_delivery_pickup_single')) ?>
                        <?php else: ?>
                            <?php echo Arr::get($cmslabel, 'item_delivery_pickup_store'); ?>
                        <?php endif; ?>
                        <?php if (date('Y-m-d', $shipping_date_original) == date('Y-m-d', time())):  ?>
                            <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
                        <?php elseif (date('Y-m-d', $shipping_date_original) == date('Y-m-d', strtotime('+1day'))):  ?>
                            <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
                        <?php else: ?>
                            <?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
                        <?php endif; ?>
                        <?php /* ?>
                        - <strong><?php echo Arr::get($cmslabel, 'free'); ?></strong>
                        <?php */ ?>
                    <?php endif; ?>
                </div>
                <?php if($item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
                    <div class="cd-item-info-desc"><?php echo Arr::get($cmslabel, 'item_time_of_delivery_bb_fast'); ?></div>
                <?php endif; ?>
                <?php if(($item['status'] == '1' AND empty($item['last_piece_sale']) AND empty($item['warehouses_single_pickup_display'])) OR ($item['status'] == '4' AND !empty($locations))): ?>
                    <div class="cd-item-info-desc link"><?php echo Arr::get($cmslabel, 'warehouses_availability'); ?></div>
                <?php endif; ?>
            </div>
            
            <?php echo View::factory('catalog/widget_detail/flyout_stores', array('item' => $item, 'locations' => $locations)); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>