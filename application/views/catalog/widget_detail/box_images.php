<?php $images = Arr::get($item, 'all_images'); ?>

<div id="product_images" class="cd-images" data-bxslider_var_name="catalog_details_bxlider">
    <?php if ($images): ?>
        <div class="cd-hero-image">
            <div class="cd-hero-slider">
                <?php $i = 0; ?>
                <?php foreach ($images as $file): ?>
                    <a class="cd-hero-slide product-gallery" href="<?php echo $item['url']; ?>?mode=gallery&index=<?php echo $i; ?>" title="<?php echo Text::meta($file['title']); ?>"  <?php if (!empty($file['variation_code'])): ?> data-variation_code="<?php echo $file['variation_code']; ?>" style="display: none"<?php endif; ?>>
                        <span <?php if ($i == 0): ?>class="cd-fixed-img"<?php endif; ?>>
                            <img <?php echo Thumb::generate($file['file'], array('width' => 640, 'height' => 640, 'default_image' => '/media/images/no-image-640x640.webp', 'html_tag' => TRUE, 'srcset' => '1280c 2x')); ?> title="<?php echo Text::meta($file['title']); ?>" alt="<?php echo Text::meta($file['description']); ?>" <?php if(!empty($file['zoom_available'])): ?> data-zoom-image="<?php echo Thumb::generate($file['file'], 1000, 1000, false, 'thumb', TRUE, '/media/images/no-image-640x640.webp'); ?>"<?php endif; ?> <?php if ($i == 0): ?>data-product_main_image="1"<?php endif; ?> <?php if(!empty($file['zoom_available'])): ?>class="zoom-available" <?php endif; ?>/>
                        </span>
                    </a>
                    <?php $i++; ?>
                <?php endforeach; ?>
            </div>

            <div data-offer_selected_html="box_badges_hero_image">
                <?php echo View::factory('catalog/widget_detail/box_badges_hero_image', array('item' => $item)); ?>
            </div>
        </div>
        <?php if(count($images) > 1): ?>
            <div class="fz0 cd-thumbs cd-thumbs-slider<?php if(count($images) <= 4): ?> no-slider<?php endif; ?>">
                <?php $t = 0; ?>
                <?php foreach ($images as $file): ?>
                    <a href="javascript:void(0);" data-slide-index="<?php echo $t; ?>" class="cd-thumb" <?php if (!empty($file['variation_code'])): ?> data-variation_code="<?php echo $file['variation_code']; ?>" style="display: none"<?php endif; ?>>
                        <span>
                            <img <?php echo Thumb::generate($file['file'], array('width' => 110, 'height' => 110, 'default_image' => '/media/images/no-image-120.webp', 'html_tag' => TRUE, 'srcset' => '220c 2x')); ?> alt="<?php echo Text::meta($file['description']); ?>" />
                        </span>
                    </a>
                    <?php $t++; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <div id="zoom-container"></div>
    <?php else: ?>
        <div class="cd-no-image cd-fixed-img"><img loading="lazy" src="/media/images/no-image-650.webp" alt="" data-product_main_image="1"></div>
    <?php endif; ?>
</div>