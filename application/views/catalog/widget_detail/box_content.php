<?php
$items_attributes = [];
$items_attributes_unit = [];
foreach($item['attributes'] as $item_attribute) {
    if(!in_array($item_attribute['attribute_code'], ['attribute_badges', 'superbenefits', 'katalog-9001'])) {
        $items_attributes[$item_attribute['attribute_title']][] = $item_attribute['title'];
        $items_attributes_unit[$item_attribute['attribute_title']] = (!empty($item_attribute['attribute_unit'])) ? $item_attribute['attribute_unit'] : '';
    }
}

$item_content = '';
$description_length = 0;
$short_description_text = trim(strip_tags($item['short_description']));
$content_text = trim(strip_tags($item['content']));
if (!empty($item['short_description']) AND strlen($short_description_text) >= 10 AND $short_description_text != $content_text AND !Text::starts_with($content_text, $short_description_text)) {
    $item_content .= $item['short_description'];
    $description_length = strlen($short_description_text);
}
if (!empty($item['content']) AND strlen($content_text) >= 10) {
    if (!empty($item_content)) {
        $item_content .= '<p>&nbsp;</p>';
    }
    $item_content .= $item['content'];
    $description_length = $description_length + strlen($content_text);
}

if (!empty($item['element_content_iframe'])) {
    // FIXME INTEG sadržaj iframe ispisati bez scrollbara
    $item_content = '<iframe src="'.$item['element_content_iframe'].'" style="min-height: 800px;"></iframe>';
}

$product_pim_min_chars = (int)App::config('product_pim_min_chars');
if (empty($item['element_content_iframe']) AND $description_length < $product_pim_min_chars) {
    $ean_code = Arr::get($item, 'ean_code');
    $manufacturer = Arr::get($item, 'manufacturer_title');

    $is_in_loadbee_list = Catalog::listitems('webcatalog_157271', [$item['id']]);
    $is_in_flixmedia_list = Catalog::listitems('webcatalog_157270', [$item['id']]);
    $is_in_logitech_list = Catalog::listitems('webcatalog_201949', [$item['id']]);

    $item_content_api = '';
    if (!empty($is_in_flixmedia_list) AND empty($is_in_loadbee_list)) {
        $cs_provider = 'Flixmedia';
        $item_content_api .= '<div id="flix-inpage"></div>';
        $item_content_api .= '<script type="text/javascript" src="//media.flixfacts.com/js/loader.js"
												data-flix-distributor="10251"
												data-flix-language="sl"
												data-flix-brand="' . Text::meta($manufacturer) . '"
												data-flix-ean="' . $ean_code . '"
												data-flix-sku=""
												data-flix-button="flix-minisite"
												data-flix-inpage="flix-inpage"
												data-flix-button-image=""
												data-flix-fallback-language="en"
												data-flix-autoload="inpage"
												data-flix-price="" async>
										</script>';
    } elseif (!empty($is_in_loadbee_list)) {
        $cs_provider = 'Loadbee';
        $item_content_api .= '<script type = "text/javascript">
										loadbeeApiKey = "CBTGtm5pktttLraG5zsAZRmvfmKdzbfZ";
									</script>';
        $item_content_api .= '<div class="loadbeeTabContent">
									<div class="loadbeeTab"
										data-loadbee-manufacturer="EAN"
										data-loadbee-product="' . $ean_code . '"
										data-loadbee-language="sl_SI"
										data-loadbee-css="default"
										data-loadbee-button="default"
										data-loadbee-template="default">
									</div>
								</div>';
        $item_content_api .= '<script async type="text/javascript" src="http://button.loadbee.com/js/v2/loadbee.js"></script>';
    } elseif (!empty($is_in_logitech_list)) {
        $cs_provider = 'Logitech';
        $item_content_api .= '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' . $ean_code . '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
    }
}
?>

<?php if($item_content OR !empty($items_attributes) OR !empty(isset($item['feedback_comment_widget']))): ?>
    <div class="cd-tabs-container">
        <div class="cd-tabs">
            <div class="cd-tab-wrapper">
                <?php if($item_content): ?>
                    <a class="cd-tab-title cd-tab-flyout-btn active" data-flyout_btn_special="tab1" href="#tab1"><span><?php echo Arr::get($cmslabel, 'tab_product_description'); ?></span></a>
                <?php endif; ?>
                <?php if(!empty($items_attributes)): ?>
                    <a class="cd-tab-title cd-tab-flyout-btn" data-flyout_btn_special="tab2" href="#tab2"><span><?php echo Arr::get($cmslabel, 'tab_specs'); ?></span></a>
                <?php endif; ?>
                <?php if(!empty(isset($item['feedback_comment_widget']))): ?>
                    <a class="cd-tab-title cd-tab-flyout-btn" data-flyout_btn_special="tab4" href="#tab4">
                        <span><?php echo Arr::get($cmslabel, 'tab_comments'); ?></span>
                        <?php if (isset($item['feedback_rate_widget'])): ?>
                            <div class="cd-tab-title-rate-section">
                                <?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
                                <?php if(number_format($item['feedback_rate_widget']['rates'], 1) > 0): ?>
                                    <span class="cd-tab-title-rate-average"><?php echo number_format($item['feedback_rate_widget']['rates'], 1); ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <div class="cd-tabs-content cd-tab-wrapper">
            <?php if(!empty($item_content) OR !empty($item_content_api)): ?>
                <div class="cd-tab-body cd-tab-body-content" id="tab1" data-anchor="tab1">
                    <div class="cd-tab-close cd-flyout-close"></div>
                    <div class="cd-tab-body-inner">
                        <div class="cd-tab-body-inner-header">
                            <div class="cd-tab-body-title"><?php echo Arr::get($cmslabel, 'tab_product_description'); ?></div>
                        </div>
                        <div id="itemContent" class="cd-tab-content cms-content"<?php if(!empty($item['ean_code'])): ?> data-ean="<?php echo Arr::get($item, 'ean_code'); ?>"<?php endif; ?>>
                            <?php if (!empty($item_content_api)) : ?>
                                <?php if(!empty($cs_provider) AND $cs_provider == 'Logitech'): ?>
                                    <div data-type_of_content="cs_api_content" data-cs_provider="<?php echo $cs_provider ?>" style="display: none;">
                                        <?php echo $item_content_api; ?>
                                    </div>
                                <?php else: ?>
                                    <div data-type_of_content="cs_api_content" data-cs_provider="<?php echo $cs_provider ?>" style="display: none;">
                                        <?php echo $item_content_api; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (!empty($item_content)) : ?>
                                <div data-type_of_content="pim_content" data-content_length="<?php echo $description_length; ?>">
                                    <?php echo $item_content; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="cd-tab-bottom cd-flyout-bottom">
                            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(!empty($items_attributes)): ?>
                <div class="cd-tab-body cd-tab-body-specs" id="tab2" data-anchor="tab2">
                    <div class="cd-tab-close cd-flyout-close"></div>
                    <div class="cd-tab-body-inner">
                        <div class="cd-tab-body-inner-header">
                            <div class="cd-tab-body-title"><?php echo Arr::get($cmslabel, 'tab_specs'); ?></div>
                        </div>
                        <!-- Attributes -->
                        <div class="cd-tab-attributes">
                            <?php foreach ($items_attributes as $item_attribute_title => $item_attribute_values): ?>
                                <div class="cd-tab-attribute">
                                    <div class="cd-tab-attribute-title"><?php echo $item_attribute_title; ?></div>
                                    <div class="cd-tab-attribute-desc">
                                        <?php echo implode(', ', $item_attribute_values); ?>
                                        <?php if (!empty($items_attributes_unit[$item_attribute_title])): ?>
                                            <?php echo $items_attributes_unit[$item_attribute_title]; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="cd-tab-bottom cd-flyout-bottom">
                            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Comments -->
            <?php if (isset($item['feedback_comment_widget'])): ?>
                <div class="cd-tab-body cd-tab-comment" id="tab4" data-anchor="tab4">
                    <div class="cd-tab-close cd-flyout-close"></div>
                    <div class="cd-tab-body-inner">
                        <div class="cd-tab-body-inner-header">
                            <div class="cd-tab-body-title"><?php echo Arr::get($cmslabel, 'tab_comments'); ?></div>
                        </div>
                        <div class="cd-comments-info">
                            <div class="cd-comments-info-col1<?php if($item['feedback_comment_widget']['comments'] == 0): ?> empty<?php endif; ?>">
                                <?php if (isset($item['feedback_rate_widget'])): ?>
                                    <div class="cd-comments-rate-section">
                                        <?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
                                        <?php if(number_format($item['feedback_rate_widget']['rates'], 1) > 0): ?>
                                            <span class="cd-comments-rate-average"><?php echo number_format($item['feedback_rate_widget']['rates'], 1); ?></span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                <div class="cd-comments-qty">
                                    <?php if($item['feedback_comment_widget']['comments'] > 0): ?>
                                        <?php echo str_replace('%c%', $item['feedback_comment_widget']['comments'], Arr::get($cmslabel, 'comments_qty')); ?>
                                    <?php else: ?>
                                        <?php echo Arr::get($cmslabel, 'no_comments'); ?>
                                    <?php endif; ?>
                                </div>
                                <button class="cd-comments-form-button">
                                    <span class="show"><?php echo Arr::get($cmslabel, 'comments_show_form'); ?></span>
                                    <span class="hide"><?php echo Arr::get($cmslabel, 'comments_hide_form'); ?></span>
                                </button>
                            </div>
                            <?php if (isset($item['feedback_rate_widget'])): ?>
                                <?php if(number_format($item['feedback_rate_widget']['rates'], 1) > 0): ?>
                                    <div class="cd-comments-info-col2 cd-chart-items">
                                        <?php $r = 1; ?>
                                        <?php
                                        $rates_number = array();
                                        foreach ($item['feedback_comment_widget']['items'] AS $rates => $rates_item) {
                                            if(!empty($rates_item['rate'])) {
                                                $rates_number[$rates] = $rates_item['rate'];
                                            }
                                        }
                                        $rates_number_total = count($rates_number);
                                        ?>
                                        <?php while($r < 6): ?>
                                            <?php
                                            if (in_array($r, $rates_number)) {
                                                $rates_number_count = array_count_values($rates_number);
                                                $rates_number_qty =  $rates_number_count[$r];
                                            } else {
                                                $rates_number_qty = '0';
                                            }

                                            if($rates_number_qty > 0) {
                                                $rates_percent = $rates_number_qty / $rates_number_total * 100;
                                            } else {
                                                $rates_percent = 0;
                                            }
                                            ?>

                                            <div class="cd-chart-item">
                                                <div class="cd-chart-rate"><?php echo $r; ?></div>
                                                <div class="cd-chart-bar"><span class="cd-chart-progress-bar" style="width: <?php echo number_format((float)$rates_percent, 2, '.', ''); ?>%"></span></div>
                                                <div class="cd-chart-qty"><?php echo $rates_number_qty; ?></div>
                                            </div>
                                            <?php $r++ ?>
                                        <?php endwhile; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <?php echo View::factory('feedback/comments', $item['feedback_comment_widget']); ?>

                        <div class="cd-tab-bottom cd-flyout-bottom">
                            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
        </div>
    </div>
<?php endif; ?>