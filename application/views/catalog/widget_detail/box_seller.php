<?php $first_main_seller_offer = $item['first_main_seller_offer_id'] ? Arr::get($item['offers'], $item['first_main_seller_offer_id']) : []; ?>

<?php if (!empty($item['seller_id'])): ?>
    <div class="cd-sellers">
        <?php if ($item['seller_code'] != '9999' AND !empty($cmslabel['sellers_info'])): ?>
            <div class="cd-sellers-info">
                <?php echo Arr::get($cmslabel, 'sellers_info'); ?>
                <?php if(!empty($cmslabel['sellers_info_btn'])): ?>
                    <div class="cd-sellers-info-btn cd-flyout-btn" data-flyout_code="seller-info"><?php echo Arr::get($cmslabel, 'sellers_info_btn'); ?></div>
                <?php endif; ?>
            </div>
            <div class="cd-flyout" data-flyout="seller-info">
                <div class="cd-flyout-close"></div>
                <div class="cd-flyout-content">
                    <div class="cd-flyout-header">
                        <div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'sellers_info_flyout_title'); ?></div>
                    </div>
                    <div class="cd-sellers-info-content"><?php echo Arr::get($cmslabel, 'sellers_info_flyout'); ?></div>
                    <div class="cd-flyout-bottom">
                        <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="cd-seller-top">
            <?php if(!empty($item['seller_url']) AND !empty($item['seller_title'])): ?>
                <div class="cd-seller-title">
                    <?php if(!empty($item['seller_corporate_name'])): ?>
						<span><?php echo Arr::get($cmslabel, 'seller_item_title'); ?> <a href="<?php echo $item['seller_url']; ?>"><?php echo $item['seller_corporate_name']; ?></a></span>
					<?php endif; ?>
					<?php if(!empty($item['seller_title'])): ?>
						<span class="extra-name">(<?php echo $item['seller_title']; ?>)</span>
					<?php endif; ?>
                </div>
            <?php endif; ?>
            <?php /* ?>
            <div class="cd-seller-rate">
                <span class="cp-rate rates-container add_rate" data-rates="0.0000" data-rates_votes="0" data-rates_sum="0" title="" data-rates_stars="1"><span data-score="1" class="icon-star-empty"></span><span data-score="2" class="icon-star-empty"></span><span data-score="3" class="icon-star-empty"></span><span data-score="4" class="icon-star-empty"></span><span data-score="5" class="icon-star-empty"></span></span>
            </div>
            <?php if(!empty($item['product_condition'])): ?>
                <div class="cd-seller-condition">
                    <?php echo Arr::get($cmslabel, 'item_condition'); ?>
                    <span><?php echo Arr::get($cmslabel, 'condition_'.$item['product_condition']); ?></span>
                </div>
            <?php endif; ?>
            <?php */ ?>
        </div>
        <?php $first_other_offer = $item['first_other_offer_id'] ? Arr::get($item['offers'], $item['first_other_offer_id']) : []; ?>
        <?php if(!empty($first_other_offer) AND (empty($first_main_seller_offer) OR $item['offers_other_total'] != 1)): ?>
            <div class="cd-seller-desc cd-flyout-btn" data-flyout_code="seller">
                <?php $offers_descl = Arr::get($cmslabel, 'offers_descl'); ?>
                <?php if($item['offers_other_total'] == 1): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_1_descl'); ?>
                <?php elseif($item['offers_other_total'] == 2): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_2_descl'); ?>
                <?php elseif($item['offers_other_total'] == 3 || $item['offers_other_total'] == 4): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_3_descl'); ?>
                <?php endif; ?>
                <?php echo str_replace(['%offers_total%','%offers_descl%','%price%'], [$item['offers_other_total'] ?? 0, $offers_descl, $first_other_offer['price_custom'] ?? 0], Arr::get($cmslabel, 'seller_others')); ?>
                <?php $first_other_offer_shipping = $first_other_offer['shipping_options'][$first_other_offer['shipping_type']]['shipping_price'] ?? 0; ?>
                <?php /* ?>
                <?php if (!empty($first_other_offer_shipping)): ?>
                    <?php echo Utils::currency_format($first_other_offer_shipping * $currency['exchange'], $currency['display']); ?> <?php echo Arr::get($cmslabel, 'shipping_label'); ?>
                <?php else: ?>
                    <?php echo Arr::get($cmslabel, 'shipping_free_label'); ?>
                <?php endif; ?>
                <?php */ ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>

<?php if ($first_main_seller_offer): ?>
    <div class="cd-seller-item special">
        <div class="cd-seller-item-col1">
            <div class="cd-seller-item-header">
                <div class="cd-seller-item-title"><?php echo Arr::get($cmslabel, 'seller_item_title'); ?> <a href="<?php echo $first_main_seller_offer['seller_url']; ?>"><?php echo $first_main_seller_offer['seller_title']; ?></a></div>
                <?php /* ?>
                <div class="cd-seller-item-rate">
                    <span class="cp-rate rates-container add_rate" data-rates="0.0000" data-rates_votes="0" data-rates_sum="0" title="" data-rates_stars="1"><span data-score="1" class="icon-star-empty"></span><span data-score="2" class="icon-star-empty"></span><span data-score="3" class="icon-star-empty"></span><span data-score="4" class="icon-star-empty"></span><span data-score="5" class="icon-star-empty"></span></span>
                </div>
                <?php */ ?>
            </div>
            <div class="cd-seller-item-info">
                <div class="cd-seller-item-price"><?php echo Utils::currency_format($first_main_seller_offer['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                <?php /* ?>
                <div class="cd-seller-item-condition"><span><?php echo Arr::get($cmslabel, 'condition_' . $first_main_seller_offer['product_condition']); ?></span></div>
                <?php */ ?>
            </div>
            <?php
            $item_offer_shipping = $first_main_seller_offer['shipping_options'][$first_main_seller_offer['shipping_type']] ?? [];

            $item_offer_shipping_date_original = ($item['status'] == '5') ? $first_main_seller_offer['date_available'] : Arr::get($item_offer_shipping, 'min_delivery_date');
            if (!empty($item_offer_shipping_date_original)) {
                $shipping_date_day = date('w', $item_offer_shipping_date_original);
                $shipping_date_month = (date('n', $item_offer_shipping_date_original) - 1);

                if (!empty($calendar_days[$shipping_date_day])) {
                    $item_offer_shipping_date = $calendar_days[$shipping_date_day] . ', ' . date('d.m.', $item_offer_shipping_date_original);
                } else {
                    $item_offer_shipping_date = date('d.m.', $item_offer_shipping_date_original);
                }
            }
            ?>
            <?php if (!empty($item_offer_shipping_date)): ?>
                <div class="cd-seller-item-shipping">
                    <?php echo str_replace('%t%', $item_offer_shipping_date, Arr::get($cmslabel, 'seller_time_of_delivery')); ?>
                    <?php /* ?>
                    <?php if (isset($item_offer_shipping['shipping_price'])): ?> - <?php echo Utils::currency_format($item_offer_shipping['shipping_price'] * $currency['exchange'], $currency['display']); ?> <?php echo Arr::get($cmslabel, 'shipping_label'); ?><?php endif; ?>
                    <?php */ ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="cd-seller-item-btn btn" data-offer_select_shopping_cart_code="<?php echo $first_main_seller_offer['shopping_cart_code']; ?>">
            <span class="selected"><?php echo Arr::get($cmslabel, 'seller_selected'); ?></span>
            <span class="unselected"><?php echo Arr::get($cmslabel, 'seller_select'); ?></span>
        </div>
    </div>
<?php endif; ?>

<?php echo View::factory('catalog/widget_detail/flyout_seller', array('item' => $item)); ?>
