<?php
$is_loyalty = (!empty($user->loyalty_code));
$installments_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['regular'])) ? reset($item['installments_calculation']['regular']) : 0 * $currency['exchange'], $currency['display']);
$installment_price = Arr::get($item, 'installments_calculation')['regular'];
$installment_loyalty_price = Arr::get($item, 'installments_calculation')['loyalty'];
if (is_array($installment_price)) {
    $installment_price = reset($installment_price);
}
if (is_array($installment_loyalty_price)) {
    $installment_loyalty_price = reset($installment_loyalty_price);
}
if (!empty($installment_loyalty_price) AND $is_loyalty) {
    $installment_price = $installment_loyalty_price;
}
?>
<?php if(!empty($installment_price) OR (!empty($installments_min_price) AND (!empty($item['installments_calculation']['regular']) OR !empty($item['installments_calculation']['regular'])))): ?>
    <div class="cd-installment-price-container">
        <div class="cd-installment-price<?php if (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?> el-discount<?php elseif($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?> el-loyalty<?php endif; ?>">
            <?php echo (!empty($installment_price)) ? str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'installments_price_text')) : ""; ?>
        </div>
        <?php if (!empty($installments_min_price) AND (!empty($item['installments_calculation']['regular']) OR !empty($item['installments_calculation']['regular']))): ?>
            <div class="cd-installments-calculate<?php if(!empty($installment_price)): ?> special<?php endif; ?>">
                <span class="cd-installments-calculate-label cd-flyout-btn" data-flyout_code="installments"><?php echo Arr::get($cmslabel, 'installments_calculate'); ?></span>

                <div class="cd-installments-calculate-flyout cd-flyout" data-flyout="installments">
                    <div class="cd-flyout-close"></div>
                    <div class="cd-flyout-content">
                        <div class="cd-flyout-header">
                            <div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'installments_flyout_title'); ?></div>
                        </div>
                        <?php if (!empty($item['installments_list_data']['payment_logo']) OR !empty($item['installments_list_data']['payment_title'])): ?>
                            <div class="cd-installments-calculate-header">
                                <?php if (!empty($item['installments_list_data']['payment_title'])): ?>
                                    <span class="cd-installments-calculate-title"><?php echo $item['installments_list_data']['payment_title']; ?></span>
                                <?php endif; ?>
                                <?php if (!empty($item['installments_list_data']['payment_logo'])): ?>
                                    <span class="cd-installments-calculate-logo">
                                                            <?php $fileExtension = pathinfo($item['installments_list_data']['payment_logo'], PATHINFO_EXTENSION); ?>
                                        <?php if($fileExtension == 'svg'): ?>
                                            <img loading="lazy" width="105" height="30" src="<?php echo Utils::file_url($item['installments_list_data']['payment_logo']); ?>" alt="">
                                        <?php else: ?>
                                            <img loading="lazy" <?php echo Thumb::generate($item['installments_list_data']['payment_logo'], array('width' => 105, 'height' => 30, 'html_tag' => TRUE)); ?> alt="" />
                                        <?php endif; ?>
                                                        </span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <?php $installments_loop = $item['installments_calculation']['regular']; ?>
                        <?php if($is_loyalty AND !empty($item['installments_calculation']['loyalty']) AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
                            <?php $installments_loop = $item['installments_calculation']['loyalty']; ?>
                        <?php endif; ?>
                        <?php if($installments_loop): ?>
                            <span class="cd-installments-calculate-table">
                                                    <?php foreach ($installments_loop as $rate => $rate_price): ?>
                                                        <div class="cd-installments-calculate-table-row"><?php echo str_replace(['%RATE%', '%RATE_PRICE%'],[$rate, Utils::currency_format($rate_price, '%s')], Arr::get($cmslabel, 'installment_single_rate')); ?></div>
                                                    <?php endforeach; ?>
                                                </span>
                        <?php endif; ?>
                        <?php if (!empty($item['installments_list_data']['payment_description'])): ?>
                            <div class="cd-installments-calculate-desc">
                                <?php echo $item['installments_list_data']['payment_description']; ?>
                            </div>
                        <?php else: ?>
                            <div class="cd-installments-calculate-desc">
                                <?php echo Arr::get($cmslabel, 'informativni_izracun'); ?>
                            </div>
                        <?php endif; ?>
                        <div class="cd-flyout-bottom">
                            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>