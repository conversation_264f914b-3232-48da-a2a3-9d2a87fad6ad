<div class="cd-seller-flyout cd-flyout" data-flyout="seller">
    <div class="cd-flyout-close"></div>
    <div class="cd-flyout-content">
        <div class="cd-flyout-header">
            <div class="cd-flyout-title">
                <?php $offers_descl = Arr::get($cmslabel, 'offers_descl'); ?>
                <?php if($item['offers_other_total'] == 1): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_1_descl'); ?>
                <?php elseif($item['offers_other_total'] == 2): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_2_descl'); ?>
                <?php elseif($item['offers_other_total'] == 3 || $item['offers_other_total'] == 4): ?>
                    <?php $offers_descl = Arr::get($cmslabel, 'offers_3_descl'); ?>
                <?php endif; ?>
                <?php echo str_replace(['%s%', '%offers_descl%'], [$item['offers_other_total'], $offers_descl], Arr::get($cmslabel, 'seller_flyout_title')); ?>
            </div>
        </div>
        <?php if(!empty($item['offers'])): ?>
            <?php foreach ($item['offers'] as $item_offer): ?>
                <div class="cd-seller-item <?php if ($item_offer['id'] == $item['offer_id']): ?>active<?php endif; ?>" data-offer_code="<?php echo $item_offer['shopping_cart_code']; ?>">
                    <div class="cd-seller-item-col1">
                        <div class="cd-seller-item-header">
                            <div class="cd-seller-item-title"><?php echo Arr::get($cmslabel, 'seller_item_title'); ?> <a href="<?php echo $item_offer['seller_url']; ?>"><?php echo $item_offer['seller_title']; ?></a></div>
                            <?php /* ?>
                            <div class="cd-seller-item-rate">
                                <span class="cp-rate rates-container add_rate" data-rates="0.0000" data-rates_votes="0" data-rates_sum="0" title="" data-rates_stars="1"><span data-score="1" class="icon-star-empty"></span><span data-score="2" class="icon-star-empty"></span><span data-score="3" class="icon-star-empty"></span><span data-score="4" class="icon-star-empty"></span><span data-score="5" class="icon-star-empty"></span></span>
                            </div>
                            <?php */ ?>
                        </div>
                        <div class="cd-seller-item-info">
                            <div class="cd-seller-item-price"><?php echo Utils::currency_format($item_offer['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                            <?php /* ?>
                            <div class="cd-seller-item-condition"><span><?php echo Arr::get($cmslabel, 'condition_' . $item_offer['product_condition']); ?></span></div>
                            <?php */ ?>
                        </div>
                        <?php
                        $item_offer_shipping = $item_offer['shipping_options'][$item_offer['shipping_type']] ?? [];

                        $item_offer_shipping_date_original = ($item['status'] == '5') ? $item_offer['date_available'] : Arr::get($item_offer_shipping, 'min_delivery_date');
                        if ($item_offer_shipping_date_original) {
                            $shipping_date_day = date('w', $item_offer_shipping_date_original);
                            $shipping_date_month = (date('n', $item_offer_shipping_date_original) - 1);

                            if (!empty($calendar_days[$shipping_date_day])) {
                                $item_offer_shipping_date = $calendar_days[$shipping_date_day] . ', ' . date('d.m.', $item_offer_shipping_date_original);
                            } else {
                                $item_offer_shipping_date = date('d.m.', $item_offer_shipping_date_original);
                            }
                        }
                        ?>
                        <?php if (!empty($item_offer_shipping_date)): ?>
                            <div class="cd-seller-item-shipping">
                                <?php echo str_replace('%t%', $item_offer_shipping_date, Arr::get($cmslabel, 'seller_time_of_delivery')); ?>
                                 <?php /* ?>
                                <?php if (isset($item_offer_shipping['shipping_price'])): ?> - <strong><?php echo Utils::currency_format($item_offer_shipping['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong> <?php echo Arr::get($cmslabel, 'shipping_label'); ?><?php endif; ?>
                                 <?php */ ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="cd-seller-item-btn btn" data-offer_select_shopping_cart_code="<?php echo $item_offer['shopping_cart_code']; ?>" data-offer_select_seller_id="<?php echo $item_offer['seller_id']; ?>">
                        <span class="selected"><?php echo Arr::get($cmslabel, 'seller_selected'); ?></span>
                        <span class="unselected"><?php echo Arr::get($cmslabel, 'seller_select'); ?></span>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        <div class="cd-flyout-bottom special">
            <div class="cd-flyout-close-btn btn btn-lightBlue"><?php echo Arr::get($cmslabel, 'flyout_close_confirm'); ?></div>
        </div>
    </div>
</div>