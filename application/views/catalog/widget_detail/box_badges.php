<?php
$badges_special_2 = (!empty($item['badges_special_2'])) ? $item['badges_special_2'] : [];
unset($badges_special_2[120638]);
$badge_coupon = (!empty($item['badge_coupon'])) ? $item['badge_coupon'] : [];
?>

<?php $badge_count = 0; ?>
<?php foreach($badges_special_2 as $badge_special): ?>
    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
        <?php $badge_count++; ?>
    <?php endif; ?>
<?php endforeach; ?>

<?php if($badge_count > 0 AND (!empty($badges_special_2) OR !empty($badge_coupon))): ?>
    <div class="cd-badges-m cd-flyout-btn" data-flyout_code="badges-mobile">
        <div class="cd-badges-m-title"><?php echo Arr::get($cmslabel, 'badges_mobile_title'); ?></div>
        <div class="cd-badges-m-images">
            <?php $badges_qty = 0; ?>
            <?php $badges_image_count = 0; ?>
            <?php foreach($badges_special_2 as $badge_special): ?>
                <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                    <?php if($badges_image_count < 3): ?>
                        <?php if(!empty($badge_special['gift_image'])): ?>
                            <div class="cd-badges-m-img">
                                <?php $fileExtension2 = pathinfo($badge_special['gift_image'], PATHINFO_EXTENSION); ?>
                                <?php if($fileExtension2 == 'svg'): ?>
                                    <img loading="lazy" width="44" height="44" src="<?php echo Utils::file_url($badge_special['gift_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                <?php else: ?>
                                    <img loading="lazy" <?php echo Thumb::generate($badge_special['gift_image'], array('width' => 44, 'height' => 44, 'html_tag' => TRUE, 'srcset' => '88r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                <?php endif; ?>
                            </div>
                            <?php $badges_image_count++; ?>
                        <?php elseif(!empty($badge_special['badge_image'])): ?>
                            <div class="cd-badges-m-img">
                                <?php $fileExtension2 = pathinfo($badge_special['badge_image'], PATHINFO_EXTENSION); ?>
                                <?php if($fileExtension2 == 'svg'): ?>
                                    <img loading="lazy" width="44" height="44" src="<?php echo Utils::file_url($badge_special['badge_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                <?php else: ?>
                                    <img loading="lazy" <?php echo Thumb::generate($badge_special['badge_image'], array('width' => 44, 'height' => 44, 'html_tag' => TRUE, 'srcset' => '88r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                <?php endif; ?>
                            </div>
                            <?php $badges_image_count++; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php $badges_qty++; ?>
                <?php endif; ?>
            <?php endforeach; ?>

            <?php $badges_count = $badges_qty + count($badge_coupon) - $badges_image_count; ?>
            <?php if($badges_count > 0): ?>
                <div class="cd-badges-m-extra">+<?php echo $badges_count; ?></div>
            <?php endif; ?>
        </div>
    </div>

    <div class="cd-badges-items-m cd-flyout" data-flyout="badges-mobile">
        <div class="cd-flyout-close"></div>
        <div class="cd-flyout-content">
            <div class="cd-flyout-header">
                <div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'badges_mobile_flyout_title'); ?></div>
            </div>
            <?php if (!empty($badges_special_2)): ?>
                <?php foreach($badges_special_2 as $badge_special): ?>
                    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                        <div class="cd-badge-item-m">
                            <?php if(!empty($badge_special['gift_image'])): ?>
                                <div class="cd-badge-item-img">
                                    <?php $fileExtension2 = pathinfo($badge_special['gift_image'], PATHINFO_EXTENSION); ?>
                                    <?php if($fileExtension2 == 'svg'): ?>
                                        <img loading="lazy" width="50" height="50" src="<?php echo Utils::file_url($badge_special['gift_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                    <?php else: ?>
                                        <img loading="lazy" <?php echo Thumb::generate($badge_special['gift_image'], array('width' => 50, 'height' => 50, 'html_tag' => TRUE, 'srcset' => '100r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                    <?php endif; ?>
                                </div>
                            <?php elseif(!empty($badge_special['badge_image'])): ?>
                                <div class="cd-badge-item-img">
                                    <?php $fileExtension2 = pathinfo($badge_special['badge_image'], PATHINFO_EXTENSION); ?>
                                    <?php if($fileExtension2 == 'svg'): ?>
                                        <img loading="lazy" width="50" height="50" src="<?php echo Utils::file_url($badge_special['badge_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                    <?php else: ?>
                                        <img loading="lazy" <?php echo Thumb::generate($badge_special['badge_image'], array('width' => 50, 'height' => 50, 'html_tag' => TRUE, 'srcset' => '100r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                                <div class="cd-badge-item-cnt-m">
                                    <?php if(!empty($badge_special['title'])): ?>
                                        <div class="cd-badge-item-title-m">
                                            <?php echo strip_tags(($badge_special['title'])); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(!empty($badge_special['short_description'])): ?><?php echo($badge_special['short_description']); ?><?php endif; ?>
                                    <?php if(!empty($badge_special['content'])): ?><?php echo($badge_special['content']); ?><?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php if (!empty($badge_coupon)): ?>
                <?php foreach($badge_coupon as $badge_coupon_item): ?>
                    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                        <div class="cd-badge-item-m">
                            <div class="cd-badge-item-cnt">
                                <div class="cd-badge-item-title-m">
                                    <?php echo str_replace([
                                        '%discount%',
                                        '%coupon_code%',
                                        '%active_to%',
                                    ], [
                                        Arr::get($badge_coupon_item, 'coupon_discount_percent', '-').' %',
                                        Arr::get($badge_coupon_item, 'coupon_code', '-'),
                                        (!empty($badge_coupon_item['coupon_active_to'])) ? date('d.m.Y', $badge_coupon_item['coupon_active_to']) : '-',
                                    ], Arr::get($cmslabel, 'badge_coupon')) ?>
                                    </div>
                                <?php if(!empty($badge_coupon_item['short_description'])): ?><?php echo($badge_coupon_item['short_description']); ?><?php endif; ?>
                                <?php if(!empty($badge_coupon_item['content'])): ?><?php echo($badge_coupon_item['content']); ?><?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
            <div class="cd-flyout-bottom">
                <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
            </div>
        </div>
    </div>

    <?php if($info['user_device'] != 'm'): ?>
        <div class="cd-badges-items">
            <?php if (!empty($badges_special_2)): ?>
                <?php foreach($badges_special_2 as $badge_special): ?>
                    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                        <div class="cd-badge-item" id="<?php echo $badge_special['code']; ?>">
                            <?php if(!empty($badge_special['gift_image'])): ?>
                                <div class="cd-badge-item-img">
                                    <?php $fileExtension2 = pathinfo($badge_special['gift_image'], PATHINFO_EXTENSION); ?>
                                    <?php if($fileExtension2 == 'svg'): ?>
                                        <img loading="lazy" width="54" height="54" src="<?php echo Utils::file_url($badge_special['gift_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                    <?php else: ?>
                                        <img loading="lazy" <?php echo Thumb::generate($badge_special['gift_image'], array('width' => 54, 'height' => 54, 'html_tag' => TRUE, 'srcset' => '108r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                    <?php endif; ?>
                                </div>
                            <?php elseif(!empty($badge_special['badge_image'])): ?>
                                <div class="cd-badge-item-img">
                                    <?php $fileExtension2 = pathinfo($badge_special['badge_image'], PATHINFO_EXTENSION); ?>
                                    <?php if($fileExtension2 == 'svg'): ?>
                                        <img loading="lazy" width="54" height="54" src="<?php echo Utils::file_url($badge_special['badge_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
                                    <?php else: ?>
                                        <img loading="lazy" <?php echo Thumb::generate($badge_special['badge_image'], array('width' => 54, 'height' => 54, 'html_tag' => TRUE, 'srcset' => '108r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description'])): ?>
                                <div class="cd-badge-item-cnt">
                                    <?php if(!empty($badge_special['title'])): ?>
                                        <div class="cd-badge-item-title">
                                            <?php echo strip_tags(($badge_special['title'])); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if(!empty($badge_special['short_description'])): ?>
                                        <div class="cd-badge-item-desc text-line">
                                            <?php echo($badge_special['short_description']); ?>
                                            <?php if(!empty($badge_special['content'])): ?>
                                                <div class="cd-badge-item-link-container">
                                                    <a class="cd-badge-item-link cd-flyout-btn"<?php if(!empty($badge_special['code'])): ?> data-flyout_code="badge-<?php echo $badge_special['code']; ?>"<?php endif; ?> href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'read_more_badge'); ?></a>
                                                </div>
                                            <?php endif; ?>
                                            <div class="c-desc-btn cd-badge-item-btn text-line-btn<?php if(!empty($badge_special['content'])): ?> special<?php endif; ?>">
                                                <span class="more"><?php echo Arr::get($cmslabel, 'category_description_more'); ?></span>
                                                <span class="less"><?php echo Arr::get($cmslabel, 'category_description_less'); ?></span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php if(!empty($badge_special['content'])): ?>
                                <div class="cd-badge-flyout cd-flyout"<?php if(!empty($badge_special['code'])): ?> data-flyout="badge-<?php echo $badge_special['code']; ?>"<?php endif; ?>>
                                    <div class="cd-flyout-close"></div>
                                    <div class="cd-flyout-content">
                                        <?php echo($badge_special['content']); ?>
                                        <div class="cd-flyout-bottom">
                                            <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!empty($badge_coupon)): ?>
                <?php foreach($badge_coupon as $badge_coupon_item): ?>
                    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
                        <div class="cd-badge-item">
                            <div class="cd-badge-item-cnt">
                                <div class="cd-badge-item-title">
                                    <?php echo str_replace([
                                        '%discount%',
                                        '%coupon_code%',
                                        '%active_to%',
                                    ], [
                                        Arr::get($badge_coupon_item, 'coupon_discount_percent', '-').' %',
                                        Arr::get($badge_coupon_item, 'coupon_code', '-'),
                                        (!empty($badge_coupon_item['coupon_active_to'])) ? date('d.m.Y', $badge_coupon_item['coupon_active_to']) : '-',
                                    ], Arr::get($cmslabel, 'badge_coupon')) ?>
                                </div>
                                <?php if(!empty($badge_special['short_description'])): ?>
                                    <div class="cd-badge-item-desc">
                                        <?php echo($badge_special['short_description']); ?>
                                    </div>
                                <?php endif; ?>
                                <?php if(!empty($badge_coupon_item['content'])): ?>
                                    <div class="cd-badge-item-link-container">
                                        <a class="cd-badge-item-link cd-flyout-btn"<?php if(!empty($badge_coupon_item['code'])): ?> data-flyout_code="badge-<?php echo $badge_coupon_item['code']; ?>"<?php endif; ?> href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'read_more_badge'); ?></a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if(!empty($badge_coupon_item['content'])): ?>
                            <div class="cd-flyout cd-badge-flyout"<?php if(!empty($badge_coupon_item['code'])): ?> data-flyout="badge-<?php echo $badge_coupon_item['code']; ?>"<?php endif; ?>>
                                <div class="cd-flyout-close"></div>
                                <div class="cd-flyout-content">
                                    <?php echo($badge_coupon_item['content']); ?>
                                    <div class="cd-flyout-bottom">
                                        <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>