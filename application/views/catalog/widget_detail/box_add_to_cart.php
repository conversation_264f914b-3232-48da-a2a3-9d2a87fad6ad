<?php if ($item['is_available'] AND !empty($item['services']) AND !in_array($item['type'], ['configurable'])): ?>
    <div class="cd-services-section">
        <div class="cd-services-title"><?php echo Arr::get($cmslabel, 'services_title'); ?></div>
        <?php foreach ($item['services'] AS $item_service): ?>
            <?php if (empty($item_service['items_available'])) {continue;} ?>
            <div class="cd-service cd-flyout-btn <?php echo $item_service['code']; ?>" data-flyout_code="benefit-<?php echo $item_service['code']; ?>">
                <div class="cd-service-title <?php echo $item_service['code']; ?>">
                    <span class="unselected"><?php echo $item_service['title']; ?></span>
                    <?php if ($item_service['type'] == 's'): ?>
                        <span class="selected"><?php echo Arr::get($cmslabel, 'service_title_zavarovanje_selected'); ?></span>
                    <?php elseif ($item_service['type'] == 'c'): ?>
                        <span class="selected"><?php echo Arr::get($cmslabel, 'service_title_storitve_selected'); ?></span>
                    <?php endif; ?>
                </div>
                <div class="cd-service-bid <?php echo $item_service['code']; ?>"></div>
                <?php if ($item_service['type'] == 's'): ?>
                    <div class="cd-service-note">
                        <span class="unselected"><?php echo Arr::get($cmslabel, 'service_note_zavarovanje'); ?></span>
                        <span class="selected"><?php echo Arr::get($cmslabel, 'service_note_zavarovanje_selected'); ?></span>
                    </div>
                <?php elseif ($item_service['type'] == 'c'): ?>
                    <div class="cd-service-note">
                        <span class="unselected"><?php echo Arr::get($cmslabel, 'service_note_storitve'); ?></span>
                        <span class="selected"><?php echo Arr::get($cmslabel, 'service_note_storitve_selected'); ?></span>
                    </div>
                <?php endif; ?>
            </div>
            <div class="cd-service-items <?php echo $item_service['code']; ?> cd-flyout" data-flyout="benefit-<?php echo $item_service['code']; ?>">
                <div class="cd-flyout-close"></div>
                <div class="cd-flyout-content cd-flyout-service-content">
                    <div class="cd-flyout-header">
                        <div class="cd-flyout-header-title"><?php echo $item_service['tip']; ?></div>
                    </div>
                    <?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
                        <?php if (empty($item_service_item['active'])) {continue;} ?>
                        <?php if ($item_service['type'] == 's'): ?>
                            <input class="special" type="radio" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>" />
                        <?php elseif ($item_service['type'] == 'c'): ?>
                            <input class="special" type="checkbox" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>"/>
                        <?php endif; ?>
                        <label class="cd-flyout-service-label" for="service-<?php echo $item_service_item_id; ?>">
                            <div class="cd-flyout-service-title">
                                <span class="title" data-service_extra_title="<?php echo $item_service_item['id']; ?>"><?php echo $item_service_item['title']; ?></span>
                                <span class="cd-flyout-service-price"><span data-service_extra_price="<?php echo $item_service_item['id']; ?>"><?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?></span></span>
                            </div>
                            <?php if($item_service_item['description']): ?>
                                <div class="cd-flyout-service-desc"><?php echo $item_service_item['description']; ?></div>
                            <?php endif; ?>
                        </label>
                    <?php endforeach; ?>

                    <?php if ($item_service['type'] == 's'): ?>
                        <div class="cd-flyout-service" data-service_extra_none="<?php echo $item_service['code']; ?>">
                            <div class="cd-flyout-service-top">
                                <input type="radio" name="services[]" value="" id="service-0" data-service_extra="<?php echo $item['shopping_cart_code']; ?>"  data-service_extra_category="<?php echo $item_service['code']; ?>" checked />
                                <label class="cd-flyout-service-label" for="service-0">
                                    <div class="cd-flyout-service-title" data-service_extra_title="<?php echo $item_service_item['id']; ?>">
                                        <?php echo Arr::get($cmslabel, 'no_warranty'); ?>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <?php $none_insurance = true; ?>
                    <?php endif; ?>
                </div>
                <div class="cd-flyout-bottom special">
                    <div class="cd-flyout-close-btn btn btn-lightBlue"><?php echo Arr::get($cmslabel, 'flyout_close_confirm'); ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<?php if (empty(Arr::get($item, 'temporary_unavailable')) AND $item['type'] != 'configurable'): ?>
    <!-- Add to cart section (if no variations) -->
    <?php if ($item['is_available']): ?>
        <?php if (!empty($item['show_r1_air_conditioning_reverse_tax_info'])): ?>
            <div class="cd-r1-info">
                <span><?php echo Arr::get($cmslabel, 'notification'); ?></span>
                <?php echo Arr::get($cmslabel, 'r1_air_conditioning_reverse_tax_info'); ?>
            </div>
        <?php endif; ?>

        <div class="add-to-cart-container">
            <span style="display: none;" data-product_category_title="1"><?php echo Arr::get($item, 'category_title'); ?></span>
            <span style="display: none;" data-product_manufacturer_title="1"><?php echo Arr::get($item, 'manufacturer_title'); ?></span>
            
            <!-- Compare list -->
            <?php if (isset($item['compare_widget'])): ?>
                <?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active'], 'mode' => 'cd_list']); ?>
            <?php endif; ?>

            <?php echo View::factory('catalog/widget/add_to_cart', ['item' => $item]); ?>
        </div>
    <?php else: ?>
        <div class="add-to-cart-container">
            <?php if (isset($item['feedback_notification_widget'])): ?>
                <!-- Qty notification form -->
                <?php $form_content = 'catalogproduct_'.$item['id'].'_'.$info['lang']; ?>
                <?php //if((!empty($item['max_qty_per_location'])) AND (int)$item['max_qty_per_location'] > 0): ?>
                <?php //echo View::factory('siteforms/widget/notifymelast_form', ['product_id' => Arr::get($item,'id')]); ?>
                <?php //else: ?>
                <?php if ($item['status'] == '7'): ?>
                    <?php echo View::factory('feedback/notification_form', array('form_content' => $form_content, 'mode' => 'special')); ?>
                <?php elseif($item['status'] == '5'): ?>
                    <?php echo View::factory('feedback/notification_form', array('form_content' => $form_content, 'mode' => 'preorder')); ?>
                <?php else: ?>
                    <?php echo View::factory('feedback/notification_form', array('form_content' => $form_content)); ?>
                <?php endif; ?>
                <?php //endif; ?>
            <?php else: ?>
                <!-- Simple contact form -->
                <?php echo View::factory('siteforms/widget/inquiry_form', array('subject' => Text::meta(Arr::get($cmslabel, 'inquiry_form_title', 'Upit o proizvodu').' '.$item['title'].', url: '.$info['url']))); ?>
            <?php endif; ?>
            <!-- Compare list -->
            <?php if (isset($item['compare_widget'])): ?>
                <div class="cd-compare-container-unavailable">
                    <?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active'], 'mode' => 'cd_list']); ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>