<?php
$badges_special = (!empty($item['badges'])) ? $item['badges'] : [];
$badges_special_1 = (!empty($item['badges_special_1'])) ? $item['badges_special_1'] : [];
$badges_special_2 = (!empty($item['badges_special_2'])) ? $item['badges_special_2'] : [];
unset($badges_special_2[120638]);
$badge_coupon = (!empty($item['badge_coupon'])) ? $item['badge_coupon'] : [];
?>

<?php $badge_count = 0; ?>
<?php foreach($badges_special_2 as $badge_special): ?>
    <?php if(!empty($badge_special['title']) OR !empty($badge_special['short_description']) OR !empty($badge_special['content'])): ?>
        <?php $badge_count++; ?>
    <?php endif; ?>
<?php endforeach; ?>

<?php if (empty(Arr::get($item, 'temporary_unavailable'))): ?>
    <div class="cd-badges">
        <?php
        $is_loyalty = (!empty($user->loyalty_code));

        $product_priorities = Kohana::config('app.catalog.product_priorities');
        $priority = Arr::get($product_priorities, $item['priority_2']);

        $discount_percent = round($item['discount_percent_custom'], 0);
        $price_saved = ($item['basic_price_custom'] - $item['price_custom']);
        if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom'])) {
            $discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['price_custom'])) * 100, 0);
            $price_saved = ($item['price_custom'] - $item['loyalty_price_custom']);
        } else if (($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom'])) {
            $discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['basic_price_custom'])) * 100, 0);
            $price_saved = ($item['basic_price_custom'] - $item['loyalty_price_custom']);
        }

        $priceRecommended = false;
        if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))) {
            $priceRecommended = true;
        }
        ?>

        <?php if ($priceRecommended == false AND (($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom'] AND $item['type'] != 'advanced' AND $item['type'] != 'configurable') OR ($item['discount_percent_custom'] >= 2 AND $item['type'] != 'advanced' AND $item['type'] != 'configurable'))): ?>
            <span class="cd-badge cd-badge-discount<?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?><?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom']): ?> cd-badge-loyalty<?php endif; ?>"><?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?><?php echo Arr::get($cmslabel, 'loyalty'); ?> <?php endif; ?>-<?php echo $discount_percent; ?> %</span>
        <?php endif; ?>
        <?php if ($item['type'] == 'advanced'): ?>
            <?php $item_type_advanced_selected = key($item["type_config"]); ?>
            <?php if (!empty($item["type_config"])): ?>
                <?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
                    <?php
                    $discount_percent = round($item_variation['discount_percent_custom'], 0);
                    if ($item_variation['selected_price'] == 'recommended' AND ($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom']) AND ($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['loyalty_price_custom'] < $item_variation['price_custom'])) {
                        $discount_percent = round((1 - ($item_variation['loyalty_price_custom'] / $item_variation['price_custom'])) * 100, 0);
                    } else if (($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['loyalty_price_custom'] < $item_variation['basic_price_custom'])) {
                        $discount_percent = round((1 - ($item_variation['loyalty_price_custom'] / $item_variation['basic_price_custom'])) * 100, 0);
                    }
                    ?>
                        <?php if($item_variation['price_custom'] > 0): ?>
                            <?php if ($discount_percent): ?>
                                <div data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
                                    <span class="cd-badge cd-badge-discount<?php if (!empty($item_variation['price_custom_prices_cart']) AND !empty($item_variation['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?><?php if($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['loyalty_price_custom'] < $item_variation['price_custom']): ?> cd-badge-loyalty<?php endif; ?>"><?php if($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['loyalty_price_custom'] < $item_variation['basic_price_custom']): ?><?php echo Arr::get($cmslabel, 'loyalty'); ?> <?php endif; ?>-<?php echo $discount_percent; ?> %</span>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>
        <?php if ($priority AND ($priority['code'] != 'action')): ?>
            <span class="cd-badge cd-badge-<?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>
        <?php endif; ?>
        <?php foreach($badges_special_1 as $badge): ?>
            <?php if(!empty($badge['label_title'])): ?>
                <span class="cd-badge cd-badge-<?php echo($badge['code']); ?><?php if($badge_count > 0 AND (!empty($badges_special_2) OR !empty($badge_coupon))): ?> cd-flyout-btn<?php endif; ?><?php if($badge['category'] == 1): ?> cd-badge-preorder<?php endif; ?>"<?php if($badge_count > 0 AND (!empty($badges_special_2) OR !empty($badge_coupon))): ?> data-flyout_code="badges-mobile"<?php endif; ?>><?php echo($badge['label_title']); ?></span>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
