<div class="cd-item-status">
    <?php if (!empty($item['status'])): ?>
        <?php if($item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
            <div class="cd-available-qty<?php if(!empty($item['shipping_options']['p'])): ?> special<?php endif; ?>">
                <span><?php echo Arr::get($cmslabel, 'na_zalogi_hitri_prevzem_pdp'); ?></span>
            </div>
        <?php else: ?>
            <?php if($item['status'] == 1): ?>
                <div class="cd-available-qty<?php if(!empty($item['shipping_options']['p'])): ?> special<?php endif; ?>">
                    <?php if ($item['last_piece_sale'] AND $item['warehouses_single_pickup_display']): ?>
                        <span class="available-last"><?php echo Arr::get($cmslabel, 'odprodaja'); ?></span>
                    <?php else: ?>
                        <span><?php echo Arr::get($cmslabel, 'na_zalogi'); ?></span>
                    <?php endif; ?>
                </div>
            <?php elseif($item['status'] == 2): ?>
                <div class="cd-available-qty">
                    <span><?php echo str_replace(['%MIN_DAY%','%MAX_DAY%'], [$item['availability_info']['min_days'] ?? 0, $item['availability_info']['max_days'] ?? 0], Arr::get($cmslabel, 'na_zalogi_dobavitelja')); ?></span>
                </div>
            <?php elseif($item['status'] == 4): ?>
                <div class="cd-available-qty<?php if(!empty($item['shipping_options']['p'])): ?> special<?php endif; ?>">
                    <span class="available-last"><?php echo Arr::get($cmslabel, 'na_zalogi_ena'); ?></span>
                </div>
            <?php elseif($item['status'] == 5): ?>
                <div class="cd-available-qty">
                    <?php if(!empty($item['is_available'])): ?>
                        <?php if (!empty($item['date_available'])): ?>
                            <span><?php echo Arr::get($cmslabel, 'na_voljo'); ?> <?php echo (strpos($item['date_available'], '.') !== false) ? $item['date_available'] : date('d.m.Y', $item['date_available']); ?></span>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi_preorder'); ?></span>
                    <?php endif; ?>
                </div>
            <?php elseif($item['status'] == 7): ?>
                <div class="cd-available-qty">
                    <span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi'); ?></span>
                </div>
            <?php elseif($item['status'] == 9): ?>
                <div class="cd-available-qty">
                    <span class="unavailable"><?php echo Arr::get($cmslabel, 'dalj_ni_na_zalogi'); ?></span>
                </div>
            <?php endif; ?>
        <?php endif; ?> 
    <?php endif; ?>
    
    <?php if(!empty($item['shipping_options']['p']) AND ($item['status'] == 1 OR $item['status'] == '4')): ?>
        <div class="cd-item-status-stores cd-flyout-btn" data-flyout_code="stores">
            <?php echo Arr::get($cmslabel, 'warehouses_availability_status'); ?>
        </div>
    <?php endif; ?>
</div>