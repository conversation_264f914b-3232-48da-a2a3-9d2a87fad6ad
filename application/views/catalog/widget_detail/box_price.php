<?php
if (!empty($item['type']) AND $item['type'] == 'configurable') {
    $item_type_config = Arr::get($item, 'item_type_config');

    $primary_product = [];
    $primary_product_attributes = [];
    if (!empty($item['primary_product_data']['id']) AND !empty($item_type_config['product_data'][$item['primary_product_data']['id']])) {
        $primary_product = $item_type_config['product_data'][$item['primary_product_data']['id']];
        $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
    }

    if (!empty($item_type_config['attribute_data']) AND !empty($item_type_config['product_data'])) {
        $selected_product_code = Arr::get($_GET, 'search_q');
        if (!empty($selected_product_code)) {
            foreach ($item_type_config['product_data'] AS $product_data_id => $product_data) {
                if (!empty($product_data['code']) AND $product_data['code'] == $selected_product_code) {
                    $primary_product = $product_data;
                    $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
                }
            }
        }

        // select first available variation
        if (empty($primary_product_attributes)) {
            foreach ($item_type_config['product_data'] AS $product_data_id => $product_data) {
                $primary_product = $product_data;
                $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
                break;
            }
        }
    }

    if (!empty($primary_product['basic_price_custom']) AND !empty($primary_product['price_custom'])) {
        $item['basic_price_custom'] = $primary_product['basic_price_custom'];
        $item['price_custom'] = $primary_product['price_custom'];
    }
}

$is_loyalty = (!empty($user->loyalty_code));
$installment_price = Arr::get($item, 'installments_calculation')['regular'];
$installment_loyalty_price = Arr::get($item, 'installments_calculation')['loyalty'];
if (is_array($installment_price)) {
    $installment_price = reset($installment_price);
}
if (is_array($installment_loyalty_price)) {
    $installment_loyalty_price = reset($installment_loyalty_price);
}
if (!empty($installment_loyalty_price) AND $is_loyalty) {
    $installment_price = $installment_loyalty_price;
}

$badge_uau = (Kohana::$environment === 1) ? 447785 : 1299393;
$badge_uau_exist = false;

if(!empty($item['badges'])) {
    foreach ($item['badges'] as $badge) {
        if ($badge['code'] == $badge_uau) {
            $badge_uau_exist = true;
            break;
        }
    }
}
?>

<div class="cd-price-container">
    <?php if (!empty($item['is_available']) AND !empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>
        <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom_prices_cart'] * $currency['exchange'], $currency['display']); ?></span>
        <span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">action</span>
    <?php elseif ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price']): ?>
        <span style="display: none;" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
        <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
        <span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">loyalty</span>
    <?php else: ?>
        <span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
        <?php if (!in_array($item['type'], ['advanced', 'configurable']) AND ((!empty($item['discount_percent']) AND $item['discount_percent'] > 0) OR $item['price_custom'] < $item['basic_price'])): ?>
            <span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">action</span>
        <?php endif; ?>
    <?php endif; ?>
    
    <?php if (empty(Arr::get($item, 'temporary_unavailable'))): ?>
        <!-- Product prices -->
        <?php if($item['price_custom'] > 0): ?>
            <div class="cd-price<?php if(!empty($badge_uau_exist)): ?> uau-badge<?php endif; ?><?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?>">
                <?php if ($item['type'] == 'advanced'): ?>
                    <?php $item_type_advanced_selected = key($item["type_config"]); ?>
                    <div class="cd-current-price" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                    <?php if (!empty($item["type_config"])): ?>
                        <?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
                            <div data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
                                <?php if($item_variation['price_custom'] > 0): ?>
                                    <?php if ($item_variation['selected_price'] == 'recommended' AND ($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom']) AND ((empty($item_variation['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['price_custom'] < $item_variation['loyalty_price_custom']))): ?>
                                        <div class="cd-old-price special"<?php if(!$is_loyalty): ?> data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>>
                                            <span><?php echo Utils::currency_format($item_variation['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                                            <span class="cd-old-price-icon">
                                                <span class="cd-old-price-tooltip">
                                                    <span class="cd-old-price-tooltip-close"></span>
                                                    <?php echo Arr::get($cmslabel, 'permanently_low_price_tooltip'); ?>
                                                </span>
                                            </span>
                                        </div>
                                        <div class="cd-price-label red"><?php echo Arr::get($cmslabel, 'permanently_low_price'); ?></div>
                                        <div class="cd-current-price red" <?php if (!empty($item_variation['price_custom_prices_cart']) AND !empty($item_variation['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item_variation['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                                    <?php elseif (($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom']) AND ((empty($item_variation['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['price_custom'] < $item_variation['loyalty_price_custom']))): ?>
                                        <div class="cd-old-price line-through"<?php if(!$is_loyalty OR empty($item_variation['loyalty_price_custom'])): ?> data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item_variation['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                                        <div class="cd-current-price red" <?php if (!empty($item_variation['price_custom_prices_cart']) AND !empty($item_variation['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>>
                                            <?php echo Utils::currency_format($item_variation['price_custom'] * $currency['exchange'], $currency['display']); ?>
                                            <!-- Discount badge -->
                                            <?php if ($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom']): ?>
                                                <div class="cd-badge-discount-info"><?php echo Arr::get($cmslabel, 'prihranek'); ?>&nbsp; <strong><?php echo Utils::currency_format($item_variation['basic_price_custom'] - $item_variation['price_custom'] * $currency['exchange'], $currency['display']); ?></strong></div>
                                            <?php endif; ?>
                                        </div>
                                    <?php elseif($is_loyalty AND !empty($item_variation['loyalty_price_custom']) AND $item_variation['loyalty_price_custom'] < $item_variation['basic_price_custom']): ?>
                                        <div class="cd-old-price line-through">
                                            <?php if ($item_variation['selected_price'] == 'recommended' AND ($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom'])): ?>
                                                <?php echo Utils::currency_format($item_variation['price_custom'] * $currency['exchange'], $currency['display']); ?>
                                            <?php else: ?>
                                                <?php echo Utils::currency_format($item_variation['basic_price_custom'] * $currency['exchange'], $currency['display']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="cd-price-label"><?php echo Arr::get($cmslabel, 'klubska_cena'); ?></div>
                                        <div class="cd-current-price blue" <?php if (!empty($item_variation['price_custom_prices_cart']) AND !empty($item_variation['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>>
                                            <?php echo Utils::currency_format($item_variation['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
                                            <!-- Discount badge -->
                                            <?php if ($item_variation['loyalty_price_custom'] < $item_variation['basic_price_custom']): ?>
                                                <div class="cd-badge-discount-info blue"><?php echo Arr::get($cmslabel, 'klubski_prihranek'); ?>&nbsp;
                                                    <?php if ($item_variation['selected_price'] == 'recommended' AND ($item_variation['discount_percent_custom'] > 0 OR $item_variation['price_custom'] < $item_variation['basic_price_custom'])): ?>
                                                        <strong><?php echo Utils::currency_format($item_variation['price_custom'] - $item_variation['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></strong>
                                                    <?php else: ?>
                                                        <strong><?php echo Utils::currency_format($item_variation['basic_price_custom'] - $item_variation['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></strong>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="cd-current-price" <?php if (!empty($item_variation['price_custom_prices_cart']) AND !empty($item_variation['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item_variation['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item_variation['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                                    <?php endif; ?>
                                    <?php echo View::factory('catalog/widget_detail/box_price_installments', array('item' => $item_variation)); ?>
                                <?php else: ?>
                                    <span class="cd-price-zero"><?php echo Arr::get($cmslabel, 'price_zero'); ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php else: ?>
                    <?php if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
                        <div class="cd-old-price special"<?php if(!$is_loyalty): ?> data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
                            <span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                            <span class="cd-old-price-icon">
                                <span class="cd-old-price-tooltip">
                                    <span class="cd-old-price-tooltip-close"></span>
                                    <?php echo Arr::get($cmslabel, 'permanently_low_price_tooltip'); ?>
                                </span>
                            </span>
                        </div>
                        <div class="cd-price-label red"><?php echo Arr::get($cmslabel, 'permanently_low_price'); ?></div>
                        <div class="cd-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                    <?php elseif (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
                        <div class="cd-old-price line-through"<?php if(!$is_loyalty OR empty($item['loyalty_price_custom'])): ?> data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                        <div class="cd-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
                            <?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
                            <!-- Discount badge -->
                            <?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
                                <div class="cd-badge-discount-info"><?php echo Arr::get($cmslabel, 'prihranek'); ?>&nbsp; <strong><?php echo Utils::currency_format($item['basic_price_custom'] - $item['price_custom'] * $currency['exchange'], $currency['display']); ?></strong></div>
                            <?php endif; ?>
                        </div>
                    <?php elseif($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
                        <div class="cd-old-price line-through">
                            <?php if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
                                <?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
                            <?php else: ?>
                                <?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?>
                            <?php endif; ?>
                        </div>
                        <div class="cd-price-label"><?php echo Arr::get($cmslabel, 'klubska_cena'); ?></div>
                        <div class="cd-current-price blue" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
                            <?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
                            <!-- Discount badge -->
                            <?php if ($item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
                                <div class="cd-badge-discount-info blue"><?php echo Arr::get($cmslabel, 'klubski_prihranek'); ?>&nbsp; 
                                    <?php if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
                                        <strong><?php echo Utils::currency_format($item['price_custom'] - $item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></strong>
                                    <?php else: ?>
                                        <strong><?php echo Utils::currency_format($item['basic_price_custom'] - $item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></strong>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="cd-current-price" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                    <?php endif; ?>
                    <?php echo View::factory('catalog/widget_detail/box_price_installments', array('item' => $item)); ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <!-- Brand logo -->
    <?php if ($item['manufacturer_main_image']): ?>
        <div class="cd-brand">
            <a href="<?php echo($item['manufacturer_url']); ?>">
                <?php $fileExtension3 = pathinfo($item['manufacturer_main_image'], PATHINFO_EXTENSION); ?>
                <?php if($fileExtension3 == 'svg'): ?>
                    <img loading="lazy" src="<?php echo Utils::file_url($item['manufacturer_main_image']); ?>" alt="<?php echo $item['manufacturer_title']; ?>">
                <?php else: ?>
                    <img loading="lazy" <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 110, 'height' => 45, 'crop' => false, 'html_tag' => TRUE, 'srcset' => '220r 2x')); ?> alt="<?php echo Text::meta($item['manufacturer_title']); ?>" />
                <?php endif; ?>
            </a>
        </div>
    <?php endif; ?>
</div>

<?php if (!empty($item['is_available'])): ?>
    <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>
        <div class="cd-conf-price-section">
            <div class="cd-conf-price">
                <span class="cd-conf-current-price-label"><?php echo Arr::get($cmslabel, 'configured_price'); ?></span>
                <span class="cd-current-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom_prices_cart'] * $currency['exchange'], $currency['display']); ?></span>
            </div>
            <div class="cd-conf-price-timer">
                <?php echo Arr::get($cmslabel, 'conf_price_valid'); ?>
                <span class="cd-conf-price-timer-bottom">
                    <span class="countdown" data-countdown="<?php echo date('Y/m/d H:i:s', $item['price_custom_prices_cart_expire']); ?>"></span>
                    <a class="cd-conf-price-remove" href="javascript:cmswebshop.shopping_cart.set_customer_data('remove_prices_cart', '<?php echo $item['shopping_cart_code']; ?>');">remove</a>
                </span>
            </div>

            <?php if(!empty($cmslabel['conf_price_note'])): ?>
                <div class="cd-conf-price-note"><?php echo Arr::get($cmslabel, 'conf_price_note'); ?></div>
            <?php endif; ?>
        </div>
    <?php elseif (!empty($item['extra_price_cart_dynamicprice']) AND !empty((float)$item['extra_price_dynamicprice'])): ?>
        <div class="cd-conf-price-label active" data-extra_price_request="1">
            <span><?php echo Arr::get($cmslabel, 'conf_price_label'); ?></span>
            <a href="javascript:void(0);" class="cd-conf-price-btn"
               data-extra_price_item_code="<?php echo $item['shopping_cart_code']; ?>"
               data-extra_price_category="dynamicprice"
               data-extra_price_final="<?php echo $item['extra_price_dynamicprice']; ?>"
               data-extra_price_mode="<?php echo Arr::get($item, 'extra_price_mode_dynamicprice') ?>"
            ><?php echo Arr::get($cmslabel, 'conf_price'); ?>
            </a>
        </div>
        <?php if (!empty($item['extra_price_mode_dynamicprice'])): ?>
            <?php if ($item['extra_price_mode_dynamicprice'] == 'show_form_our'): ?>
                <div data-extra_price_form="show_form_our" class="cd-conf-price-label cd-dynamicprice-desc"><?php echo Arr::get($cmslabel, 'dynamicprice_desc_lowest_price'); ?></div>
            <?php elseif ($item['extra_price_mode_dynamicprice'] == 'show_form_limit'): ?>
                <div data-extra_price_form="show_form_limit" class="cd-conf-price-label cd-dynamicprice-desc"><?php echo Arr::get($cmslabel, 'dynamicprice_desc_lowest_price'); ?></div>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>

<?php if (empty(Arr::get($item, 'temporary_unavailable')) AND $item['price_custom'] > 0 AND (!empty($item['loyalty_price_custom']) OR !empty($installment_price))): ?>
    <!-- Loyalty installments -->
    <?php if ($is_loyalty AND !empty($item['loyalty_price_custom'])): ?>

    <?php else: ?>
        <?php $loyalty_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['loyalty'])) ? (float)reset($item['installments_calculation']['loyalty']) : 0 * $currency['exchange'], $currency['display']); ?>
        <?php $loyalty_price = Utils::currency_format(Arr::get($item, 'loyalty_price_custom') * $currency['exchange'], $currency['display']); ?>
        <?php if (!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
            <?php if ($user AND $is_loyalty): ?>

            <?php else: ?>
                <div class="cd-loyalty-price-container">
                    <div class="cd-loyalty-price">
                        <?php echo str_replace("%PRICE%", $loyalty_min_price, Arr::get($cmslabel, 'loyalty_price_installments')); ?>
                    </div>
                    <div class="cd-loyalty-price-button btn cd-flyout-btn" data-flyout_code="loyalty"><span><?php echo Arr::get($cmslabel, 'loyalty_join_button'); ?></span></div>
                </div>
            <?php endif; ?>
            </div>
        <?php elseif (!empty($item['loyalty_price_custom'])): ?>
            <div class="cd-loyalty-price-container">
                <?php if ($item['type'] == 'advanced'): ?>
                    <?php $item_type_advanced_selected = key($item["type_config"]); ?>
                    <div class="cd-current-price" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
                    <?php if (!empty($item["type_config"])): ?>
                        <?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
                            <div class="cd-loyalty-price" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
                                <?php if ($item_variation['price_custom'] > 0 AND $item_variation['loyalty_price_custom'] < $item_variation['basic_price_custom']): ?>
                                    <span><?php echo Arr::get($cmslabel, 'loyalty_user_price'); ?></span>
                                    <?php echo Utils::currency_format(Arr::get($item_variation, 'loyalty_price_custom') * $currency['exchange'], $currency['display']); ?>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="cd-loyalty-price">
                        <span><?php echo Arr::get($cmslabel, 'loyalty_user_price'); ?></span>
                        <?php echo $loyalty_price; ?>
                    </div>
                <?php endif; ?>
                <div class="cd-loyalty-price-button btn cd-flyout-btn" data-flyout_code="loyalty"><span><?php echo Arr::get($cmslabel, 'loyalty_join_button'); ?></span></div>
            </div>
        <?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
            <div class="cd-loyalty-price-container">
                <?php //if loyalty price is not set but another badge has installment_price=0 and this badge has visible only for loyalty flag ?>
                <div class="cd-loyalty-price">
                    <?php echo str_replace("%PRICE%", $loyalty_min_price, Arr::get($cmslabel, 'loyalty_price_installments')); ?>
                </div>
                <div class="cd-loyalty-price-button btn cd-flyout-btn" data-flyout_code="loyalty"><span><?php echo Arr::get($cmslabel, 'loyalty_join_button'); ?></span></div>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if($user AND !$is_loyalty): ?>
        <div class="cd-loyalty-flyout cd-flyout" data-flyout="loyalty">
            <div class="cd-flyout-close"></div>
            <div class="cd-flyout-content">
                <div class="cd-flyout-header">
                    <div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'loyalty_flyout_title'); ?></div>
                </div>
                <div class="cd-loyalty-flyout-list"><?php echo Arr::get($cmslabel, 'loyalty_flyout_content_user'); ?></div>
                <div class="cd-loyalty-flyout-btn"><?php echo Arr::get($cmslabel, 'loyalty_flyout_btn_user'); ?></div>
                <div class="cd-loyalty-flyout-box"><?php echo Arr::get($cmslabel, 'loyalty_flyout_box'); ?></div>
                <div class="cd-flyout-bottom">
                    <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                </div>
            </div>
        </div>
    <?php elseif(!$user): ?>
        <div class="cd-loyalty-flyout cd-flyout" data-flyout="loyalty">
            <div class="cd-flyout-close"></div>
            <div class="cd-flyout-content">
                <div class="cd-flyout-header">
                    <div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'loyalty_not_user_flyout_title'); ?></div>
                </div>
                <div class="cd-loyalty-flyout-list"><?php echo Arr::get($cmslabel, 'loyalty_flyout_content'); ?></div>
                <div class="cd-loyalty-flyout-btn"><?php echo Arr::get($cmslabel, 'loyalty_flyout_btn'); ?></div>
                <div class="cd-loyalty-flyout-box"><?php echo Arr::get($cmslabel, 'loyalty_flyout_box'); ?></div>
                <div class="cd-flyout-bottom">
                    <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>
