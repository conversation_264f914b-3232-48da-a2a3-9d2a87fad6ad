<?php
$item_type_config = [];
$configurable_attributes_filter = [];
if ($item['type'] == 'configurable') {
    $item_type_config = Arr::get($item, 'item_type_config');
    $active_variation = []; // FIXME based on Arr::get($_GET, 'search_q')
    $configurable_attributes_filter = (!empty($item_type_config)) ? Widget_Catalog::variations_attributes_filter($info['lang'], $item_type_config, $active_variation, ['configurable' => 1]) : [];

} else if (($item['type'] == 'advanced' OR $item['type'] == 'standard') AND !empty($item['parent_configurable_type_config'])) {
    $item_type_config = $item['parent_configurable_type_config'];
}

$primary_product = [];
$primary_product_attributes = [];
if (!empty($item['primary_product_data']['id']) AND !empty($item_type_config['product_data'][$item['primary_product_data']['id']])) {
    $primary_product = $item_type_config['product_data'][$item['primary_product_data']['id']];
    $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
}

if ($item['type'] == 'configurable' AND !empty($item_type_config['attribute_data']) AND !empty($item_type_config['product_data'])) {
    $selected_product_code = Arr::get($_GET, 'search_q');
    if (!empty($selected_product_code)) {
        foreach ($item_type_config['product_data'] AS $product_data_id => $product_data) {
            if (!empty($product_data['code']) AND $product_data['code'] == $selected_product_code) {
                $primary_product = $product_data;
                $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
            }
        }
    }

    // select first available variation
    if (empty($primary_product_attributes)) {
        foreach ($item_type_config['product_data'] AS $product_data_id => $product_data) {
            $primary_product = $product_data;
            $primary_product_attributes = (!empty($primary_product['configurable_attributes'])) ? array_column($primary_product['configurable_attributes'], 'id') : [];
            break;
        }
    }
}
?>

<?php if (!empty($item_type_config['attribute_data']) AND !empty($item_type_config['product_data'])): ?>
    <?php if (count($configurable_attributes_filter)): ?>
        <!-- Odabir atributa - ispis svih kombinacija (najčešće) -->
        <div data-offer_select_shopping_cart_code_configurable="1" data-offer_select_shopping_cart_code="" style="display: none;"></div>
        <div class="variations" id="variation_choices"
             data-variation_choice_method="advanced_variation"
             data-variation_default_attributes="<?php echo implode(',', $primary_product_attributes); ?>"
             data-variation_keep_attributes="1"
        >
            <?php foreach ($configurable_attributes_filter AS $filter_field => $filter_data): ?>
                <?php if ($filter_field == 'meta'): ?>
                    <?php echo $filter_data; ?>
                <?php else: ?>
                    <div class="variation variation<?php echo $filter_field; ?><?php if(count($filter_data['options_attributes']) <= 1): ?> variation-single active<?php endif; ?>" <?php if (count($filter_data['options_attributes']) > 1): ?> data-variation_choice_attribute="<?php echo $filter_field; ?>"<?php endif; ?>>
                        <input type="hidden" name="<?php echo $filter_field; ?>" />

                        <!-- Attribute title -->
                        <div class="variation-title">
                            <?php $filter_title = (Arr::get($cmslabel, 'filter_title'.$filter_field)) ? Arr::get($cmslabel, 'filter_title'.$filter_field) : Arr::get($filter_data, 'label', Arr::get($cmslabel, $filter_field, $filter_field)); ?>
                            <?php echo $filter_title; ?>: <strong class="strong"></strong>
                        </div>

                        <!-- Variation attributes list -->
                        <?php if ($filter_data['options_attributes']): ?>
                            <!-- If multiple variation attributes -->
                            <div class="variation-items variation-item<?php echo $filter_field; ?>">
                                <!-- Custom output if attribute code is _size or _color -->
                                <?php foreach ($filter_data['options_attributes'] AS $i => &$options_attributes): ?>
                                    <a rel="canonical" href="javascript:void(0);" title="<?php echo $options_attributes['title']; ?>" data-attribute_category_code="<?php echo $filter_field; ?>" data-attribute_id="<?php echo $options_attributes['id']; ?>" data-attribute_category_display="always" class="attribute variation-attribute<?php if (count($filter_data['options_attributes']) == 1): ?> active<?php endif; ?><?php if (!empty($options_attributes['image'])): ?> special<?php endif; ?> variation-button">
                                        <?php if(!empty($options_attributes['image'])): ?>
                                            <span class="img">
                                                <img id="<?php echo $options_attributes['id']; ?>" <?php echo Thumb::generate($options_attributes['image'], array('width' => 42, 'height' => 42, 'crop' => true, 'default_image' => '/media/images/no-color.jpg', 'html_tag' => true)); ?> alt="<?php echo $options_attributes['title']; ?>" title="<?php echo $options_attributes['title']; ?>" />
                                            </span>
                                        <?php elseif(!empty($options_attributes['color'])): ?>
                                            <span class="hex-color" style="background-color: <?php echo $options_attributes['color']; ?>;"></span>
                                        <?php endif; ?>
                                        <span class="title"<?php if (!empty($options_attributes['image'])): ?> style="display: none;"<?php endif; ?>><?php echo $options_attributes['title']; ?></span>
                                    </a>
                                <?php endforeach; ?>
                                <span class="variation-alert" data-attribute_alert="<?php echo $filter_field; ?>"><?php echo Arr::get($cmslabel, 'select_variation', 'Odaberi'); ?></span>
                            </div>
                        <?php else: ?>
                            <!-- If only one attribute is available -->
                            <?php foreach ($filter_data['options_attributes'] AS $i => $options_attributes): ?>
                                <a rel="canonical" href="javascript:void(0);" title="<?php echo $options_attributes['title']; ?>" data-attribute_category_code="<?php echo $filter_field; ?>" data-attribute_id="<?php echo $options_attributes['id']; ?>" data-attribute_category_display="always" class="attribute<?php if (strpos($item['attributes_ids'], ','.$options_attributes['id'].',') === false): ?> unavailable<?php endif; ?>variation-button active">
                                    <span class="title"><?php echo $options_attributes['title']; ?></span>
                                </a>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>

        <!-- Add variation to cart -->
        <div class="add-to-cart-container hidden" data-variation_box="addtocart">
            <?php foreach ($item_type_config['product_data'] as $variation_id => $variation): ?>
                <div data-variation_item_addtocart="<?php echo $variation['shopping_cart_code']; ?>" <?php if (!empty($variation['offer_shopping_cart_code'])): ?>data-variation_item_offer_trigger="<?php echo $variation['offer_shopping_cart_code']; ?>"<?php endif; ?>></div>
            <?php endforeach; ?>

            <!-- If variation is not selected -->
            <?php if (count($item_type_config['product_data']) > 1): ?>
                <div class="add-to-cart-container-var" data-variation_item_addtocart="unavailable">
                    <a class="btn btn-white cd-btn-add add_to_cart add-to-cart-disabled" href="javascript:void(0);" data-attribute_alert_display="cursor"><span><?php echo Arr::get($cmslabel, 'btn_select_variation', 'Odaberi i dodaj'); ?></span></a>
                    <?php /*<span class="variation-alert" data-attribute_alert="<?php echo $filter_field; ?>"><?php echo Arr::get($cmslabel, 'select_variation', 'Odaberi'); ?></span>*/ ?>
                </div>
            <?php endif; ?>

            <!-- If variation selection is not completed (eq. from size and color, only size is selected) -->
            <span  class="add-to-cart-container-var" data-variation_item_addtocart="0" style="display: none">
                <a class="btn btn-white cd-btn-add add_to_cart add-to-cart-disabled" href="javascript:void(0);" data-attribute_alert_display="cursor"><span><?php echo Arr::get($cmslabel, 'btn_select_variation', 'Odaberi i dodaj'); ?></span></a>
                <?php /*<span class="variation-alert" data-attribute_alert="<?php echo $filter_field; ?>"><?php echo Arr::get($cmslabel, 'select_variation', 'Odaberi'); ?></span>*/ ?>
            </span>
        </div>
    <?php endif; ?>
<?php endif; ?>

<?php if ($item['type'] == 'advanced'): ?>
    <?php if (!empty($item["type_config"])): ?>
        <?php $item_type_advanced_selected = key($item["type_config"]); ?>
        <div class="variations">
            <div class="variation">
                <div class="variation-title"><?php echo Arr::get($item, 'attribute_title')?>: <strong class="strong"></strong></div>
                <div class="variation-items variation-item_kapacitet">
                    <?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
                        <a href="javascript:setProductVariation('<?php echo $item_variation['code']; ?>', '<?php echo $item_variation['shopping_cart_code']; ?>')" data-variation_active_code="<?php echo $item_variation['code']; ?>" data-variation_shopping_cart_code="<?php echo $item_variation['shopping_cart_code']; ?>" class="attribute variation-attribute variation-size <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected == $item_variation_code): ?>active<?php endif; ?>" <?php if (!empty($item_variation['title_extra'])): ?>title="<?php echo Text::meta($item_variation['title_extra']); ?>"<?php endif; ?>>
                            <span class="title"><?php echo $item_variation['title']; ?></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>
