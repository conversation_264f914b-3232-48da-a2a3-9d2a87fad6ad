<?php if(!empty($item['shipping_options']['p'])): ?>
    <div class="cd-tab-info-body cd-tab-body-warehouse cd-flyout" data-flyout="stores">
        <div class="cd-flyout-close"></div>
        <div class="cd-flyout-content" id="cdStores">
            <div class="cd-flyout-header">
                <div class="cd-flyout-header-title">
                    <?php if ($item['status'] == '4' AND !empty($locations)): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_limited_flyout_title'); ?> 
                    <?php else: ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_flyout_title'); ?> 
                    <?php endif; ?>
                    <?php /* if(count($locations) > 0 AND empty($item['last_piece_sale'])): ?>
                        <span class="lightGreen">(<?php echo count($locations); ?>)</span>
                    <?php endif; */ ?>
                </div>
            </div>

            <?php $show_locations = false; ?>
            <?php if(!empty($locations) AND empty($item['warehouses_single_pickup_display']) AND (($item['status'] == 1 AND empty($item['last_piece_sale'])) OR $item['status'] == 4)): ?>
                <?php $show_locations = true; ?>
            <?php endif; ?>

            <?php if($show_locations == true): ?>
                <div class="cd-stores">
                    <?php foreach ($locations AS $location): ?>
                        <?php $location_available_qty = (int)$location['available_qty']; ?>
                        <article class="clear cd-store">
                            <div class="clear cd-store-row1">
                                <?php if (!empty($location['url']) AND empty($location['ignore_link_on_stock_display'])): ?>
                                    <div class="cd-store-title"><a href="<?php echo $location['url']; ?>#locationpoint-<?php echo $location['id']; ?>" target="_blank"><?php echo (!empty($location['title2']) ? $location['title2'] : $location['title']); ?></a></div>
                                <?php else: ?>
                                    <div class="cd-store-title"><?php echo (!empty($location['title2']) ? $location['title2'] : $location['title']); ?></div>
                                <?php endif; ?>
                                <?php if (!empty($location['url']) AND empty($location['ignore_link_on_stock_display'])): ?>
                                    <a href="<?php echo $location['url']; ?>#locationpoint-<?php echo $location['id']; ?>" target="_blank" class="cd-store-availability<?php if ($location_available_qty === 1): ?> last<?php elseif($location['available_qty'] > 0): ?> available<?php else: ?> unavailable<?php endif; ?>">
                                <?php else: ?>
                                    <div class="cd-store-availability<?php if ($location_available_qty === 1): ?> last<?php elseif($location['available_qty'] > 0): ?> available<?php else: ?> unavailable<?php endif; ?>">
                                <?php endif; ?>
                                    <?php if($location['code'] == 2500 AND $item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
                                        <span><?php echo Arr::get($cmslabel, 'store_available_bb_fast'); ?></span>
                                    <?php else: ?>
                                        <?php if ($location_available_qty === 0): ?>
                                            <span><?php echo Arr::get($cmslabel, 'store_not_available'); ?></span>
                                        <?php elseif ($location_available_qty === 1): ?>
                                            <span><?php echo Arr::get($cmslabel, 'store_available_last'); ?></span>
                                        <?php elseif ($location_available_qty > 0): ?>
                                            <span><?php echo Arr::get($cmslabel, 'store_available'); ?></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php if (!empty($location['url']) AND empty($location['ignore_link_on_stock_display'])): ?>
                                    </a>
                                <?php else: ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php /* if ($location['address']): ?>
                                <div class="cd-store-col cd-store-col-address"><?php echo $location['address']; ?></div>
                            <?php endif; */?>
                            <?php /* if ($location['contact_email']): ?>
                                <div class="cd-store-col cd-store-col-email"><a href="mailto:<?php echo $location['contact_email']; ?>"><?php echo $location['contact_email']; ?></a></div>
                            <?php endif;*/ ?>
                            <?php /*  if ($location['contact']): ?>
                                <div class="cd-store-col cd-store-col-tel"><?php echo $location['contact']; ?></div>
                            <?php endif; */ ?>
                        </article>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($item['status'])): ?>
                <div class="cd-store-note<?php if ($show_locations == true): ?> special<?php endif; ?>">
                    <?php if($item['status'] == 1 AND !empty($item['last_piece_sale'])): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_last_piece_sale'); ?>
                    <?php elseif(!empty($item['warehouses_single_pickup_display'])): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_pickup_only'); ?></div>
                    <?php elseif($item['status'] == 1): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_na_zalogi'); ?>
                    <?php elseif($item['status'] == 2): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_na_zalogi_dobavitelja'); ?>
                    <?php elseif($item['status'] == 4): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_na_zalogi_ena'); ?>
                    <?php elseif($item['status'] == 5): ?>
                        <?php echo Arr::get($cmslabel, 'item_stores_note_preorder'); ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if(!empty($cmslabel['item_stores_note']) AND $item['status'] == 1 AND empty($item['last_piece_sale']) AND empty($item['warehouses_single_pickup_display'])): ?>
                <div class="cd-store-note special"><?php echo Arr::get($cmslabel, 'item_stores_note'); ?></div>
            <?php endif; ?>

            <div class="cd-flyout-bottom">
                <div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
            </div>
        </div>
    </div>
<?php endif; ?>