<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php if(isset($_GET['special_view']) AND $_GET['special_view'] == 'wishlist'): ?>
	<?php $this->block('page_class'); ?> page-wishlist<?php $this->endblock('page_class'); ?>
<?php endif; ?>
<?php if($q): ?>
	<?php $this->block('page_class'); ?> page-search page-catalog-lvl2<?php $this->endblock('page_class'); ?>
	<?php $this->block('benefits_class'); ?> special flyout-page<?php $this->endblock('benefits_class'); ?>
<?php endif; ?>
<?php if($kind_content == 'manufacturer'): ?>
	<?php $this->block('page_class'); ?> page-brand page-catalog-lvl2 flyout-page<?php $this->endblock('page_class'); ?>
<?php endif; ?>
<?php if($kind_content == 'seller'): ?>
	<?php $this->block('page_class'); ?> page-catalog-lvl2 flyout-page<?php $this->endblock('page_class'); ?>
<?php endif; ?>
<?php if(!empty($kind['level']) AND $kind['level'] > 1): ?>
	<?php $this->block('page_class'); ?> page-catalog-lvl2 flyout-page<?php $this->endblock('page_class'); ?>
	<?php $this->block('page_fixed_header'); ?><?php if($info['user_device'] == 'm'): ?>50<?php else: ?>-10<?php endif; ?>
	<?php $this->endblock('page_fixed_header'); ?>
<?php endif; ?>
<?php if(empty($kind) OR $kind_content == 'list'): ?>
	<?php $this->block('page_class'); ?> page-catalog-speciallist page-catalog-lvl2 flyout-page<?php $this->endblock('page_class'); ?>
<?php endif; ?>

<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>
<?php $suggestion_categories = (!empty($search_fields['_basic']['suggestion_categories'])) ? $search_fields['_basic']['suggestion_categories'] : []; ?>

<?php $this->block('breadcrumb_section'); ?>
	<div class="bc c-bc">
		<div class="wrapper wrapper-bc">
			<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
			<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
		</div>
	</div>
<?php $this->endblock('breadcrumb_section'); ?>

<?php $this->block('main'); ?>
	<?php if($q): ?>
		<div class="s-header">
			<?php if (!empty($suggestion_categories) AND $kind_content == 'category' AND empty($kind['id'])): ?>
				<div class="s-header-categories-wrapper">
					<div class="s-header-title"><?php echo Arr::get($cmslabel, 'search_headline'); ?></div>
					<div class="s-header-categories">
						<?php foreach ($suggestion_categories AS $suggestion_category): ?>
							<a href="<?php echo $suggestion_category['url']; ?>" class="s-header-category"><?php echo $suggestion_category['title']; ?></a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>

			<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
			<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
			<?php $search_totals = Widget_Search::totals($info['lang'], $q); ?>
			<?php 
			if ((int) Arr::get($search_totals, 'catalog', 0) === 0) {
				if ((int) Arr::get($search_totals, 'publish', 0) > 0) {
					Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=publish');
				} elseif ((int) Arr::get($search_totals, 'cms', 0) > 0) {
					Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=cms');
				}
			}
			?>
			<div class="s-nav-cnt">
				<ul class="s-nav">
                    <li class="selected"><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $q; ?>"><span><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter" data-items_total="with_bracket">(<?php echo (int) $items_total; ?>)</span></span></a></li>
					<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=publish.01"><span><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish.01', 0); ?>)</span></span></a></li>
					<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><span><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', 0); ?>)</span></span></a></li>
				</ul>
			</div>
		</div>
	<?php else: ?>
		<div class="c-main<?php if((!empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1) OR $kind_content == 'manufacturer' OR $kind_content == 'list' OR $kind_content == 'seller'): ?> lvl2<?php endif; ?>">
			<div class="wrapper">
				<?php if($kind_content == 'seller'): ?>
					<div class="seller-detail-header-row">
						<div class="seller-item-img seller-detail-item-img">
							<?php if(!empty($kind['main_image'])): ?>
								<?php $fileExtension = pathinfo($kind['main_image'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img width="146px" height="146px" src="<?php echo Utils::file_url($kind['main_image']); ?>" alt="<?php echo $kind['title']; ?>">
								<?php else: ?>
									<img <?php echo Thumb::generate($kind['main_image'], array('width' => 146, 'height' => 146, 'default_image' => '/media/images/no-image-146.webp', 'placeholder' => '/media/images/no-image-146.webp', 'srcset' => '292c 2x')); ?> alt="<?php echo $kind['title']; ?>" />
								<?php endif; ?>
							<?php else: ?>
								<img src="/media/images/no-image-146.webp" alt="<?php echo $kind['title']; ?>">
							<?php endif; ?>
						</div>
						<div class="seller-detail-item-desc">
							<div class="seller-detail-item-title-box">
								<div class="seller-detail-item-title">
									<?php if(!empty($kind['corporate_name'])): ?>
										<span><?php echo $kind['corporate_name']; ?></span>
									<?php endif; ?>
									<?php if(!empty($kind['title'])): ?>
										<span class="extra-name">(<?php echo $kind['title']; ?>)</span>
									<?php endif; ?>
								</div>
								<?php if(!empty($kind['grade']) AND number_format($kind['grade']) > 0): ?>
									<div class="seller-detail-rate">
										<span class="seller-detail-rate-average"><?php echo number_format($kind['grade'], 1); ?></span>
										<span class="cp-rate seller-rate rates-container add_rate add_rate_<?php echo $kind['code']; ?>" data-rates="<?php echo $kind['grade']; ?>" data-rates_votes="1" data-rates_sum="<?php echo $kind['grade']; ?>"></span>
									</div>
								<?php endif; ?>
							</div>
							<?php if($kind['status'] == 'Closed' AND (!empty($kind['closed_from']) OR !empty($kind['closed_to']))): ?>
								<div class="seller-detail-item-closed strong"><?php echo Arr::get($cmslabel, 'seller_closed'); ?> (<?php if(!empty($kind['closed_from'])): ?><?php echo date('d.m.Y.', $kind['closed_from']); ?><?php endif; ?><?php if(!empty($kind['closed_to'])): ?> - <?php echo date('d.m.Y.', $kind['closed_to']); ?><?php endif; ?>)</div>
							<?php endif; ?>
							<div class="seller-detail-extra">
								<div class="seller-item-info">
									<?php if($kind['corporate_name']): ?><?php echo $kind['corporate_name']; ?>, <?php endif; ?>
									<?php if($kind['address']): ?><?php echo $kind['address']; ?>, <?php endif; ?>
									<?php if($kind['zipcode']): ?><?php echo $kind['zipcode']; ?><?php endif; ?>
									<?php if($kind['city']): ?> <?php echo $kind['city']; ?>, <?php endif; ?>
									<?php if($kind['shipping_country']): ?><?php echo $kind['shipping_country']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> <?php endif; ?>
									<?php if($kind['identification_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_identification_number'); ?>: </strong> <?php echo $kind['identification_number']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> </span><?php endif; ?>
									<?php if($kind['vat_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_vat_number'); ?>: </strong> <?php echo $kind['vat_number']; ?></span><?php endif; ?>
								</div>
								<?php if(!empty($kind['date_joined'])): ?>
									<?php
										$calendar_month = Kohana::config('app.utils.calendar.si.month');
										$calendar_year = Kohana::config('app.utils.calendar.si.year');
										$date_month = (date('n', $kind['date_joined']) - 1);
										$date_year = (date('Y', $kind['date_joined']));
										$seller_date = $calendar_month[$date_month] .' '. $date_year .'.';
									?>
									<?php /* ?>
									<div class="seller-item-date">
										<?php echo Arr::get($cmslabel, 'seller_date_joined'); ?>
										<strong><?php echo $seller_date; ?></strong>
									</div>
									<?php */ ?>
								<?php endif; ?>
								<?php if(!empty($kind['shipping_country'])): ?>
									<div class="seller-item-country">
										<?php echo Arr::get($cmslabel, 'seller_shipping_country'); ?>
										<strong><?php echo $kind['shipping_country']; ?></strong>
									</div>
								<?php endif; ?>
							</div>
						</div>
					</div>
				<?php else: ?>
					<div class="c-header">
						<h1 class="c-title<?php if(!empty($kind) AND !empty($kind['content']) AND empty($kind['hide_description'])): ?> special<?php endif; ?>">
							<?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?>: <?php echo $q; ?><?php endif; ?>
							<?php if($items_total AND ((!empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1) OR $kind_content == 'manufacturer' OR $kind_content == 'list')): ?>
								<span class="c-title-counter">(<strong data-items_total="1"><?php echo $items_total ?></strong>)</span>
							<?php endif; ?>
						</h1>

						<?php if(!empty($kind) AND $kind_content == 'list' AND !empty($kind['main_image'])): ?>
							<div class="c-promo c-promo-list c-promo-special">
								<?php $kind_title = str_replace('"', "'", Text::meta($kind['title'])); ?>
								<div class="c-promo-item c-promo-list-item c-promo-item-special" data-tracking_gtm_promo="addPromo|<?php echo $kind['id']; ?>|<?php echo $kind_title; ?>|<?php echo $kind['main_image']; ?>|<?php echo $kind_title; ?>">
									<span class="c-promo-item-img c-promo-list-item-img" data-tracking_gtm_promo_view="<?php echo $kind['id']; ?>|<?php echo $kind_title; ?>|<?php echo $kind['main_image']; ?>|list - <?php echo $kind_title; ?>">
										<picture>
											<?php if (!empty($kind['main_image_2'])): ?>
												<source srcset="<?php echo Thumb::generate($kind['main_image_2'], 760, 260, true, 'thumb', TRUE, '/media/images/no-image-760x260.webp'); ?>" media="(max-width: 760px)">
											<?php endif; ?>
											<img <?php echo Thumb::generate($kind['main_image'], array('width' => 888, 'height' => 324, 'crop' => true, 'default_image' => '/media/images/no-image-1480x160.webp', 'html_tag' => TRUE)); ?> alt="<?php echo $kind['title']; ?>" />
										</picture>
									</span>
								</div>
							</div>
						<?php endif; ?>
						
						<?php if (!empty($kind) AND !empty($kind['content']) AND empty($kind['hide_description']) AND $kind_content != 'manufacturer' AND $kind_content != 'list' AND $kind_content != 'seller'): ?>
							<div class="c-desc text-line">
								<?php echo $kind['content']; ?>
								<div class="c-desc-btn text-line-btn">
									<span class="more"><?php echo Arr::get($cmslabel, 'category_description_more'); ?></span>
									<span class="less"><?php echo Arr::get($cmslabel, 'category_description_less'); ?></span>
								</div>
							</div>
						<?php endif; ?>

						<?php if(($kind_content == 'manufacturer' OR $kind_content == 'list' OR $kind_content == 'seller') AND !empty($kind['content'])): ?>
							<div class="c-desc text-line">
								<?php echo ($kind) ? $kind['content'] : Arr::get($cms_page, 'content'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['content']) ? ' - '.Text::meta($extra_kind['content']) : ''; ?><?php if ($q): ?>: <?php echo $q; ?><?php endif; ?>
								<div class="c-desc-btn text-line-btn">
									<span class="more"><?php echo Arr::get($cmslabel, 'category_description_more'); ?></span>
									<span class="less"><?php echo Arr::get($cmslabel, 'category_description_less'); ?></span>
								</div>
							</div>
						<?php endif; ?>
					</div>

					<?php if($info['user_device'] == 'm' AND !empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1): ?>
						<?php echo View::factory('catalog/widget/categories_special', ['kind' => $kind]); ?>
					<?php endif; ?>
				
					<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] == 1): ?>
						<?php echo View::factory('catalog/widget/catalog_promo', ['kind' => $kind, 'mode' => 'catalog_category_top']); ?>
					<?php endif; ?>

					<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1): ?>
						<?php $catalog_promo_lvl2 = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'catalog_category_promo', 'limit' => 1, 'catalogcategory_id' => $kind['id'])); ?>
						<?php if($catalog_promo_lvl2): ?>
							<div class="c-promo c-promo-special">
								<?php $cpp_i = 1; ?>
								<?php foreach ($catalog_promo_lvl2 as $catalog_promo_item): ?>
									<?php $catalog_promo_item_title = str_replace('"', "'", Text::meta($catalog_promo_item['title'])); ?>
									<?php if(!empty($kind)): ?>
										<?php $kind_title = str_replace('"', "'", Text::meta($kind['title'])); ?>
									<?php endif; ?>
									<?php if($catalog_promo_item['link']): ?><a href="<?php echo $catalog_promo_item['link']; ?>" class="c-promo-item c-promo-item-special link" <?php if($catalog_promo_item['link_target_blank']): ?> target="_blank"<?php endif; ?> data-tracking_gtm_promo_click="<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|category - <?php echo $kind_title; ?> - <?php echo $cpp_i; ?>" data-tracking_gtm_promo="addPromo|<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php echo $catalog_promo_item_title; ?>"><?php else: ?><div class="c-promo-item c-promo-item-special" data-tracking_gtm_promo="addPromo|<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|<?php echo $catalog_promo_item_title; ?>"><?php endif; ?>
										<span class="c-promo-item-img" data-tracking_gtm_promo_view="<?php echo $catalog_promo_item['id']; ?>|<?php echo $catalog_promo_item_title; ?>|<?php echo $catalog_promo_item['image']; ?>|category - <?php echo $kind_title; ?> - <?php echo $cpp_i; ?>">
											<picture>
												<?php if (!empty($catalog_promo_item['image_2'])): ?>
													<source srcset="<?php echo Thumb::generate($catalog_promo_item['image_2'], 760, 260, true, 'thumb', TRUE, '/media/images/no-image-760x260.webp'); ?>" media="(max-width: 760px)">
												<?php endif; ?>
												<img <?php echo Thumb::generate($catalog_promo_item['image'], array('width' => 1480, 'height' => 160, 'crop' => true, 'default_image' => '/media/images/no-image-1480x160.webp', 'html_tag' => TRUE)); ?> alt="<?php echo $catalog_promo_item['title']; ?>" />
											</picture>
										</span>
									<?php if($catalog_promo_item['link']): ?></a><?php else: ?></div><?php endif; ?>
									<?php $cpp_i++; ?>
								<?php endforeach; ?>
							</div>
						<?php endif; ?>
					<?php endif; ?>

					<?php if(empty($kind) OR ($info['user_device'] == 'm' AND !empty($kind) AND !empty($kind['level']) AND $kind['level'] < 2) OR ($info['user_device'] != 'm' AND !empty($kind) AND !empty($kind['level']) AND $kind['level'] < 3)): ?>
						<?php echo View::factory('catalog/widget/categories', ['kind' => $kind]); ?>
					<?php endif; ?>

					<?php if(!empty($kind['position_h']) AND $kind_content != 'manufacturer' AND $kind_content != 'list' AND $kind_content != 'seller' AND empty($active_filters)): ?>
						<?php 
							$category_excluded_list = Widget_Catalog::speciallists(['lang' => $info['lang'], 'category' => $kind['id'], 'limit' => 1, 'single' => true]);
							$category_excluded_items = (!empty($category_excluded_list['code'])) ? Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $category_excluded_list['code'], 'sort' => 'list_position', 'only_available' => true, 'limit' => 12)) : [];
						?>
						
						<?php if(!empty($category_excluded_items)): ?>
							<div class="c-exclude-items<?php if(!empty($kind['level']) AND $kind['level'] > 1): ?> c-exclude-items-lvl2<?php endif; ?>" data-count-items="<?php echo count($category_excluded_items); ?>">
								<div class="c-subtitle"><?php echo Arr::get($cmslabel, 'top_products'); ?></div>
								<div class="<?php if(!empty($kind['level']) AND $kind['level'] > 1): ?>c-exclude-slider2 <?php else: ?>c-exclude-slider <?php endif; ?>swipe-slider blazy-container">
									<?php if(!empty($kind['level']) AND $kind['level'] > 1): ?>
										<?php echo View::factory('catalog/index_entry_featured', ['items' => $category_excluded_items, 'class' => 'CP']); ?>
									<?php else: ?>
										<?php echo View::factory('catalog/index_entry', ['items' => $category_excluded_items, 'mode' => 'slider']); ?>
									<?php endif; ?>
								</div>
							</div>
						<?php endif; ?>
					<?php endif; ?>

					<?php if(!empty($kind['level']) AND $kind['level'] == 1 AND $kind_content != 'manufacturer'  AND $kind_content != 'seller' AND !empty($kind['related_manufacturers_ids'])): ?>
						<?php
						$manufacturers_ids = Text::db_to_array($kind['related_manufacturers_ids']);
						$manufacturers = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'id' => $manufacturers_ids, 'limit' => count($manufacturers_ids), 'sort_related' => $manufacturers_ids)); 

						$kind['manufacturers'] = $manufacturers;
						?>

						<?php if(!empty($kind['manufacturers'])): ?>
							<div class="m-exluded-items">
								<div class="c-subtitle"><?php echo Arr::get($cmslabel, 'shop_by_brand'); ?></div>
								<div class="m-exluded m-exluded-slider">
									<?php foreach ($kind['manufacturers'] as $manufacturer): ?>
										<a class="m-exluded-item" href="<?php echo $manufacturer['url']; ?>">
											<?php if($manufacturer['main_image']): ?>
												<img loading="lazy" <?php echo Thumb::generate($manufacturer['main_image'], array('width' => 130, 'height' => 50, 'default_image' => '/media/images/no-image-50.webp', 'html_tag' => TRUE, 'srcset' => '260r 2x')); ?> />
											<?php else: ?>
												<span><?php echo $manufacturer['title']; ?></span>
											<?php endif; ?>
										</a>
									<?php endforeach; ?>
								</div>
							</div>
						<?php endif; ?>
					<?php endif; ?>

					<?php if(!empty($kind['level']) AND $kind['level'] == 1): ?>
						<?php echo View::factory('catalog/widget/catalog_promo', ['kind' => $kind, 'mode' => 'catalog_category_bottom']); ?>
					<?php endif; ?>
				<?php endif; ?>
			</div>
		</div>
	<?php endif; ?>

	<?php if(!empty($kind['level']) AND $kind_content == 'seller'): ?>
		<?php if ((!empty($kind['content']) OR !empty($kind['element_return_policy']) OR !empty($kind['element_gdpr_compliance']) OR !empty($kind['element_other_terms_conditions'])) OR !empty($kind['url_product_page'])): ?>
			<div class="seller-tabs special">
				<div class="wrapper">
					<?php if(!empty($kind['content']) OR !empty($kind['element_return_policy']) OR !empty($kind['element_gdpr_compliance']) OR !empty($kind['element_other_terms_conditions'])): ?>
						<a class="seller-tab-title" href="<?php echo $kind['url']; ?>"><span><?php echo Arr::get($cmslabel, 'tab_seller_information'); ?></span></a>
					<?php endif; ?>
					<?php if(!empty($kind['url_product_page'])): ?>
						<a class="seller-tab-title active" href="<?php echo $kind['url_product_page']; ?>"><span><?php echo Arr::get($cmslabel, 'tab_seller_items'); ?></span></a>
					<?php endif; ?>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<div class="c-main-items" data-scrollto="items_catalog_layout">
		<?php if((!empty($kind) AND !empty($kind['level']) AND $kind['level'] > 1) OR $q OR $kind_content == 'manufacturer' OR $kind_content == 'list' OR $kind_content == 'seller'): ?>
			<div class="c-items-row wrapper">
				<div class="c-filters-section">
					<?php if(!$q AND !empty($kind) AND !empty($kind['parents'])): ?>
						<?php $numParents = count($kind['parents']); ?>
						<?php $p = 0; ?>
						<?php foreach($kind['parents'] as $parent): ?>
							<?php if(++$p == $numParents): ?>
								<?php $cat_title = $parent['title']; ?>
								<?php $cat_url = $parent['url']; ?>
							<?php endif; ?>
						<?php endforeach; ?>

						<a href="<?php echo $cat_url; ?>" class="c-filter-back link"><?php echo Arr::get($cmslabel, 'back_to_category'); ?> <span><?php echo $cat_title; ?></span></a>
					<?php endif; ?>

					<?php if(!$q AND $kind_content != 'list' AND $kind_content != 'manufacturer' AND $kind_content != 'seller'): ?>
						<div class="ci-categories">
							<?php if(empty($kind)): ?>
								<?php $cat_title = Arr::get($cmslabel, 'catalog_categories'); ?>
								<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'level_range' => '1.1']); ?>
							<?php else: ?>
								<?php $cat_position = !empty($kind['parents']) ? reset($kind['parents'])['position_h'] : $kind['position_h']; ?>
								<?php $cat_title = $kind['title']; ?>
								<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $kind['position_h'], 'level_range' => '1.3']); ?>
								<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] == 3): ?>
									<?php $cat_position = Utils::get_all_parents($kind['position_h'], '.', 2); ?>
									<?php $categories_others = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $cat_position, 'level_range' => '1.3']); ?>
								<?php else: ?>
									<?php $categories_others = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $cat_position, 'level_range' => '2.2']); ?>
								<?php endif; ?>
							<?php endif; ?>

							<?php $curr_cat = Utils::extract_segments($info['lang'], $info['basic_url'], 3, 0, TRUE); ?>
							<div class="ci-categories-list">
								<div class="ci-categories-list-title<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] < 3): ?> has-children<?php endif; ?>"><?php echo $cat_title; ?></div>
								<ul class="ci-categories-list-items" data-current_url="<?php echo $curr_cat; ?>">
									<?php if($categories): ?>
										<?php foreach($categories as $category): ?>
											<?php if(!empty($kind)): ?>
												<li class="<?php if($category['url'] == $info['url']): ?> active<?php endif; ?>">
													<a<?php if($category['url'] == $info['url']): ?> class="active"<?php endif; ?> href="<?php echo $category['url']; ?>">
														<span class="subtitle"><?php echo $category['title']; ?></span>
														<?php if($category['total'] > 0): ?>
															<span class="ci-counter"> (<?php echo $category['total']; ?>)</span>
														<?php endif; ?>
													</a>
												</li>
											<?php endif; ?>
										<?php endforeach; ?>
									<?php endif; ?>
								</ul>
							</div>
							<?php if(!empty($kind) AND !empty($kind['level']) AND $kind['level'] < 4 AND !empty($categories_others)): ?>
								<div class="ci-categories-list special">
									<div class="ci-categories-list-title has-children"><?php echo Arr::get($cmslabel, 'catalog_other_categories'); ?></div>
									<ul class="ci-categories-list-other-items">
										<?php foreach($categories_others as $category): ?>
											<?php if($category['url'] != $info['url']): ?>
												<li>
													<a href="<?php echo $category['url']; ?>">
														<?php echo $category['title']; ?>
													</a>
												</li>
											<?php endif; ?>
										<?php endforeach; ?>
									</ul>
								</div>
							<?php endif; ?>
						</div>
					<?php endif; ?>

					<?php echo View::factory('catalog/widget/search_fields', [
						'search_fields' => $search_fields,
						'active_filters' => $active_filters,
						'kind_content' => $kind_content,
						'q' => $q,
					]); ?>
				</div>

				<?php if(!empty($cmslabel['shop_extra_info'])): ?>
					<div class="s-extra-info-flyout cd-flyout" data-flyout="shop_extra_info">
						<div class="cd-flyout-close"></div>
						<div class="cd-flyout-content">
							<div class="cd-flyout-header">
								<div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'shop_extra_info_flyout_title'); ?></div>
							</div>
							<?php echo Arr::get($cmslabel, 'shop_extra_info_flyout_content'); ?>
							<div class="cd-flyout-bottom">
								<div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
							</div>
						</div>
					</div>
				<?php endif; ?>

				<div class="c-items-col c-items-col2">
					<div class="c-toolbar">
                        <?php echo View::factory('catalog/widget/search_fields_badge_uau'); ?>

                        <?php echo View::factory('catalog/widget/search_fields_discount'); ?>

						<?php echo View::factory('catalog/widget/search_fields_location', ['search_fields' => $search_fields]); ?>

						<?php if((!empty($search_fields) AND $items_total > 1) OR $active_filters): ?>
							<div class="c-toolbar-btn-filters"><span><?php echo Arr::get($cmslabel, 'filters'); ?></span></div>
						<?php endif; ?>

						<?php if($items_total > 1 OR !empty($active_filters) AND !empty($cmslabel['shop_extra_info'])): ?>
							<div class="s-extra-info-container">
								<div class="s-extra-info cd-flyout-btn" data-flyout_code="shop_extra_info">
									<span><?php echo Arr::get($cmslabel, 'shop_extra_info'); ?></span>
								</div>
							</div>
						<?php endif; ?>

						<?php echo View::factory('catalog/widget/search_fields_sort', ['items_total' => $items_total, 'active_filters' => $active_filters]); ?>
					</div>

					<?php if($items_total > 1 OR !empty($active_filters) AND !empty($cmslabel['shop_extra_info'])): ?>
						<div class="s-extra-info-wrapper">
							<div class="s-extra-info cd-flyout-btn" data-flyout_code="shop_extra_info">
								<span><?php echo Arr::get($cmslabel, 'shop_extra_info'); ?></span>
							</div>
						</div>
					<?php endif; ?>

                    <?php echo View::factory('catalog/widget/search_fields_active', [
                        'active_filters' => $active_filters,
                        'reset_url' => $search_fields['_basic']['reset_url'] ?? '?',
                    ]); ?>

					<?php if((!empty($kind['level']) AND $kind['level'] > 1) OR $q OR $kind_content == 'manufacturer' OR $kind_content == 'list' OR $kind_content == 'seller'): ?>
						<div id="items_catalog_layout">
							<?php echo View::factory('catalog/index_layout', [
								'cms_page' => $cms_page,
								'kind' => $kind,
								'extra_kind' => $extra_kind,
								'q' => $q,
								'items' => $items,
								'items_per_page' => $items_per_page,
								'items_all' => $items_all,
								'items_layout_sufix' => $items_layout_sufix,
								'items_total' => $items_total,
								'pagination' => $pagination,
								'selected_sort' => $selected_sort,
							]); ?>
						</div>
					<?php endif; ?>
				</div>
			</div>
		<?php endif; ?>
	</div>

    <?php if (!empty($kind['element_footer_content'])): ?>
        <div class="c-bottom">
            <div class="wrapper">
                <?php if (!empty($kind['element_footer_title'])): ?><h3 class="c-bottom-title"><?php echo $kind['element_footer_title']; ?></h3><?php endif; ?>
                <div class="c-bottom-desc c-desc text-line">
                    <?php echo $kind['element_footer_content']; ?>
                    <div class="c-desc-btn text-line-btn">
                        <span class="more"><?php echo Arr::get($cmslabel, 'category_description_more'); ?></span>
                        <span class="less"><?php echo Arr::get($cmslabel, 'category_description_less'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $this->endblock('main'); ?>

<?php $this->block('content_layout'); ?>
<?php if(isset($_GET['special_view']) AND $_GET['special_view'] == 'wishlist'): ?>
	<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>

	<div class="wishlist-row wrapper">
		<div class="ci-categories wishlist-categories">
			<?php echo View::factory('catalog/widget/wishlist_categories'); ?>
		</div>
		<div class="wishlist-section<?php if ($user): ?> auth-content<?php endif; ?>">
			<h1 class="<?php if ($user): ?>auth-main-title<?php else: ?>wishlist-title<?php endif; ?> wishlist-title-empty">
				<?php if($user): ?>
					<span><?php echo Arr::get($cmslabel, 'auth_wishlist_title'); ?></span>
				<?php else: ?>
					<span><?php echo Arr::get($cmslabel, 'wishlist_products_title'); ?></span>
				<?php endif; ?>
				<?php if (!empty($item['total_items'])): ?>
					<span class="<?php if ($user): ?>auth-title-counter<?php else: ?>wishlist-detail-counter<?php endif; ?> wishlist_count">(<?php echo $item['total_items']; ?>)</span>
				<?php endif; ?>
			</h1>	
			<p class="wishlist-empty-desc"><?php echo Arr::get($cmslabel, 'no_wishlists'); ?></p>
		</div>
	</div>
<?php endif; ?>
<?php $this->endblock('content_layout'); ?>