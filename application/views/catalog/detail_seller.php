<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>

<?php $this->block('main'); ?>
	<div class="seller-detail-header">
		<div class="bc">
			<div class="wrapper wrapper-bc">
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
		</div>

		<div class="wrapper">
			<div class="seller-detail-header-row">
				<div class="seller-item-img seller-detail-item-img">
					<?php if(!empty($item['main_image'])): ?>
						<?php $fileExtension = pathinfo($item['main_image'], PATHINFO_EXTENSION); ?>
						<?php if($fileExtension == 'svg'): ?>
							<img width="146px" height="146px" src="<?php echo Utils::file_url($item['main_image']); ?>" alt="<?php echo $item['title']; ?>">
						<?php else: ?>
							<img <?php echo Thumb::generate($item['main_image'], array('width' => 146, 'height' => 146, 'default_image' => '/media/images/no-image-146.webp', 'placeholder' => '/media/images/no-image-146.webp', 'srcset' => '292c 2x')); ?> alt="<?php echo $item['title']; ?>" />
						<?php endif; ?>
					<?php else: ?>
						<img src="/media/images/no-image-146.webp" alt="<?php echo $item['title']; ?>">
					<?php endif; ?>
				</div>
				<div class="seller-detail-item-desc">
					<div class="seller-detail-item-title-box">
						<div class="seller-detail-item-title">
							<?php if(!empty($item['corporate_name'])): ?>
								<span><?php echo $item['corporate_name']; ?></span>
							<?php endif; ?>
							<?php if(!empty($item['title'])): ?>
								<span class="extra-name">(<?php echo $item['title']; ?>)</span>
							<?php endif; ?>
						</div>
						<?php if(!empty($item['grade']) AND number_format($item['grade']) > 0): ?>
							<div class="seller-detail-rate">
								<span class="seller-detail-rate-average"><?php echo number_format($item['grade'], 1); ?></span>
								<span class="cp-rate seller-rate rates-container add_rate add_rate_<?php echo $item['code']; ?>" data-rates="<?php echo $item['grade']; ?>" data-rates_votes="1" data-rates_sum="<?php echo $item['grade']; ?>"></span>
							</div>
						<?php endif; ?>
					</div>
					<?php if($item['status'] == 'Closed' AND (!empty($item['closed_from']) OR !empty($item['closed_to']))): ?>
						<div class="seller-detail-item-closed strong"><?php echo Arr::get($cmslabel, 'seller_closed'); ?> (<?php if(!empty($item['closed_from'])): ?><?php echo date('d.m.Y.', $item['closed_from']); ?><?php endif; ?><?php if(!empty($item['closed_to'])): ?> - <?php echo date('d.m.Y.', $item['closed_to']); ?><?php endif; ?>)</div>
					<?php endif; ?>
					<div class="seller-detail-extra">
						<div class="seller-item-info">
							<?php if($item['corporate_name']): ?><?php echo $item['corporate_name']; ?>, <?php endif; ?>
							<?php if($item['address']): ?><?php echo $item['address']; ?>, <?php endif; ?>
							<?php if($item['zipcode']): ?><?php echo $item['zipcode']; ?><?php endif; ?>
							<?php if($item['city']): ?> <?php echo $item['city']; ?>, <?php endif; ?>
							<?php if($item['shipping_country']): ?><?php echo $item['shipping_country']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> <?php endif; ?>
							<?php if($item['identification_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_identification_number'); ?>: </strong> <?php echo $item['identification_number']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> </span><?php endif; ?>
							<?php if($item['vat_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_vat_number'); ?>: </strong> <?php echo $item['vat_number']; ?></span><?php endif; ?>
						</div>
						<?php if(!empty($item['date_joined'])): ?>
							<?php
								$calendar_month = Kohana::config('app.utils.calendar.si.month');
								$calendar_year = Kohana::config('app.utils.calendar.si.year');
								$date_month = (date('n', $item['date_joined']) - 1);
								$date_year = (date('Y', $item['date_joined']));
								$seller_date = $calendar_month[$date_month] .' '. $date_year .'.';
							?>
							<?php /* ?>
							<div class="seller-item-date">
								<?php echo Arr::get($cmslabel, 'seller_date_joined'); ?>
								<strong><?php echo $seller_date; ?></strong>
							</div>
							<?php */ ?>
						<?php endif; ?>
						<?php if(!empty($item['shipping_country'])): ?>
							<div class="seller-item-country">
								<?php echo Arr::get($cmslabel, 'seller_shipping_country'); ?>
								<strong><?php echo $item['shipping_country']; ?></strong>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
	</div>

	<?php if ((!empty($item['content']) OR !empty($item['element_return_policy']) OR !empty($item['element_gdpr_compliance']) OR !empty($item['element_other_terms_conditions'])) OR !empty($item['url_product_page'])): ?>
		<div class="seller-tabs">
			<div class="wrapper">
				<?php if(!empty($item['content']) OR !empty($item['element_return_policy']) OR !empty($item['element_gdpr_compliance']) OR !empty($item['element_other_terms_conditions'])): ?>
					<a class="seller-tab-title active" href="<?php echo $item['url']; ?>"><span><?php echo Arr::get($cmslabel, 'tab_seller_information'); ?></span></a>
				<?php endif; ?>
				<?php if(!empty($item['url_product_page'])): ?>
					<a class="seller-tab-title" href="<?php echo $item['url_product_page']; ?>"><span><?php echo Arr::get($cmslabel, 'tab_seller_items'); ?></span></a>
				<?php endif; ?>
			</div>
		</div>
	<?php endif; ?>

	<?php if(!empty($item['content']) OR !empty($item['element_return_policy']) OR !empty($item['element_gdpr_compliance']) OR !empty($item['element_other_terms_conditions'])): ?>
		<div class="seller-detail-content-wrapper wrapper">
			<?php if(!empty($item['content'])): ?>
				<div class="seller-detail-content-item">
					<div class="seller-detail-content-item-title"><?php echo Arr::get($cmslabel, 'seller_tab_information'); ?></div>
					<div class="seller-detail-content-desc cms-content"><?php echo $item['content']; ?></div>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['element_return_policy'])): ?>
				<div class="seller-detail-content-item">
					<div class="seller-detail-content-item-title"><?php echo Arr::get($cmslabel, 'seller_return_policy_title'); ?></div>
					<div class="seller-detail-content-desc cms-content"><?php echo $item['element_return_policy']; ?></div>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['element_gdpr_compliance'])): ?>
				<div class="seller-detail-content-item">
					<div class="seller-detail-content-item-title"><?php echo Arr::get($cmslabel, 'seller_gdpr_compliance_title'); ?></div>
					<div class="seller-detail-content-desc cms-content"><?php echo $item['element_gdpr_compliance']; ?></div>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['element_other_terms_conditions'])): ?>
				<div class="seller-detail-content-item">
					<div class="seller-detail-content-item-title"><?php echo Arr::get($cmslabel, 'seller_terms_conditions_title'); ?></div>
					<div class="seller-detail-content-desc cms-content"><?php echo $item['element_other_terms_conditions']; ?></div>
				</div>
			<?php endif; ?>
		</div>
	<?php endif; ?>
<?php $this->endblock('main'); ?>