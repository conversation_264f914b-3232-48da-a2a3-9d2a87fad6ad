<?php
$mode = (isset($mode)) ? $mode : '';
$class = (isset($class))? $class : '';
$product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities');
$i = (!empty($pagination)) ? (($pagination->current_page - 1) * $pagination->items_per_page) + 1 : 1;
$is_loyalty = (!empty($user->loyalty_code));
?>

<?php foreach ($items as $item): ?>
	<?php
	$priceRecommended = false;
	if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))) {
		$priceRecommended = true;
	}
	?>

	<article class="clear cp<?php if($mode == 'wishlist'): ?> wishlist-mode<?php endif; ?>"<?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?> data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo $class; ?>">
		<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
		<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
		<span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'title')); ?></span>
		<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'code'); ?></span>
		<?php if ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']): ?>
			<span style="display: none;" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
			<span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
			<span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">loyalty</span>
		<?php else: ?>
			<span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
			<?php if (!in_array($item['type'], ['advanced', 'configurable']) AND ((!empty($item['discount_percent']) AND $item['discount_percent'] > 0) OR $item['price_custom'] < $item['basic_price'])): ?>
				<span style="display: none;" data-product_price_type="<?php echo $item['shopping_cart_code']; ?>">action</span>
			<?php endif; ?>
		<?php endif; ?>

		<?php if (!empty($compare_page)): ?>
			<div class="cp-compare-header">
				<form class="cp-compare-form" action="?special_view=compare" method="GET">
					<input class="cp-compare-input" name="compare_autocomplete-<?php echo $item['id']; ?>" placeholder="<?php if($info['user_device'] != 'm'): ?><?php echo Arr::get($cmslabel, 'compare_add_title', 'Upišite naziv proizvoda'); ?><?php else: ?>'<?php endif; ?>" data-id_exclude="<?php echo ($items) ? implode(',', array_keys($items)) : ''; ?>" type="text" data-compare_content="<?php echo $item['compare_widget']['content']; ?>">
					<button class="cp-compare-btn" type="submit"></button>
				</form>
			</div>
		<?php endif ?>

		<?php if (isset($item['compare_widget']) AND !empty($compare_page)): ?>
			<div class="cp-compare-container">
				<a class="cp-compare cp-btn-compare compare_set_<?php echo $item['compare_widget']['content']; ?> compare_active" href="javascript:cmscompare.set('<?php echo $item['compare_widget']['content']; ?>', 'remove_reload', '', '_tracking:index');"><span><?php echo Arr::get($cmslabel, 'compare_remove', 'Ukloni'); ?></span></a>
			</div>
		<?php endif; ?>

		<?php
		$availability_status = $item['availability_info']['status'] ?? null;
		$priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : ''));
		$badges_special = (!empty($item['badges'])) ? $item['badges'] : [];
		$badges_special_1 = (!empty($item['badges_special_1'])) ? $item['badges_special_1'] : [];
		$badges_special_2 = (!empty($item['badges_special_2'])) ? $item['badges_special_2'] : [];
		unset($badges_special_2[120638]);
		$badge_uau = (Kohana::$environment === 1) ? 447785 : 1299393;
		$badge_uau_exist = false;
		
		if(!empty($item['badges'])) {
			foreach ($item['badges'] as $badge) {
				if ($badge['code'] == $badge_uau) {
					$badge_uau_exist = true;
					break;
				}
			}
		}
		?>
		<div class="cp-image-container">
			<figure class="cp-image">
				<a href="<?php echo $item['url']; ?>">
					<img loading="lazy" <?php echo Thumb::generate(Arr::get($item, 'main_image'), array('width' => 240, 'height' => 240, 'default_image' => '/media/images/no-image-240.webp', 'html_tag' => true, 'srcset' => '480c 2x')); ?> <?php if($mode != 'landing'): ?> title="<?php echo Text::meta($item['main_image_title']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?> alt="<?php if($mode == 'landing'): ?><?php echo $item['title']; ?><?php else: ?><?php echo Text::meta($item['main_image_description']); ?><?php endif; ?>" />
				</a>
			</figure>

			<?php if(!isset($badges) OR !empty($badges_special)): ?>
				<div class="cp-badges">
					<?php if (!empty($badges_special_1)): ?>
						<?php foreach($badges_special_1 as $badge_special): ?>
							<?php if ($badge_special['category'] == 0 AND $priceRecommended == false): ?>
								<div class="cp-badge cp-badge-discount<?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom']): ?> cp-badge-discount-loyalty<?php endif; ?>">
									<?php
									$discount_percent = $item['discount_percent_custom'];
									$price_saved = ($item['basic_price_custom'] - $item['price_custom']);
									if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom'])) {
										$discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['price_custom'])) * 100, 0);
										$price_saved = ($item['price_custom'] - $item['loyalty_price_custom']);
									} else if (($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom'])) {
										$discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['basic_price_custom'])) * 100, 0);
										$price_saved = ($item['basic_price_custom'] - $item['loyalty_price_custom']);
									}
									?>
									<span><?php if(!empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['price_custom']): ?><?php echo Arr::get($cmslabel, 'loaylty'); ?> <?php endif; ?>-<?php echo $discount_percent; ?> %</span>
									<div class="cp-badge-tooltip cp-badge-tooltip-discount"><?php echo Arr::get($cmslabel, 'prihranek'); ?> <strong><?php echo Utils::currency_format($price_saved * $currency['exchange'], $currency['display']); ?></strong></div>
								</div>
							<?php elseif ($badge_special['category'] == 4): ?>
								<div class="cp-badge cp-badge-new">
									<span><?php echo $priority['title']; ?></span>
								</div>
							<?php else: ?>
								<?php if(!empty($badge_special['label_title'])): ?>
									<?php if(!empty($badge_special['badge_url'])): ?>
									<a href="<?php echo ($badge_special['badge_url']); ?>" class="cp-badge link cp-badge-<?php echo($badge_special['code']); ?><?php if($badge_special['category'] == 1): ?> green<?php endif; ?>">
									<?php else: ?>
									<div class="cp-badge cp-badge-<?php echo ($badge_special['code']); ?><?php if($badge_special['category'] == 1): ?> green<?php endif; ?>">
									<?php endif; ?>
										<span><?php echo($badge_special['label_title']); ?></span>
										<?php $hoverTextEmpty = ($badge_special['label_title_hover'] == '/'); ?>
										<?php if(!empty($badge_special['label_title_hover']) AND !$hoverTextEmpty): ?>
											<div class="cp-badge-tooltip"><?php echo($badge_special['label_title_hover']); ?></div>
										<?php elseif(empty($badge_special['label_title_hover']) AND !empty($item['date_available']) AND !$hoverTextEmpty AND ($badge_special['category'] == 1)): ?>
											<div class="cp-badge-tooltip"><?php echo Arr::get($cmslabel, 'izid'); ?> <strong><?php echo ($item['date_available']); ?></strong></div>
										<?php elseif(empty($badge_special['label_title_hover']) AND (!empty($badge_special['coupon_code']) OR !empty($badge_special['coupon_discount_percent'])) AND !$hoverTextEmpty AND ($badge_special['category'] == 2)): ?>
											<div class="cp-badge-tooltip">
												<?php if(!empty($badge_special['coupon_code'])): ?>
													<?php echo($badge_special['coupon_code']); ?>
												<?php endif; ?>
												<?php if(!empty($badge_special['coupon_discount_percent'])): ?>
													(
													<?php if ($item['discount_percent_custom'] >= 2 AND $item['price_custom'] < $item['basic_price_custom']): ?>
														<?php echo Arr::get($cmslabel, 'prihrani_se'); ?>
													<?php else: ?>
														<?php echo Arr::get($cmslabel, 'prihrani'); ?>
													<?php endif; ?>
													<strong><?php echo($badge_special['coupon_discount_percent']); ?> %</strong>)
												<?php endif; ?>
											</div>
										<?php endif; ?>
									<?php if(!empty($badge_special['badge_url'])): ?>
									</a>
									<?php else: ?>
									</div>
									<?php endif; ?>
								<?php endif; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if (!empty($badges_special_2) OR (!empty($item['shipping_options']['bb_fast']['active']) AND !empty($cmslabel['fast_shipping_image']))): ?>
				<div class="cp-badges-special">
					<?php if(!empty($badges_special_2)): ?>
						<?php foreach($badges_special_2 as $badge_special): ?>
							<?php if($badge_special['badge_image']): ?>
								<div class="cp-badge-special">
									<?php if($badge_special['badge_url']): ?><a href="<?php echo($badge_special['badge_url']); ?>"><?php else: ?><span><?php endif; ?>
										<?php $fileExtension = pathinfo($badge_special['badge_image'], PATHINFO_EXTENSION); ?>
										<?php if($fileExtension == 'svg'): ?>
											<img loading="lazy" src="<?php echo Utils::file_url($badge_special['badge_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
										<?php else: ?>
											<img loading="lazy" <?php echo Thumb::generate($badge_special['badge_image'], array('width' => 40, 'height' => 40, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
										<?php endif; ?>
									<?php if($badge_special['badge_url']): ?></a><?php else: ?></span><?php endif; ?>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>

					<?php if(!empty($item['shipping_options']['bb_fast']['active']) AND !empty($cmslabel['fast_shipping_image'])): ?>
						<div class="cp-badge-special">
							<?php echo Arr::get($cmslabel, 'fast_shipping_image'); ?>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['attributes_special'])): ?>
				<div class="cp-energy">
					<?php
					$attr_title = [];
					$attr_img = [];
					foreach($item['attributes_special'] as $attr) {
						if(in_array($attr['attribute_code'], ['ucinek-pranja-in-su-100218739'])){
							$attr_title[0] = $attr['title'];
							$attr_img[0] = $attr['image'];
						}
						if(in_array($attr['attribute_code'], ['razred-energijske-u-100215480'])){
							$attr_title[1] = $attr['title'];
							$attr_img[1] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energijske-u-100176542'])) {
							$attr_title[2] = $attr['title'];
							$attr_img[2] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energij-ucinkov-35'])) {
							$attr_title[3] = $attr['title'];
							$attr_img[3] = $attr['image'];
						}
					}
					?>

					<?php if(!empty($attr_img) OR !empty($attr_title)): ?>
						<?php
						ksort($attr_img);
						ksort($attr_title);
						$attr_img = reset($attr_img);
						$attr_title = reset($attr_title);
						?>
						<?php if(!empty($attr_img)): ?>
							<img loading="lazy" width="80" height="25" src="<?php echo Utils::file_url($attr_img); ?>" alt="<?php echo $attr_title; ?>">
						<?php endif; ?>
						<?php if(!empty($attr_title) AND empty($attr_img)): ?>
							<?php echo $attr_title; ?>
						<?php endif; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</div>

		<div class="clear cp-cnt">
			<div class="cp-cnt-top">
				<div class="cp-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>
				<?php if(!empty($item['category_title']) AND $mode != 'slider'): ?>
					<div class="cp-category"><a href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a></div>
				<?php endif; ?>
				<h2 class="cp-title">
					<a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>">
						<?php echo $item['title']; ?>
					</a>
				</h2>
				<div class="cp-cnt-comment">
					<!-- Rating -->
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
					<?php endif; ?>
					<!-- Comments -->
					<?php if (isset($item['feedback_comment_widget'])): ?>
						<div class="cp-comment-count comments-count catalog-comment-count">
							<span title="<?php echo Arr::get($cmslabel, 'comments_num'); ?>">
								(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
							</span>
						</div>
					<?php endif; ?>
				</div>
			</div>

			<?php if($item['price_custom'] > 0): ?>
				<div class="cp-price<?php if(!empty($badge_uau_exist)): ?> uau-badge<?php endif; ?>">
					<?php
					$installment_price = (!empty($item['installments_calculation']['regular']) ? $item['installments_calculation']['regular'] : 0);
					if (is_array($installment_price)) {
						$installment_price = reset($installment_price);
					}
					$installment_loyalty_price = (!empty($item['installments_calculation']['loyalty']) ? $item['installments_calculation']['loyalty'] : 0);
					if (is_array($installment_loyalty_price)) {
						$installment_loyalty_price = reset($installment_loyalty_price);
					}
					if (!empty($installment_loyalty_price) AND $is_loyalty) {
						$installment_price = $installment_loyalty_price;
					}

					$installment_price = (!empty($installment_price) AND !empty($item['installments_calculation']['regular'])) ? Utils::currency_format($installment_price * $currency['exchange'], $currency['display']) : '';
					// always remove installment price
					$installment_price = '';
					$price_from = (in_array($item['type'], ['advanced', 'configurable']) AND $item['basic_price_custom'] > $item['price_custom']);
					?>

					<?php if($priceRecommended == true): ?>
						<div class="cp-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($itinstallment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
						<?php if(empty($item['loyalty_price_custom'])): ?>
							<div class="cp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
						<?php endif; ?>
					<?php elseif (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
						<?php if ($price_from): ?>
							<span class="cp-old-price cp-price-label var"><?php echo Arr::get($cmslabel, 'price_variation', 'Od'); ?></span>
						<?php endif; ?>
						<div class="cp-current-price cp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($itinstallment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
						<?php if(empty($item['loyalty_price_custom']) OR ($is_loyalty AND !empty($item['loyalty_price_custom'] AND $item['loyalty_price_custom'] > $item['price_custom']))): ?>
							<div class="cp-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
						<?php endif; ?>
					<?php elseif ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']): ?>
						<?php if ($price_from): ?>
							<span class="cp-old-price cp-price-label var"><?php echo Arr::get($cmslabel, 'price_variation', 'Od'); ?></span>
						<?php endif; ?>
						<div class="cp-current-price cp-discount-price blue" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($installment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
						<div class="cp-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
							<?php if ($item['selected_price'] == 'recommended' AND ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom'])): ?>
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php else: ?>
								<?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php endif; ?>
						</div>
					<?php else: ?>
						<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($installment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
					<?php endif; ?>

					<?php if(!$is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
						<div class="cp-loyalty-price-container">
							<div class="cp-loyalty-price">
								<span><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
								<?php $loyalty_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['loyalty'])) ? (float)reset($item['installments_calculation']['loyalty']) : 0 * $currency['exchange'], $currency['display']); ?>
								<?php $loyalty_price = Utils::currency_format(Arr::get($item, 'loyalty_price_custom') * $currency['exchange'], $currency['display']); ?>
								<div class="cp-loyalty-tooltip">
									<span class="cd-shipping-info-close cp-loyalty-tooltip-close"></span>
									<?php if ($user AND !$is_loyalty): ?>
										<?php if(!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$item['price_custom'], $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php elseif (!empty($item['loyalty_price_custom'])): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') :  Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php endif; ?>
									<?php elseif (!$user AND !$is_loyalty): ?>
										<?php if(!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['installments_calculation']['loyalty'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php elseif (!empty($item['loyalty_price_custom'])): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
											<div class="cp-loyalty-tooltip-row special">
												<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
											</div>
										<?php endif; ?>
									<?php endif; ?>
									<?php if ($user AND !$is_loyalty): ?>
										<div class="cp-loyalty-tooltip-row">
											<?php echo Arr::get($cmslabel, 'loyalty_benefits_become_member'); ?>
											<div class="cp-loyalty-tooltip-login">
												<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'become_member'); ?></span></a>
											</div>
										</div>
									<?php elseif (!$user AND !$is_loyalty): ?>
										<div class="cp-loyalty-tooltip-row">
											<?php echo Arr::get($cmslabel, 'loyalty_benefits_login'); ?>
											<div class="cp-loyalty-tooltip-login">
												<a class="special" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', false); ?>"><?php echo Arr::get($cmslabel, 'login'); ?></a>
												<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><?php echo Arr::get($cmslabel, 'create_account'); ?></a>
											</div>
										</div>
									<?php endif; ?>
								</div>
							</div>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if ($mode != 'related' AND $mode != 'slider'): ?>
				<?php if (!empty($item['extra_price_cart_dynamicprice']) AND !empty((float)$item['extra_price_dynamicprice']) AND !empty('extra_price_mode_dynamicprice')): ?>
					<div class="cp-conf"><?php if (!empty($cmslabel['iznenaci_ceno'])): ?><?php echo Arr::get($cmslabel, 'iznenaci_ceno'); ?><?php endif; ?></div>
				<?php endif; ?>

				<div class="available-qty<?php if (!empty($item['extra_price_cart_dynamicprice']) AND !empty((float)$item['extra_price_dynamicprice']) AND !empty('extra_price_mode_dynamicprice')): ?> special<?php endif; ?>">
					<div class="available-qty-btn">
						<?php if (!empty($availability_status)): ?>
							<?php if($item['is_available'] == 1 AND !empty($item['shipping_options']['bb_fast'])): ?>
								<span><?php echo Arr::get($cmslabel, 'na_zalogi_hitri_prevzem_cp'); ?></span>
							<?php else: ?>
								<?php if($availability_status == 1): ?>
									<?php if ($item['last_piece_sale'] AND $item['warehouses_single_pickup_display']): ?>
										<span class="available-last"><?php echo Arr::get($cmslabel, 'odprodaja'); ?></span>
									<?php else: ?>
										<span><?php echo Arr::get($cmslabel, 'na_zalogi'); ?></span>
									<?php endif; ?>
								<?php elseif($availability_status == 2): ?>
									<span><?php echo str_replace(['%MIN_DAY%','%MAX_DAY%'], [$item['availability_info']['min_days'] ?? 0, $item['availability_info']['max_days'] ?? 0], Arr::get($cmslabel, 'na_zalogi_dobavitelja')); ?></span>
								<?php elseif($availability_status == 4): ?>
									<span class="available-last"><?php echo Arr::get($cmslabel, 'na_zalogi_ena'); ?></span>
								<?php elseif($availability_status == 5): ?>
									<?php if(!empty($item['is_available'])): ?>
										<span><?php echo Arr::get($cmslabel, 'na_voljo'); ?> <?php echo date('d.m.Y', $item['shipping_date']); ?></span>
									<?php else: ?>
										<span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi_preorder'); ?></span>
									<?php endif; ?>
								<?php elseif($availability_status == 7): ?>
									<span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi'); ?></span>
								<?php elseif($availability_status == 9): ?>
									<span class="unavailable"><?php echo Arr::get($cmslabel, 'dalj_ni_na_zalogi'); ?></span>
								<?php endif; ?>
							<?php endif; ?>
						<?php endif; ?>

						<div class="cp-available-tooltip cp-tooltip">
							<div class="cd-shipping-info-close"></div>
							<?php if (!empty($item['shipping_options']) AND !empty($item['active_shipping_options_count'])): ?>
								<?php foreach ($item['shipping_options'] as $delivery_key => $delivery_data): ?>
									<?php if(!empty($delivery_data['active'])): ?>
										<?php if ($availability_status == '5' AND !empty($item['shipping_date'])): ?>
											<!-- Preorder product -->
											<?php if($delivery_key == 'p'): ?>
												<div class="cp-tooltip-row in-stores">
													<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_delivery_pickup'); ?></strong></div>
													<div class="cp-tooltip-desc">
														<?php if (!empty($item['warehouses_single_pickup_display'])): ?>
															<?php echo str_replace(['%s%', '%s2%'], [$item['warehouses_single_pickup_display']['title'], date('d.m.Y', $item['shipping_date'])], Arr::get($cmslabel, 'item_time_of_delivery_preorder_single')) ?>
														<?php else: ?>
															<?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?>
														<?php endif; ?>
														<?php /* ?>
														- <strong><?php echo Arr::get($cmslabel, 'free'); ?></strong>
														<?php */ ?>
													</div>
												</div>
											<?php elseif (in_array($delivery_key, ['s', 'e', 'bb', 'bb_xxl', 'bb_fast'])): ?>
												<div class="cp-tooltip-row<?php if($delivery_key == 'bb'): ?> bb-shipping<?php elseif ($delivery_key == 'bb_fast'): ?> delivery-premium<?php else: ?> delivery<?php endif; ?>">
													<div class="cp-tooltip-title">
														<?php if ($delivery_key == 's'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery'); ?>
														<?php elseif ($delivery_key == 'e'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_express_delivery'); ?>
														<?php elseif ($delivery_key == 'bb'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery'); ?>
														<?php elseif ($delivery_key == 'bb_xxl'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery'); ?>
														<?php elseif ($delivery_key == 'bb_fast'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_premium_title'); ?>
														<?php endif; ?>
													</div>
													<div class="cp-tooltip-desc">
														<?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?> 
														<?php /* ?>
														- <strong><?php echo Utils::currency_format($delivery_data['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong>
														<?php */ ?>
													</div>
												</div>
											<?php endif; ?>
										<?php elseif ($availability_status != 5): ?>
											<!-- Not preorder product -->
											<?php if (in_array($delivery_key, ['p', 's', 'e', 'bb', 'bb_xxl', 'bb_fast'])): ?>
												<?php
													if ($delivery_key != 'bb_fast') {
														$calendar_month = Kohana::config('app.utils.calendar.si.month');
														$calendar_days = Kohana::config('app.utils.calendar.si.days_full');
														$shipping_date_day = date('w', $delivery_data['min_delivery_date']);
														$shipping_date_month = (date('n', $delivery_data['min_delivery_date']) - 1);

														if (!empty($calendar_days[$shipping_date_day]) AND !empty($calendar_month[$shipping_date_month])) {
															if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())) {
																$shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
															} elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))) {
																$shipping_date = date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
															} else {
																$shipping_date = $calendar_days[$shipping_date_day] . ', ' . date('d', $delivery_data['min_delivery_date']) . '.' . date('m', $delivery_data['min_delivery_date']) . '.';
															}
														} else {
															$shipping_date = strftime("%a %e %B", $delivery_data['min_delivery_date']);
														}
													}
												?>

												<div class="cp-tooltip-row <?php if($delivery_key == 'p'): ?>in-stores<?php elseif($delivery_key == 'bb'): ?> bb-shipping<?php elseif($delivery_key == 'bb_fast'): ?> delivery-premium<?php else: ?> delivery<?php endif; ?>">
													<div class="cp-tooltip-desc">
														<?php if ($delivery_key == 'p'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_pickup'); ?>

															<?php if(!empty($item['warehouses_single_pickup_display'])): ?>
																<?php echo str_replace('%s%', $item['warehouses_single_pickup_display']['title'], Arr::get($cmslabel, 'item_delivery_pickup_single')) ?>
															<?php endif; ?>
														<?php elseif ($delivery_key == 's'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_standard_delivery'); ?>
														<?php elseif ($delivery_key == 'e'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_express_delivery'); ?>
														<?php elseif ($delivery_key == 'bb'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_bigbang_delivery'); ?>
														<?php elseif ($delivery_key == 'bb_xxl'): ?>
															<?php echo Arr::get($cmslabel, 'item_delivery_bigbang_xxl_delivery'); ?>
														<?php elseif ($delivery_key == 'bb_fast'): ?>
															<?php echo str_replace('%s%', implode($item['shipping_options']['bb_fast']['fast_shipping_titles']), Arr::get($cmslabel, 'item_delivery_premium')); ?>
														<?php endif; ?>
														
														<?php if ($delivery_key != 'bb_fast'): ?>
															<?php if (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', time())):  ?>
																<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
															<?php elseif (date('Y-m-d', $delivery_data['min_delivery_date']) == date('Y-m-d', strtotime('+1day'))):  ?>
																<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
															<?php else: ?>
																<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
															<?php endif; ?>
														<?php endif; ?>

														<?php /*if ($delivery_key == 'p'): ?>
															- <strong><?php echo Arr::get($cmslabel, 'free'); ?></strong>
														<?php else: ?>
															- <strong><?php echo Utils::currency_format($delivery_data['shipping_price'] * $currency['exchange'], $currency['display']); ?></strong>
														<?php endif;*/ ?>
													</div>

													<?php if($delivery_key == 'bb_fast' AND !empty($cmslabel['item_delivery_premium_note'])): ?>
														<div class="cp-tooltip-note"><?php echo Arr::get($cmslabel, 'item_delivery_premium_note'); ?></div>
													<?php endif; ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>
									<?php endif; ?>
								<?php endforeach; ?>
							<?php elseif ($availability_status == 4): ?>
								<span class="cp-tooltip-empty"><?php echo Arr::get($cmslabel, 'item_delivery_limited_stock'); ?></span>
							<?php else: ?>
								<span class="cp-tooltip-empty"><?php echo Arr::get($cmslabel, 'no_available_deliveries'); ?></span>
							<?php endif; ?>
						</div>
					</div>
				</div>
			<?php endif; ?>

			<?php if (!empty($item['seller_id']) AND $mode != 'related' AND $mode != 'slider'): ?>
				<div class="cp-seller">
					<?php if(!empty($item['seller_corporate_name'])): ?>
						<span><?php echo Arr::get($cmslabel, 'seller_item_title'); ?> <a href="<?php echo $item['seller_url']; ?>"><?php echo $item['seller_corporate_name']; ?></a></span>
					<?php endif; ?>
					<?php if(!empty($item['seller_title'])): ?>
						<span class="extra-name">(<?php echo $item['seller_title']; ?>)</span>
					<?php endif; ?>
				</div>
			<?php endif; ?>
			
			<?php if($mode != 'slider'): ?>
				<div class="cp-bottom">
					<!-- Compare -->
					<?php if (isset($item['compare_widget']) AND empty($compare_page) AND $mode != 'related'): ?>
						<?php echo View::factory('catalog/widget/set_compare', $item['compare_widget']); ?>
					<?php endif; ?>

					<?php if ($mode == 'related'): ?>
						<div class="cp-qty<?php if((int) $item['available_qty'] < 2): ?> cpd-qty-hidden<?php endif; ?>">
							<?php $aq = $item['available_qty']; ?>
							<div class="cp-qty1">
								<select name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="product_qty_input" onclick="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', 'select', 1)">
									<?php for ($i = 1; $i <= $aq && $i < 6; $i++): ?>
										<option value="<?php echo $i; ?>"><?php echo $i; ?></option>
									<?php endfor; ?>
									<?php if($aq > 5): ?>
										<option class="qty-toggle" value="toggle"> +</option>
									<?php endif; ?>
								</select>
							</div>

							<div class="cp-qty2">
								<span class="cp-qty-note" name="qty[<?php echo $item['shopping_cart_code']; ?>]"><?php echo Arr::get($cmslabel, 'enter_qty'); ?></span>
								<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="cp-input-qty product_qty_input" value="1" />
							</div>
						</div>
					<?php endif; ?>

					<?php if (!empty($item['is_available'])): ?>
						<?php
							$add_to_cart_label = 'add_to_shopping_cart';
							$add_to_cart_status = 'addtocart';
							if (in_array($item['type'], ['advanced', 'configurable'])) {
								$add_to_cart_label = 'add_to_shopping_cart_configurable';
								$add_to_cart_status = 'details';
							} elseif ($item['status'] == '5') {
								$add_to_cart_label = 'add_to_shopping_cart_preorder';
								if(empty($item['is_available'])) {
									$add_to_cart_status = 'details';
								}
							}
						?>
						<a class="btn btn-green cp-btn-addtocart <?php echo $add_to_cart_status; ?>" title="<?php echo Arr::get($cmslabel, $add_to_cart_label); ?>" href="<?php if (in_array($item['type'], ['advanced', 'configurable'])): ?><?php echo $item['url']; ?><?php elseif ($mode == 'related'): ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 3)<?php else: ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'simple', 3)<?php endif; ?>">
							<span><?php echo Arr::get($cmslabel, $add_to_cart_label); ?></span>
						</a>
					<?php else: ?>
						<a class="btn btn-green cp-btn-addtocart cp-btn-details" title="<?php echo Arr::get($cmslabel, 'read_more'); ?>" href="<?php echo $item['url']; ?>">
							<span><?php echo Arr::get($cmslabel, 'read_more'); ?></span>
						</a>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</div>
		<?php if (!empty($compare_page)): ?>
			<div class="c-compare-m-btns">
				<a href="javascript:void(0);" data-compare_mode="all" class="c-compare-btn c-compare-btn-all active"><?php echo Arr::get($cmslabel, 'compare_all_details'); ?><span></span></a>
				<a href="javascript:void(0);" data-compare_mode="diff" class="c-compare-btn c-compare-btn-diff"><?php echo Arr::get($cmslabel, 'compare_difference'); ?><span></span></a>
			</div>
			<div class="cp-compare-attributes cp-attributes-compare<?php /*if(count($item) < 2): ?> cp-attributes-compare-single<?php endif; */?>">
				<table class="table-cp-attributes">
					<?php
					$item_attributes = array_flip(array_keys($attributes));
					foreach ($item['attributes'] AS $attribute) {
						if (isset($item_attributes[$attribute['attribute_code']])) {
							$item_attributes[$attribute['attribute_code']] = [$attribute['attribute_title'], $attribute['title']];
						}
					}
					?>

					<?php $j = 1; ?>
					<?php foreach ($item_attributes AS $attribute_code => $attribute_data): ?>
						<?php if (is_array($attribute_data)): ?>
							<tr class="attr-row active attr-row<?php echo $j; ?>" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-title"><span class="col-attribute-title"><?php echo $attribute_data[0]; ?></span><span class="col-attribute-value"><?php echo $attribute_data[1]; ?></span></td>
							</tr>
						<?php else: ?>
							<tr class="attr-row active attr-row<?php echo $j; ?> attr-row-empty" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-title">&nbsp;</td>
							</tr>
						<?php endif; ?>
						<?php $j++; ?>
					<?php endforeach; ?>
				</table>
			</div>
		<?php endif ?>
	</article>

	<?php if (!empty($pagination) AND $pagination->current_page == 1 AND in_array($i, [6, 12, 18]) AND !empty($kind['rotator_elements'])): ?>
		<?php $rotator_element = array_shift($kind['rotator_elements']); ?>
		<?php $rotator_element_title = str_replace('"', "'", Text::meta($rotator_element['title'])); ?>
		<?php if ($rotator_element['type'] == 'p'): ?>
			<div class="cp cp-promo">
				<?php if(!empty($rotator_element['url'])): ?><a href="<?php echo $rotator_element['url']; ?>" data-tracking_gtm_promo_click="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - product"><?php endif; ?>
					<?php if(!empty($rotator_element['image'])): ?>
						<figure data-tracking_gtm_promo_view="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - product">
							<span><img loading="lazy" <?php echo Thumb::generate($rotator_element['image'], ['width' => 355, 'height' => 595, 'crop' => TRUE, 'html_tag' => TRUE, 'srcset' => '710c 2x']); ?> alt="<?php echo $rotator_element['title']; ?>" /></span>
						</figure>
					<?php endif; ?>
				<?php if(!empty($rotator_element['url'])): ?></a><?php endif; ?>
			</div>
		<?php elseif ($rotator_element['type'] == 'i'): ?>
			<div class="cp cp-promo cp-promo-inline">
				<?php if(!empty($rotator_element['url'])): ?><a href="<?php echo $rotator_element['url']; ?>" data-tracking_gtm_promo_click="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - inline"><?php endif; ?>
					<?php if(!empty($rotator_element['image'])): ?>
						<figure data-tracking_gtm_promo_view="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - inline">
							<span><img loading="lazy" <?php echo Thumb::generate($rotator_element['image'], ['width' => 1105, 'crop' => false, 'html_tag' => TRUE, 'srcset' => '2210c 2x']); ?> alt="<?php echo $rotator_element['title']; ?>" /></span>
						</figure>
					<?php endif; ?>
				<?php if(!empty($rotator_element['url'])): ?></a><?php endif; ?>
			</div>
		<?php endif; ?>
	<?php endif; ?>
	<?php if (!empty($compare_page) AND $i == 3): ?>
		<?php break; ?>
	<?php endif ?>
	<?php $i++; ?>
<?php endforeach; ?>