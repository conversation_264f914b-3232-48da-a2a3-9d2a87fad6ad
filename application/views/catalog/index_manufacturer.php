<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-manufacturers<?php $this->endblock('page_class'); ?>

<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>
<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
<div class="wrapper">
	<div class="m-header">
		<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	</div>

	<?php $manufacturers = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'special' => 1, 'limit' => 10)); ?>
	<?php if($manufacturers): ?>
		<div class="special-brands">
			<?php $m = 1; ?>
			<?php foreach ($manufacturers as $manufacturer):?>		
				<a class="sp-item" href="<?php echo $manufacturer['url']; ?>">
					<?php if(!empty($manufacturer['main_image'])): ?>
						<?php $fileExtension = pathinfo($manufacturer['main_image'], PATHINFO_EXTENSION); ?>		
						<?php if($fileExtension == 'svg'): ?>
							<img loading="lazy" src="<?php echo Utils::file_url($manufacturer['main_image']); ?>" alt="<?php echo $manufacturer['title']; ?>">
						<?php else: ?>
							<span class="lloader">
								<span><img loading="lazy" <?php echo Thumb::generate($manufacturer['main_image'], array('width' => 170, 'height' => 80, 'default_image' => '/media/images/no-image-160.webp', 'placeholder' => '/media/images/no-image-160.webp', 'srcset' => '340c 2x')); ?> alt="<?php echo $manufacturer['title']; ?>" /></span>
							</span>
						<?php endif; ?>
					<?php else: ?>
						<span><?php echo $manufacturer['title']; ?></span>
					<?php endif; ?>
				</a>
				<?php $m++; ?>
			<?php endforeach; ?>
		</div>
	<?php endif; ?>
</div>

<?php $manufacturers_alphabet = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'limit' => 0, 'with_total' => true, 'sort' => 'title', 'hierarhy_by_alphabet' => true)); ?>
<div class="m-alphabet">
	<div class="wrapper">
		<div class="ma-title"><span><?php echo Arr::get($cmslabel, 'alphabet_title'); ?></span></div>
		<div class="ma-items">
			<?php foreach($manufacturers_alphabet as $alphabet => $manufacturers): ?>
				<a href="#<?php echo $alphabet; ?>" class="ma-item"><span><?php echo $alphabet; ?></span></a>
			<?php endforeach; ?>
		</div>
	</div>
</div>

<div class="wrapper">
	<div class="m-items">
		<?php if (sizeof($manufacturers_alphabet)): ?>
			<?php foreach($manufacturers_alphabet as $alphabet => $manufacturers): ?>
				<div class="m-column">
					<div class="m-letter" id="<?php echo $alphabet; ?>"><span><?php echo $alphabet; ?></span></div>
					<?php $i = 1; ?>
					<div class="m-list-section">
						<ul class="m-list">
							<?php foreach($manufacturers as $manufacturer): ?>
								<li><a href="<?php echo $manufacturer['url']; ?>"><?php echo $manufacturer['title']; ?></a></li>
								
								<?php $i++; ?>
							<?php endforeach; ?>
						</ul>
					</div>
				</div>
			<?php endforeach; ?>
			<div class="clear"></div>
		<?php else: ?>
			<?php echo Arr::get($cmslabel, 'no_manufacturers'); ?>
		<?php endif; ?>
	</div>
</div>
<?php $this->endblock('content_layout'); ?>