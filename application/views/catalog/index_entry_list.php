<?php 
$mode = (isset($mode)) ? $mode : ''; 
$product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); 
$i = (!empty($pagination)) ? (($pagination->current_page - 1) * $pagination->items_per_page) + 1 : 1;

// coupon
list($list_coupons, $item_coupon_ids) = Catalog::listitems_coupon(array_keys($items));
$is_loyalty = (!empty($user->loyalty_code));

$shipping_service_enabled = Kohana::config('app.webshop.shipping_service_enabled');
$shipping_bigbang_enable = false;
if (!empty($shipping_service_enabled['bigbang_dostavna']) AND !empty($item['services_ids'])) {
    foreach ($shipping_service_enabled['bigbang_dostavna'] AS $shipping_service_bigbang_dostavna) {
        if (!empty($item['services_ids'][$shipping_service_bigbang_dostavna])) {
            $shipping_bigbang_enable = true;
            break;
        }
    }
}
?>

<?php foreach ($items as $item): ?>
	<article class="clear cp-list<?php if($mode == 'wishlist'): ?> wishlist-mode<?php endif; ?>"<?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?> data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo $mode; ?>">
		<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
        <span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
        <span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'title')); ?></span>
        <span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'code'); ?></span>
		<span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
		
		<?php $attributes_special = $item['attributes_special']; ?>
		<?php $superbenefits = 0; ?>
		<?php $img = 0; ?>

		<div class="cp-list-col1">	
			<?php /* ?>
				<?php foreach($item['attributes_special'] as $attr): ?>
					<?php if($attr['attribute_code'] == 'superbenefits'): ?>
						<div class="cp-badge cp-badge-list cp-badge-superbenefit"><span><?php echo $attr['title']; ?></span></div>
						<?php $superbenefits = 1; ?>
					<?php endif ?>

					<?php if($attr['attribute_image']): ?>
						<?php $img++; ?>
					<?php endif; ?>
				<?php endforeach; ?>

				<?php if(!$superbenefits): ?>
					<?php $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : '')); ?>
					<?php if ($priority): ?>
						<div class="cp-badge cp-badge-list <?php echo Arr::get($priority, 'code'); ?>"><span><?php echo Arr::get($priority, 'title'); ?></span></div>
					<?php endif; ?>
				<?php endif; ?>
		
				<div class="cp-attributes cp-list-attributes">
					<?php foreach($item['attributes_special'] as $attr): ?>
						<?php if(in_array($attr['attribute_code'], ['attribute_badges'])): ?>
							<div class="cp-attribute">
								<img <?php echo Thumb::generate($attr['image'], array('width' => 60, 'height' => 60, 'crop' => true, 'html_tag' => TRUE, 'srcset' => '120c 2x')); ?> alt="<?php echo $attr['title']; ?>" />
							</div>
						<?php endif ?>
					<?php endforeach; ?>
			</div>
			<?php */ ?>

			<?php 
			$priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : ''));
			$badges_special = (!empty($item['badges'])) ? $item['badges'] : []; 
			$badges_special_1 = [];

			$badges_special_2 = [];
			if (!empty($badges_special)) {
				foreach ($badges_special AS $badge_special_id => $badge_special_data) {
					if ((int)$badge_special_data['category'] <= 5) {
						if ((int)$badge_special_data['category'] == 5 AND !empty($priority['code']) AND $priority['code'] == 'new') {
							$badges_special_1[4]['category'] = '4';
						}
						// preorder
						if ($badge_special_data['category'] == 1) {
							if (!empty($item['status']) AND !empty($item['date_available']) AND $item['status'] == '5') {
								$badges_special_1[$badge_special_id] = $badge_special_data;
							}
						} else if ($badge_special_data['category'] == 2) {
							if ($list_coupons) {
								$coupon_ids = Arr::get($item_coupon_ids, $item['id']);
								if (!empty($coupon_ids)) {
									$coupon_ids = array_filter(explode(',', $coupon_ids));
									$item_coupons = Arr::extract($list_coupons, $coupon_ids);
									$item_coupon = reset($item_coupons);
									$badge_special_data['coupon_code'] = $item_coupon['coupon_code'];
									$badge_special_data['coupon_discount_percent'] = (int)$item_coupon['coupon_discount_percent'];
									$badge_special_data['coupon_active_to'] = $item_coupon['coupon_active_to'];
									
									$badges_special_1[$badge_special_id] = $badge_special_data;
								}
							}						
						} else {
							$badges_special_1[$badge_special_id] = $badge_special_data;
						}
					} else {
						$badges_special_2[$badge_special_id] = $badge_special_data;
					}
				}

				if (!empty($priority['code']) AND $priority['code'] == 'new' AND !empty($badges_special_1[4])) {
					$badges_special_1[4]['category'] = '4';
				}

				if (count($badges_special_1) > 2) {
					$badges_special_1 = array_slice($badges_special_1, 0, 2, true);
				}
				if (count($badges_special_2) > 3) {
					$badges_special_2 = array_slice($badges_special_2, 0, 3, true);
				}
			} else {
				if (!empty($priority['code']) AND $priority['code'] == 'new') {
					$badges_special_1[4]['category'] = '4';
				}
			}		

			?>

			<?php if(!isset($badges) OR !empty($badges_special)): ?>
				<div class="cp-badges cp-badges-list">
					<?php if (!empty($badges_special_1)): ?>
						<?php foreach($badges_special_1 as $badge_special): ?>
							<?php if ($badge_special['category'] == 4): ?>
								<div class="cp-badge cp-badge-list cp-badge-new">
									<span><?php echo $priority['title']; ?></span>
								</div>
							<?php else: ?>
								<?php if(!empty($badge_special['label_title'])): ?>
									<?php if(!empty($badge_special['badge_url'])): ?>
									<a href="<?php echo ($badge_special['badge_url']); ?>" class="cp-badge cp-badge-list link<?php if($badge_special['category'] == 1): ?> green<?php endif; ?>">
									<?php else: ?>
									<div class="cp-badge cp-badge-list<?php if($badge_special['category'] == 1): ?> green<?php endif; ?>">
									<?php endif; ?>
										<span><?php echo($badge_special['label_title']); ?></span>
										<?php $hoverTextEmpty = ($badge_special['label_title_hover'] == '/'); ?>
										<?php if(!empty($badge_special['label_title_hover']) AND !$hoverTextEmpty): ?>
											<div class="cp-badge-tooltip cp-badge-tooltip-list"><?php echo($badge_special['label_title_hover']); ?></div>
										<?php elseif(empty($badge_special['label_title_hover']) AND !empty($item['date_available']) AND !$hoverTextEmpty AND ($badge_special['category'] == 1)): ?>
											<div class="cp-badge-tooltip cp-badge-tooltip-list"><?php echo Arr::get($cmslabel, 'izid'); ?> <strong><?php echo ($item['date_available']); ?></strong></div>
										<?php elseif(empty($badge_special['label_title_hover']) AND (!empty($badge_special['coupon_code']) OR !empty($badge_special['coupon_discount_percent'])) AND !$hoverTextEmpty AND ($badge_special['category'] == 2)): ?>
											<div class="cp-badge-tooltip cp-badge-tooltip-list">
												<?php if(!empty($badge_special['coupon_code'])): ?>
													<?php echo($badge_special['coupon_code']); ?> 
												<?php endif; ?>
												<?php if(!empty($badge_special['coupon_discount_percent'])): ?>
													(
													<?php if ($item['discount_percent_custom'] >= 2): ?>
														<?php echo Arr::get($cmslabel, 'prihrani_se'); ?> 
													<?php else: ?>
														<?php echo Arr::get($cmslabel, 'prihrani'); ?> 
													<?php endif; ?>
													<strong><?php echo($badge_special['coupon_discount_percent']); ?> %</strong>)
												<?php endif; ?>
											</div>
										<?php endif; ?>
									<?php if(!empty($badge_special['badge_url'])): ?>
									</a>
									<?php else: ?>
									</div>
									<?php endif; ?>
								<?php endif; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<div class="cp-image-container">
				<figure class="cp-image cp-image-list">
					<a href="<?php echo $item['url']; ?>">
						<img data-lazy="<?php echo Thumb::generate($item['main_image'], 240, 240, false, 'thumb', TRUE, '/media/images/no-image-240.webp'); ?>" <?php echo Thumb::generate($item['main_image'], array('width' => 240, 'height' => 240, 'default_image' => '/media/images/no-image-240.webp', 'placeholder' => '/media/images/no-image-240.webp', 'srcset' => '480c 2x')); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" />
					</a>
				</figure>

				<?php if (!empty($badges_special_2)): ?>
					<div class="cp-badges-special cp-badges-list-special">
						<?php foreach($badges_special_2 as $badge_special): ?>
							<?php if($badge_special['badge_image']): ?>
								<div class="cp-badge-special">
									<?php if($badge_special['badge_url']): ?><a href="<?php echo($badge_special['badge_url']); ?>"><?php else: ?><span><?php endif; ?>
										<?php $fileExtension = pathinfo($badge_special['badge_image'], PATHINFO_EXTENSION); ?>
										<?php if($fileExtension == 'svg'): ?>
											<img loading="lazy" src="<?php echo Utils::file_url($badge_special['badge_image']); ?>" alt="<?php echo $badge_special['label_title']; ?>">
										<?php else: ?>
											<img loading="lazy" <?php echo Thumb::generate($badge_special['badge_image'], array('width' => 40, 'height' => 40, 'html_tag' => TRUE, 'srcset' => '80r 2x')); ?> alt="<?php echo Text::meta($badge_special['label_title']); ?>" />
										<?php endif; ?>
									<?php if($badge_special['badge_url']): ?></a><?php else: ?></span><?php endif; ?>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<div class="cp-list-col2">
			<div class="cp-list-row1">
				<div class="cp-list-cnt">
					<div class="cp-list-cnt-comment">
						<!-- Rating -->
						<?php if (isset($item['feedback_rate_widget'])): ?>
							<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
						<?php endif; ?>
						<!-- Comments -->
						<?php if (isset($item['feedback_comment_widget'])): ?>
							<div class="cp-comment-count comments-count catalog-comment-count">
								<a title="<?php echo Arr::get($cmslabel, 'comments_num'); ?>" href="<?php echo $item['url']; ?>#comments_data_<?php echo Arr::get($item['feedback_comment_widget'], 'content'); ?>">
									(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
								</a>
							</div>
						<?php endif; ?>
					</div>

					<div class="cp-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>
					<?php if(!empty($item['category_title'])): ?>
						<div class="cp-category cp-list-category"><?php echo $item['category_title']; ?></div>	
					<?php endif; ?>
					<h2 class="cp-title cp-list-title"><a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></a></h2>
				
					<?php if($attributes_special): ?>
						<div class="cp-list-special-attrs<?php if($img == 0): ?> cp-list-special-attrs2<?php endif; ?>">
							<?php $a = 1; ?>
							<?php foreach ($attributes_special AS $attr): ?>
								<?php if(!in_array($attr['attribute_code'], ['barva-48', 'barva-pim-6698538', 'attribute_badges', 'superbenefits', 'ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'])): ?>
									<div class="cp-list-special-attr">
										<?php if ($attr['attribute_image'] AND $img > 0): ?>
											<div class="cp-list-special-attr-img"><img loading="lazy" src="<?php echo Utils::file_url($attr['attribute_image']); ?>" /></div>
										<?php endif; ?>
										<div class="cp-list-special-attr-cnt">
											<span class="cp-list-special-attr-title"><?php echo $attr['attribute_title']; ?><?php if($img < 1): ?>: <?php endif; ?></span>
											<span class="cp-list-special-attr-value"><?php echo $attr['title']; ?></span>
										</div>
									</div>
								<?php if($a == 4) break; ?>
								<?php $a++; ?>
								<?php endif; ?>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>

					<!-- Wishlist -->
					<?php if (!empty($item['wishlist_widget']) AND $mode == 'wishlist'): ?>
						<div class="cp-wishlist wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
							<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2, '_tracking:index');" class="cp-btn cp-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
							<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', 'remove', '', 2, '_tracking:index');" class="cp-btn cp-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
							<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"></span>
						</div>
					<?php endif; ?>
				</div>

				<div class="cp-list-price-section">
					<?php if (!in_array($item['type'], ['advanced', 'configurable']) AND ((!empty($priority['code']) AND $item['discount_percent_custom'] >= 2) OR $item['discount_percent_custom'] >= 2 OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']))): ?>
                        <div class="cp-badge cp-list-badge cp-badge-discount<?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?> cp-list-badge-discount-loyalty cp-badge-discount-loyalty<?php endif; ?>">
                            <?php 
							$discount_percent = $item['discount_percent_custom'];
							$price_saved = ($item['basic_price_custom'] - $item['price_custom']);
							if (($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom'])) {
								$discount_percent = round((1 - ($item['loyalty_price_custom'] / $item['basic_price_custom'])) * 100, 0);
								$price_saved = ($item['basic_price_custom'] - $item['loyalty_price_custom']);
							}
							?>
							<span><?php if($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?><?php echo Arr::get($cmslabel, 'loaylty'); ?> <?php endif; ?>-<?php echo $discount_percent; ?> %</span>
							<div class="cp-badge-tooltip cp-badge-tooltip-list cp-badge-tooltip-discount cp-badge-tooltip-list-discount"><?php echo Arr::get($cmslabel, 'prihranek'); ?> <strong><?php echo Utils::currency_format($price_saved * $currency['exchange'], $currency['display']); ?></strong></div>
                        </div>
                    <?php endif; ?>

					<?php if($item['price_custom'] > 0): ?>
						<?php if(!$is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
							<div class="cp-loyalty-price-container cp-list-loyalty-price-container">
								<div class="cp-loyalty-price">
									<?php echo Arr::get($cmslabel, 'loyalty'); ?>: <?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
									<?php $loyalty_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['loyalty'])) ? (float)reset($item['installments_calculation']['loyalty']) : 0 * $currency['exchange'], $currency['display']); ?>
									<?php $loyalty_price = Utils::currency_format(Arr::get($item, 'loyalty_price_custom') * $currency['exchange'], $currency['display']); ?>
									<div class="cp-loyalty-tooltip cp-list-loyalty-tooltip">
										<span class="cd-shipping-info-close cp-loyalty-tooltip-close"></span>
										<?php if ($user AND !$is_loyalty): ?>
											<?php if(!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$item['price_custom'], $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php elseif (!empty($item['loyalty_price_custom'])): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') :  Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php endif; ?>
										<?php elseif (!$user AND !$is_loyalty): ?>
											<?php if(!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['installments_calculation']['loyalty'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php elseif (!empty($item['loyalty_price_custom'])): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
												<div class="cp-loyalty-tooltip-row special">
													<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'cp_loyalty_installments_text') : Arr::get($cmslabel, 'cp_loyalty_installments_text_no_rates')); ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>
										<?php if ($user AND !$is_loyalty): ?>
											<div class="cp-loyalty-tooltip-row">
												<?php echo Arr::get($cmslabel, 'loyalty_benefits_become_member'); ?>
												<div class="cp-loyalty-tooltip-login">
													<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'become_member'); ?></span></a>
												</div>
											</div>
										<?php elseif (!$user AND !$is_loyalty): ?>
											<div class="cp-loyalty-tooltip-row">
												<?php echo Arr::get($cmslabel, 'loyalty_benefits_login'); ?>
												<div class="cp-loyalty-tooltip-login">
													<a class="special" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', false); ?>"><?php echo Arr::get($cmslabel, 'login'); ?></a>
													<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><?php echo Arr::get($cmslabel, 'create_account'); ?></a>
												</div>
											</div>
										<?php endif; ?>
									</div>
								</div>
							</div>
						<?php endif; ?>

						<div class="cp-price cp-list-price">
                            <?php if (in_array($item['type'], ['advanced', 'configurable']) AND $item['basic_price_custom'] > $item['price_custom']): ?>
                                <div class="cp-current-price cp-variation-price">
                                    <span class="cp-old-price cp-price-label"><?php echo Arr::get($cmslabel, 'price_variation', 'Od'); ?></span>
                                    <span data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
                                </div>
							<?php else: ?>
								<?php if ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] > $item['loyalty_price_custom']): ?>
									<div class="cp-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
									<div class="cp-current-price cp-discount-price blue" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($installment_price)) ? str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'installments_price_text')) : ""; ?></div>
								<?php elseif ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
									<div class="cp-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
									<div class="cp-current-price cp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
								<?php else: ?>
									<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
								<?php endif; ?>
							<?php endif; ?>
						</div>
					<?php endif; ?>

					<?php if(!empty($item['attributes_special'])): ?>
						<div class="cp-list-energy">
							<?php
							$attr_title = [];
							$attr_img = [];
							foreach($item['attributes_special'] as $attr) {
								if(in_array($attr['attribute_code'], ['ucinek-pranja-in-su-100218739'])){
									$attr_title[0] = $attr['title'];
									$attr_img[0] = $attr['image'];
								}
								if(in_array($attr['attribute_code'], ['razred-energijske-u-100215480'])){
									$attr_title[1] = $attr['title'];
									$attr_img[1] = $attr['image'];
								}
								if (in_array($attr['attribute_code'], ['razred-energijske-u-100176542'])) {
									$attr_title[2] = $attr['title'];
									$attr_img[2] = $attr['image'];
								}
								if (in_array($attr['attribute_code'], ['razred-energij-ucinkov-35'])) {
									$attr_title[3] = $attr['title'];
									$attr_img[3] = $attr['image'];
								}
							}
							?>

							<?php if(!empty($attr_img) OR !empty($attr_title)): ?>
								<?php
								ksort($attr_img);
								ksort($attr_title);
								$attr_img = reset($attr_img);
								$attr_title = reset($attr_title);
								?>
								<?php if(!empty($attr_img)): ?>
									<img loading="lazy" width="80" height="25" src="<?php echo Utils::file_url($attr_img); ?>" alt="<?php echo $attr_title; ?>">
								<?php endif; ?>
								<?php if(!empty($attr_title) AND empty($attr_img)): ?>
									<?php echo $attr_title; ?>
								<?php endif; ?>
							<?php endif; ?>
						</div>
					<?php endif; ?>
						
					<div class="available-qty list-available-qty">
						<div class="available-qty-btn">
                            <?php if (!empty($item['last_piece_sale'])): ?>
								<span><?php echo Arr::get($cmslabel, 'odprodaja'); ?></span>
							<?php elseif($item['is_available']): ?>
								<?php if ($item['status'] == '5'): ?>
									<?php /*<span><?php echo str_replace('%d%', $item['date_available'], Arr::get($cmslabel, 'date_available')) ?></span> */ ?>
									<span>
										<?php if (!empty($item['available_qty_counter']) AND (int)$item['available_qty'] <= $item['available_qty_counter']): ?>
											<?php 
											$available_qty = (int)$item['available_qty'];
											$na_voljo_texts = array_filter(explode("\n", Arr::get($cmslabel, 'na_voljo_qty')));

											$na_voljo_text = ($available_qty > count($na_voljo_texts)) 
												? $na_voljo_texts[count($na_voljo_texts) - 1] 
												: $na_voljo_texts[$available_qty - 1];

											$na_voljo_text = str_replace('%QTY%', $available_qty, $na_voljo_text);
											echo $na_voljo_text
											?>
										<?php else: ?>
											<?php echo Arr::get($cmslabel, 'na_voljo'); ?>
										<?php endif; ?>
									</span>								
								<?php elseif (!empty($item['warehouses_ids']) AND ((int) $item['max_qty_per_location'] > 1 OR ((int) $item['max_qty_per_location'] == 1 AND strpos($item['warehouses_ids'], ',1010=') !== false))): ?>
									<span><?php echo Arr::get($cmslabel, 'na_zalogi'); ?></span>
								<?php else: ?>
									<span><?php echo Arr::get($cmslabel, 'na_zalogi_dobavitelj'); ?></span>
								<?php endif; ?>
							<?php elseif ((!empty($item['max_qty_per_location'])) AND (int) $item['max_qty_per_location'] > 0): ?>
								<span class="available-last"><?php echo Arr::get($cmslabel, 'na_zalogi_ena'); ?></span>
							<?php elseif ($item['status'] == '7' AND (!empty($item['max_qty_per_location']) AND (int) $item['max_qty_per_location'] == 0)): ?>
								<span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi'); ?></span>
							<?php elseif($item['status'] == '5'): ?>
								<span class="unavailable"><?php echo Arr::get($cmslabel, 'ni_na_zalogi_preorder'); ?></span>
							<?php endif; ?>

							<?php $locations = (isset($item['warehouses']) AND $item['warehouses']) ? array_filter(array_map(function($element){return ($element['available_qty'] > 0) ? $element : null;}, $item['warehouses'])) : array(); ?>
							<?php $delivery_premium = false; ?>
							<?php if (!empty($badges_special_2)): ?>
								<?php foreach($badges_special_2 as $badge_special): ?>
									<?php if($badge_special['code'] == '120638'): ?>
										<?php $delivery_premium = true; ?>
									<?php endif; ?>
								<?php endforeach; ?>
							<?php endif; ?>
							<?php if (!empty($item['shipping_date']) OR !empty($locations) OR $delivery_premium): ?>
								<div class="cp-available-tooltip cp-list-available-tooltip cp-tooltip">
									<div class="cd-shipping-info-close"></div>
									<?php if ($item['status'] == '5'): ?>
										<?php if (!empty($item['shipping_date'])): ?>
											<div class="cp-tooltip-row delivery">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_delivery'); ?></strong></div>
												<div class="cp-tooltip-desc">
													<?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?>
												</div>
											</div>

											<div class="cp-tooltip-row in-stores">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_in_stores'); ?></strong></div>
												<div class="cp-tooltip-desc">
													<?php echo str_replace('%s%', date('d.m.Y', $item['shipping_date']), Arr::get($cmslabel, 'item_time_of_delivery_preorder')) ?>
												</div>
											</div>
										<?php endif; ?>
									<?php else: ?>
										<?php if (!empty($item['shipping_date'])): ?>
											<div class="cp-tooltip-row delivery">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_delivery'); ?></strong></div>

												<?php
												$calendar_month = Kohana::config('app.utils.calendar.si.month');
												$calendar_days = Kohana::config('app.utils.calendar.si.days_full');
												$shipping_date_day = date('w', $item['shipping_date']);
												$shipping_date_month = (date('n', $item['shipping_date']) - 1);

												if (!empty($calendar_days[$shipping_date_day]) AND !empty($calendar_month[$shipping_date_month])) {
													$shipping_date = $calendar_days[$shipping_date_day].' '.date('d', $item['shipping_date']).'. '.$calendar_month[$shipping_date_month];
												} else {
													$shipping_date = strftime("%a %e %B", $item['shipping_date']);
												}
												?>
												<div class="cp-tooltip-desc">
													<?php if (date('Y-m-d', $item['shipping_date']) == date('Y-m-d', time())):  ?>
														<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_today')) ?>
													<?php elseif (date('Y-m-d', $item['shipping_date']) == date('Y-m-d', strtotime('+1day'))):  ?>
														<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery_tomorow')) ?>
													<?php else: ?>
														<?php echo str_replace('%s%', $shipping_date, Arr::get($cmslabel, 'item_time_of_delivery')) ?>
													<?php endif; ?>
													<?php if (!empty($cmslabel['item_tab_standardna_dostava'])): ?>
														<strong> - <?php echo Arr::get($cmslabel, 'item_tab_standardna_dostava'); ?></strong>
													<?php endif; ?>
												</div>
												<?php if(!empty($cmslabel['item_status_delivery_note'])): ?>
													<div class="cp-tooltip-note"><?php echo Arr::get($cmslabel, 'item_status_delivery_note'); ?></div>
												<?php endif; ?>
											</div>
										<?php endif; ?>

										<?php if (!empty($delivery_premium)): ?>
											<div class="cp-tooltip-row delivery-premium">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_delivery_premium'); ?></strong></div>
												<div class="cp-tooltip-desc">
													<?php echo Arr::get($cmslabel, 'item_status_delivery_premium_desc'); ?>
													<?php if (!empty($cmslabel['item_tab_hitra_dostava'])): ?>
														<strong> - <?php echo Arr::get($cmslabel, 'item_tab_hitra_dostava'); ?></strong>
													<?php endif; ?>
												</div>
											</div>
										<?php endif; ?>

										<!-- Product / variations warehouse availability -->
										<?php if ($locations): ?>
											<div class="cp-tooltip-row in-stores">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_in_stores'); ?></strong></div>
												<div class="cp-tooltip-desc">
													<?php echo Arr::get($cmslabel, 'item_status_warehouses_availability'); ?> <a href="<?php echo $item['url']; ?>#cd_location"><?php echo count($locations); ?> <?php echo Arr::get($cmslabel, 'warehouses_availability2'); ?></a>
													<?php if (!empty($cmslabel['item_tab_osebni_prevzem'])): ?>
														<strong> - <?php echo Arr::get($cmslabel, 'item_tab_osebni_prevzem'); ?></strong>
													<?php endif; ?>
												</div>
											</div>
										<?php endif; ?>
										<?php if (!empty($shipping_bigbang_enable)): ?>
											<div class="cp-tooltip-row bb-shipping">
												<div class="cp-tooltip-title"><strong><?php echo Arr::get($cmslabel, 'item_status_bb_delivery'); ?></strong></div>
												<div class="cp-tooltip-desc">
													<?php echo Arr::get($cmslabel, 'item_status_bb_delivery_desc'); ?>
													<?php if (!empty($cmslabel['item_tab_bb_dostava'])): ?>
														<strong> - <?php echo Arr::get($cmslabel, 'item_tab_bb_dostava'); ?></strong>
													<?php endif; ?>
												</div>
											</div>
										<?php endif; ?>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						</div>
					</div>

					<div class="cp-list-btns">
						<!-- Compare -->
						<?php if (isset($item['compare_widget'])): ?>
							<?php echo View::factory('catalog/widget/set_compare', $item['compare_widget']); ?>
						<?php endif; ?>
					
						<?php if ($item['is_available'] AND empty($item['variation_total'])): ?>
                            <span style="display: none;" data-product_category_title="1"><?php echo Arr::get($item, 'category_title'); ?></span>
                            <span style="display: none;" data-product_manufacturer_title="1"><?php echo Arr::get($item, 'manufacturer_title'); ?></span>
                            <?php
                            $add_to_cart_label = 'add_to_shopping_cart';
                            if (in_array($item['type'], ['advanced', 'configurable'])) {
                                $add_to_cart_label = 'add_to_shopping_cart_configurable';
                            } elseif ($item['status'] == '5') {
                                $add_to_cart_label = 'add_to_shopping_cart_preorder';
                            }
                            ?>
                            <a class="btn btn-green cp-btn-addtocart" title="<?php echo Arr::get($cmslabel, $add_to_cart_label); ?>" href="<?php if (in_array($item['type'], ['advanced', 'configurable'])): ?><?php echo $item['url']; ?><?php else: ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'simple', 3)<?php endif; ?>">
                                <span><?php echo Arr::get($cmslabel, $add_to_cart_label); ?></span>
                            </a>
						<?php else: ?>
							<a class="btn btn-green cp-btn-addtocart cp-btn-details" title="<?php echo Arr::get($cmslabel, 'read_more'); ?>" href="<?php echo $item['url']; ?>">
								<span><?php echo Arr::get($cmslabel, 'read_more'); ?></span>
							</a>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
	</article>

	<?php if (!empty($pagination) AND $pagination->current_page == 1 AND in_array($i, [6, 12]) AND !empty($kind['rotator_elements'])): ?>
		<?php $rotator_element = array_shift($kind['rotator_elements']); ?>
		<?php $rotator_element_title = str_replace('"', "'", Text::meta($rotator_element['title'])); ?>
        <?php if ($rotator_element['type'] == 'i'): ?>
			<div class="cp cp-list-promo cp-promo-inline">
				<?php if(!empty($rotator_element['url'])): ?><a href="<?php echo $rotator_element['url']; ?>" data-tracking_gtm_promo_click="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - inline"><?php endif; ?>
					<?php if(!empty($rotator_element['image'])): ?>
						<figure data-tracking_gtm_promo_view="<?php echo $rotator_element['id']; ?>|<?php echo $rotator_element_title; ?>|<?php echo $rotator_element['image']; ?>|categorylist - <?php echo $item['category_title']; ?> - inline">
							<span><img loading="lazy" <?php echo Thumb::generate($rotator_element['image'], ['width' => 1105, 'crop' => false, 'html_tag' => TRUE, 'srcset' => '2210c 2x']); ?> alt="<?php echo $rotator_element['title']; ?>" /></span>
						</figure>
					<?php endif; ?>
				<?php if(!empty($rotator_element['url'])): ?></a><?php endif; ?>
			</div>
        <?php endif; ?>
	<?php endif; ?>
	<?php $i++; ?>
<?php endforeach; ?>