<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item, 'schema_org' => true, 'schema_org_mode' => 'single_reviews', 'breadcrumb' => $breadcrumb]); ?><?php $this->endblock('seo'); ?>

<?php $images = Arr::get($item, 'all_images'); ?>
<?php $this->block('page_class'); ?> flyout-page page-catalog-detail<?php if($images AND count($images) > 1): ?> page-cd-zoom<?php endif; ?><?php $this->endblock('page_class'); ?>
<?php $this->block('breadcrumb_section'); ?> <?php $this->endblock('breadcrumb_section'); ?>

<?php $this->block('main'); ?>
	<?php $is_loyalty = (!empty($user->loyalty_code)); ?>
	<div class="cd-main">
		<div class="cd-row wrapper">
			<div class="cd-col1">
				<div class="cd-header">
					<div class="bc bc-short">
						<div class="wrapper-bc wrapper-cd-bc">
							<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
						</div>
					</div>
					<?php if ($item['type'] == 'advanced'): ?>
						<?php $item_type_advanced_selected = key($item["type_config"]); ?>
						<h1 class="cd-title">
							<?php if (!empty($item['primary_product_data'])): ?>
								<span data-product_title="1" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo $item['seo_h1'] ?></span>
							<?php endif; ?>
							<?php if (!empty($item["type_config"])): ?>
								<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
									<span data-product_title="<?php echo Arr::get($item_variation, 'shopping_cart_code_multiple_string', $item_variation['code']) ?>" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
										<?php echo $item['seo_h1'] ?><?php if (!empty($item_variation['title_extra'])): ?>, <?php echo $item_variation['title_extra']; ?><?php endif; ?>
									</span>
								<?php endforeach; ?>
							<?php endif; ?>
						</h1>
					<?php else: ?>
						<h1 class="cd-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h1>
					<?php endif; ?>

					<div class="cd-info">
						<!-- Product code number & cart attributes -->
						<?php if ($item['type'] == 'advanced'): ?>
							<?php if (!empty($item['primary_product_data'])): ?>
								<div class="cd-code" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>>
									<div><span class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</span> <span data-product_code="1"><?php echo $item['primary_product_data']['code']; ?></span></div>
								</div>
							<?php endif; ?>
							<?php if (!empty($item["type_config"])): ?>
								<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
									<div class="cd-code" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
										<div><span class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</span> <span data-product_code="<?php echo Arr::get($item_variation, 'shopping_cart_code_multiple_string', $item_variation['code']) ?>">
											<?php if (!empty($item['primary_product_data'])): ?><?php echo $item['primary_product_data']['code']; ?><?php if (!empty($item_variation['code_extra'])): ?>, <?php endif; ?><?php endif; ?><?php echo $item_variation['code_extra']; ?>
										</span></div>
									</div>
								<?php endforeach; ?>
							<?php endif; ?>
						<?php elseif ($item['type'] == 'configurable' AND !empty($item['primary_product_data'])): ?>
							<div class="cd-code" data-variation_box="code" data-variation_active_id="<?php echo $item['primary_product_id']; ?>">
								<?php if (!empty($item['type_config']['product_data'])): ?>
									<?php foreach ($item['type_config']['product_data'] AS $item_variation_code => $item_variation): ?>
										<?php $variation_selected = ($item['primary_product_data']['code'] == $item_variation['code']); ?>
										<div class="cd-code" data-variation_item_code="<?php echo $item_variation['shopping_cart_code']; ?>" <?php if (!$variation_selected): ?>style="display: none;"<?php endif; ?>>
											<span class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</span>
											<span><?php echo Arr::get($item_variation, 'code') ?></span>
										</div>
									<?php endforeach; ?>
								<?php else: ?>
									<div>
										<span class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</span>
										<span data-product_code="1"><?php echo $item['primary_product_data']['code']; ?></span>
									</div>
								<?php endif; ?>
							</div>
						<?php else: ?>
							<div class="cd-code">
								<div><span class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</span> <span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
							</div>
						<?php endif; ?>

						<!-- Product rating -->
						<?php if (isset($item['feedback_rate_widget'])): ?>
							<div class="cd-avarage-rate-section">
								<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
								<span class="cd-rate-average"><?php echo number_format($item['feedback_rate_widget']['rates'], 1); ?></span>
								<a class="cd-comments-link cd-tab-flyout-btn" data-flyout_btn_special="tab4" href="#tab4"><?php echo Arr::get($cmslabel, 'send_review'); ?></a>
							</div>
						<?php endif; ?>
					</div>
				</div>

				<div data-offer_selected_html="box_images">
					<?php echo View::factory('catalog/widget_detail/box_images', array('item' => $item)); ?>
				</div>


				<div data-offer_selected_html="box_badges">
					<?php echo View::factory('catalog/widget_detail/box_badges', array('item' => $item)); ?>
				</div>
			</div>

			<div class="cd-col2" data-tracking_gtm_impression="1|<?php echo $item['code']; ?>">
				<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
				<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
				<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>

				<div data-offer_selected_html="box_price">
					<?php echo View::factory('catalog/widget_detail/box_price', array('item' => $item)); ?>
				</div>

				<?php if(!empty($item['attributes_special'])): ?>
					<?php
					$attr_title = [];
					$attr_img = [];
					foreach($item['attributes_special'] as $attr) {
						if(in_array($attr['attribute_code'], ['ucinek-pranja-in-su-100218739'])){
							$attr_title[0] = $attr['title'];
							$attr_img[0] = $attr['image'];
						}
						if(in_array($attr['attribute_code'], ['razred-energijske-u-100215480'])){
							$attr_title[1] = $attr['title'];
							$attr_img[1] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energijske-u-100176542'])) {
							$attr_title[2] = $attr['title'];
							$attr_img[2] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energij-ucinkov-35'])) {
							$attr_title[3] = $attr['title'];
							$attr_img[3] = $attr['image'];
						}
					}
					?>

					<?php if(!empty($attr_img) OR !empty($attr_title)): ?>
						<?php
						ksort($attr_img);
						ksort($attr_title);
						$attr_img = reset($attr_img);
						$attr_title = reset($attr_title);
						?>
						<div class="cd-energy-info">
							<div class="cd-energy<?php if(!empty($item['energy_image'])): ?> link cd-flyout-btn<?php endif; ?>" data-flyout_code="energy">
								<?php if(!empty($attr_img)): ?>
									<img loading="lazy" width="70" height="26" src="<?php echo Utils::file_url($attr_img); ?>" alt="<?php echo $attr_title; ?>">
								<?php endif; ?>
								<?php if(!empty($attr_title) AND empty($attr_img)): ?>
									<?php echo $attr_title; ?>
								<?php endif; ?>
								<span><?php echo Arr::get($cmslabel, 'energy_title'); ?></span>
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<?php if(!empty($item['energy_image'])): ?>
					<div class="cd-energy-container cd-flyout" data-flyout="energy">
						<div class="cd-flyout-close"></div>
						<div class="cd-flyout-content">
							<div class="cd-flyout-header">
								<div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'energy_flyout_title'); ?></div>
							</div>
							<div class="cd-energy-container-img"><img loading="lazy" <?php echo Thumb::generate($item['energy_image'], array('width' => 300, 'height' => 585, 'html_tag' => TRUE, 'srcset' => '600r 2x')); ?> alt="<?php echo Arr::get($cmslabel, 'energy_title'); ?>" /></div>
							<div class="cd-flyout-bottom">
								<div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
							</div>
						</div>
					</div>
				<?php endif; ?>

				<div data-offer_selected_html="box_status">
					<?php echo View::factory('catalog/widget_detail/box_status', array('item' => $item)); ?>
				</div>

				<?php echo View::factory('catalog/widget_detail/box_add_to_cart_configurable', array('item' => $item)); ?>

				<div data-offer_selected_html="box_add_to_cart">
					<?php echo View::factory('catalog/widget_detail/box_add_to_cart', array('item' => $item)); ?>
				</div>

				<?php if(!empty($cmslabel['return_policy_value'])): ?>
					<div class="cd-return-policy">
						<?php echo Arr::get($cmslabel, 'return_policy_value'); ?>
						<?php if(!empty($cmslabel['return_policy_info'])): ?>
							<div class="cd-return-policy-link cd-flyout-btn" data-flyout_code="return-policy"><?php echo Arr::get($cmslabel, 'return_policy_link'); ?></div>
						<?php endif; ?>
					</div>
					<div class="cd-return-policy-flyout cd-flyout" data-flyout="return-policy">
						<div class="cd-flyout-close"></div>
						<div class="cd-flyout-content">
							<div class="cd-flyout-header">
								<div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'return_policy_flyout_title'); ?></div>
							</div>
							<?php echo Arr::get($cmslabel, 'return_policy_info'); ?>
							<div class="cd-flyout-bottom">
								<div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
							</div>
						</div>
					</div>
				<?php endif; ?>

				<?php if(!empty($cmslabel['companies_value'])): ?>
					<div class="cd-return-policy cd-companies<?php if(!empty($cmslabel['return_policy_value'])): ?> special<?php endif; ?>">
						<?php echo Arr::get($cmslabel, 'companies_value'); ?>
						<?php if(!empty($cmslabel['companies_info'])): ?>
							<div class="cd-return-policy-link"><?php echo Arr::get($cmslabel, 'companies_info'); ?></div>
						<?php endif; ?>
					</div>
				<?php endif; ?>

				<div data-offer_selected_html="box_shipping">
					<?php echo View::factory('catalog/widget_detail/box_shipping', array('item' => $item)); ?>
				</div>

				<div data-offer_selected_html="box_seller">
					<?php echo View::factory('catalog/widget_detail/box_seller', array('item' => $item)); ?>
				</div>

				<?php if(!empty($cmslabel['shop_extra_info'])): ?>
					<div class="cd-s-extra-info s-extra-info cd-flyout-btn" data-flyout_code="shop_extra_info">
						<span><?php echo Arr::get($cmslabel, 'shop_extra_info_pdp'); ?></span>
					</div>
				<?php endif; ?>
				<?php if(!empty($cmslabel['shop_extra_info'])): ?>
					<div class="s-extra-info-flyout cd-flyout" data-flyout="shop_extra_info">
						<div class="cd-flyout-close"></div>
						<div class="cd-flyout-content">
							<div class="cd-flyout-header">
								<div class="cd-flyout-header-title"><?php echo Arr::get($cmslabel, 'shop_extra_info_flyout_title_pdp'); ?></div>
							</div>
							<?php echo Arr::get($cmslabel, 'shop_extra_info_flyout_content_pdp'); ?>
							<div class="cd-flyout-bottom">
								<div class="cd-flyout-close-label"><?php echo Arr::get($cmslabel, 'flyout_close'); ?></div>
							</div>
						</div>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<!-- Related products -->
        <?php
        $related_items_request = json_encode(array(
            'lang' => $info['lang'],
            'related_code' => 'related',
            'related_item_id' => $item['id'],
            'related_widget_data' => Arr::get($item, 'related_widget_data'),
            'only_available' => true,
            'limit' => 10,
            'always_to_limit' => TRUE,
            'response_view' => 'catalog/index_entry',
            'response_extra_data' => ['class' => 'PDP', 'mode' => 'slider'],
        ));
        ?>
        <div class="cd-related-products wrapper" data-generate_related_products="related" data-generate_related_products_request="<?php echo base64_encode($related_items_request); ?>">
            <div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'); ?></div>
            <div class="cd-related-products-slider swipe-slider blazy-container" data-generate_related_products_box="related"></div>
        </div>

		<div data-offer_selected_html="box_content">
			<?php echo View::factory('catalog/widget_detail/box_content', ['item' => $item]); ?>
		</div>

		<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
	</div>

	<!-- Related posts -->
	<?php $related_posts = Widget_Publish::publishes(array('lang' => $info['lang'], 'related_code' => 'related', 'related_product_id' => $item['id'], 'extra_fields' => ['short_description'], 'limit' => 9)); ?>
	<?php if ($related_posts): ?>
		<div class="cd-related-posts">
			<div class="wrapper">
				<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_posts', 'Povezane objave'); ?></div>
				<div class="cd-related-post-slider blazy-container">
					<?php foreach ($related_posts as $item): ?>
						<?php echo View::factory('publish/index_entry_small', array('item' => $item, 'mode' => '')); ?>
					<?php endforeach; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<div class="cd-fixed-bar">
		<div class="cd-tab-wrapper"></div>
	</div>

<?php $this->endblock('main'); ?>