<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <title><?php echo $item['seo_title']; ?></title>
    <?php echo Html::media('fancybox,standard', 'css'); ?>
    <?php echo Html::media('modernizr', 'js'); ?>
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="gallery">

<div class="slider" id="slider" data-elem="slider">
    <div class="gallery-caption"><?php echo Arr::get($cmslabel, 'scroll_to_zoom'); ?></div>
    <div class="slides" data-elem="slides" data-options="preloaderUrl:/media/images/loader.svg;loop:false;maxZoom:3"></div>

    <?php
    $images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']);
    if (!empty($item['energy_image'])) {
        array_push($images, [
            "file" => $item['energy_image'],
            "url" => Utils::file_url($item['energy_image']),
            "kind" => "image",
            "title" => Arr::get($cmslabel, 'energy_image_title'),
            "description" => $item['energy_version'],
        ]);
    }

    if ($item['type'] == 'advanced' AND !empty($item["type_config"])) {
        foreach ($item["type_config"] AS $item_variation_code => $item_variation) {
            if (!empty($item_variation['products'])) {
                foreach ($item_variation['products'] AS $item_variation_product) {
                    if (!empty($item_variation_product['main_image'])) {
                        $item_variation_product_images = Utils::get_files('catalogproduct', $item_variation_product['id'], 'image', $info['lang']);
                        foreach ($item_variation_product_images AS $item_variation_product_image) {
                            array_push($images,
                                Arr::merge($item_variation_product_image, ['variation_code' => $item_variation_code])
                            );
                        }
                    }
                }
            }
        }
    }
    ?>
    <?php if(count($images) > 1): ?>
        <div class="gallery-sidebar" data-elem="thumbsHolder">
            <div class="gallery-thumbs"  data-last-index="<?php echo count($images); ?>-1" data-total-thumbs="<?php echo count($images); ?>">

                <?php $t = 0; ?>
                <?php foreach ($images as $file): ?>
                    <div data-slide-index="<?php echo $t; ?>" href="#" class="gallery-thumb<?php if($t == 0): ?> active<?php endif; ?>" <?php if (!empty($file['attributes_ids'])): ?> data-attributes_ids="<?php echo $file['attributes_ids']; ?>"<?php endif; ?>>
                        <span><img loading="lazy" <?php echo Thumb::generate($file['file'], array('width' => 133, 'height' => 133, 'default_image' => '/media/images/no-image-130.webp', 'html_tag' => TRUE, 'srcset' => '270c 2x')); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
                    </div>
                    <?php $t++; ?>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="gsZoomContainer">
        <div class="gsZoom gsZoomIn" data-elem="zoomIn" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0.5; cursor:default"> </div>
        <div class="gsZoom gsZoomOut" data-elem="zoomOut" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0.5; cursor:default"> </div>
    </div>

    <div class="gsControl gsPrev midLeft" data-elem="prev" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>
    <div class="gsControl gsNext midRight" data-elem="next" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>

    <ul data-elem="items">
        <?php foreach($images as $image): ?>
            <li><img loading="lazy" src="<?php echo Utils::file_url($image['file']); ?>" alt="<?php echo $image['title']; ?>"></li>
        <?php endforeach; ?>
    </ul>
</div>

<?php echo Html::media('js_gallery'); ?>
<script>
    $(window).load(function() {
        setTimeout(function() {
            var gallery = TouchNSwipe.getSlider("slider"),
                slideIndex = <?php echo (!empty($_GET['index'])) ? $_GET['index'] : 0; ?>,
                winHeight = $(window).height(),
                thumbsContainer = $('div.gallery-thumbs'),
                thumb = $('div.gallery-thumb');

            gallery.on(ImageSlider.INDEX_CHANGE, onIndexChange);


            var thumbSlider = $(".gallery-thumbs").slick({
                vertical: true,
                slidesToShow: 4,
                slidesToScroll: 1,
                infinite: false,
                verticalSwiping: true,
                responsive: [
                    {
                        breakpoint: 1200,
                        settings: {
                            vertical: false,
                            verticalSwiping: false,
                            slidesToShow: 4,
                            slidesToScroll: 4
                        }
                    }
                ]
            });

            gallery.index(slideIndex);
            goToThumb(slideIndex);

            thumb.on('click', function() {
                slideIndex = $(this).data('slide-index');
                gallery.index(slideIndex);

            });

            function onIndexChange() {
                var slideIndex = gallery.index();
                goToThumb(slideIndex);
            }

            function goToThumb(index) {

                thumbsContainer.slick('slickGoTo', index);
                thumb.removeClass('slick-current');
                thumbsContainer.find('[data-slick-index="'+index+'"]').addClass('slick-current');
                gallery.index(index);
            }

            $('.slick-prev, .slick-next').on('click', function() {
                var slideIndex = thumbSlider.slick('slickCurrentSlide');
                gallery.index(slideIndex);
            });

        }, 1000);
    });
</script>
<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>