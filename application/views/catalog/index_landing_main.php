<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-landing-catalog<?php $this->endblock('page_class'); ?>

<?php $this->block('main_header'); ?> <?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="wrapper">
		<?php echo View::factory('catalog/widget/categories'); ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('main_extra'); ?>
	<div class="wrapper">
		<?php echo View::factory('catalog/widget/catalog_promo', ['mode' => 'catalog_landing']); ?>
	</div>
<?php $this->endblock('main_extra'); ?>