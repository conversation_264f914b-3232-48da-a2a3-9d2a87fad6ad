<?php $items_layout_base_url = Url::query($_GET, FALSE, 'items_layout'); ?>
<?php $items_layout_base_url .= ($items_layout_base_url)  ? '&' : '?'; ?>
<?php if (sizeof($items)): ?>
    <div class="c-load-more-container c-load-before-container">
        <a href="javascript:void(0);" class="btn btn-lightBlue btn-medium load-more-previous btn-load-more" style="display: none;"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_before_catalog', 'Učitaj prijašnje proizvode')); ?></a>
    </div>

	<div id="items_catalog" class="fz0 c-items<?php if ($items_layout_sufix == '_list'): ?> c-items-list<?php endif; ?>" data-infinitescroll="items_catalog" data-infinitescroll_previous_page="<?php echo $pagination->previous_page; ?>"  data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="00">
		<?php echo View::factory('catalog/index_entry'.$items_layout_sufix, array('items' => $items, 'pagination' => $pagination, 'kind' => $kind)); ?>
	</div>

	<?php if($pagination): ?>
		<div class="c-load-more-container">
			<a href="javascript:void(0);" class="btn btn-lightBlue btn-medium load-more btn-load-more" style="display: none;"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_catalog', 'Učitaj još proizvoda')); ?></a>
			<div class="c-pagination-status-label">Prikazanih <span class="total_items_display fw-b"><?php echo $pagination->total_items_display; ?></span> od <span class="fw-b"><?php echo $pagination->total_items; ?></span> izdelkov</div>
			<?php echo $pagination; ?>
		</div>
	<?php endif; ?>
<?php else: ?>
	<!-- IS SEARCH -->
	<?php if ($q): ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'search_no_products'); ?></div>
	<?php else: ?>
		<div class="c-empty"><?php echo Arr::get($cmslabel, 'no_products'); ?></div>
	<?php endif; ?>
<?php endif; ?>