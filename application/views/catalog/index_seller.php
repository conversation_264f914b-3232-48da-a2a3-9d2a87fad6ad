<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('main_header'); ?>
	<div class="main-header seller-list-header">
		<div class="wrapper">
			<h1 class="seller-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
			<div class="seller-sort">
				<select name="seller-sort" name="sort" onchange="window.location.href=this.options[this.selectedIndex].value">
                    <option value="?"><?php echo Arr::get($cmslabel, 'ordering_top_rated'); ?></option>
					<option value="?sort=grade_weak" <?php if ($selected_sort == 'grade_weak'): ?>selected<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_weak_rated'); ?></option>
					<option value="?sort=az" <?php if ($selected_sort == 'az'): ?>selected<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_az'); ?></option>
					<option value="?sort=za" <?php if ($selected_sort == 'za'): ?>selected<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_za'); ?></option>
				</select>
			</div>
		</div>
	</div>
<?php $this->endblock('main_header'); ?>

<?php $this->block('content_layout'); ?>

	<div class="main-content wrapper">
		<?php if (sizeof($items)): ?>
			<div class="seller-items">
				<?php foreach($items as $item): ?>
					<?php if(!empty($item['url']) AND $item['status'] != 'Suspended'): ?><a href="<?php echo $item['url']; ?>" class="seller-item link"><?php else: ?><div class="seller-item"><?php endif; ?>
						<div class="seller-item-img">
							<?php if(!empty($item['main_image'])): ?>
								<?php $fileExtension = pathinfo($item['main_image'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img width="146px" height="146px" src="<?php echo Utils::file_url($item['main_image']); ?>" alt="<?php echo $item['title']; ?>">
								<?php else: ?>
									<img <?php echo Thumb::generate($item['main_image'], array('width' => 146, 'height' => 146, 'default_image' => '/media/images/no-image-146.webp', 'placeholder' => '/media/images/no-image-146.webp', 'srcset' => '292c 2x')); ?> alt="<?php echo $item['title']; ?>" />
								<?php endif; ?>
							<?php else: ?>
								<img src="/media/images/no-image-146.webp" alt="<?php echo $item['title']; ?>">
							<?php endif; ?>
						</div>
						<div class="seller-item-desc">
							<div class="seller-item-title">
								<?php if(!empty($item['corporate_name'])): ?>
									<span><?php echo $item['corporate_name']; ?></span>
								<?php endif; ?>
								<?php if(!empty($item['title'])): ?>
									<span class="extra-name">(<?php echo $item['title']; ?>)</span>
								<?php endif; ?>
							</div>
							<?php if($item['status'] == 'Closed' AND (!empty($item['closed_from']) OR !empty($item['closed_to']))): ?>
								<div class="seller-item-closed strong"><?php echo Arr::get($cmslabel, 'seller_closed'); ?> (<?php if(!empty($item['closed_from'])): ?><?php echo date('d.m.Y.', $item['closed_from']); ?><?php endif; ?><?php if(!empty($item['closed_to'])): ?> - <?php echo date('d.m.Y.', $item['closed_to']); ?><?php endif; ?>)</div>
							<?php endif; ?>

							<?php if(!empty($item['date_joined'])): ?>
								<?php
									$calendar_month = Kohana::config('app.utils.calendar.si.month');
									$calendar_year = Kohana::config('app.utils.calendar.si.year');
									$date_month = (date('n', $item['date_joined']) - 1);
									$date_year = (date('Y', $item['date_joined']));
									$seller_date = $calendar_month[$date_month] .' '. $date_year .'.';
								?>
								<?php /* ?>
								<div class="seller-item-date">
									<?php echo Arr::get($cmslabel, 'seller_date_joined'); ?>
									<strong><?php echo $seller_date; ?></strong>
								</div>
								<?php */ ?>
							<?php endif; ?>
							<div class="seller-item-info">
								<?php if($item['corporate_name']): ?><?php echo $item['corporate_name']; ?>, <?php endif; ?>
								<?php if($item['address']): ?><?php echo $item['address']; ?>, <?php endif; ?>
								<?php if($item['zipcode']): ?><?php echo $item['zipcode']; ?><?php endif; ?>
								<?php if($item['city']): ?> <?php echo $item['city']; ?>, <?php endif; ?>
								<?php if($item['shipping_country']): ?><?php echo $item['shipping_country']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> <?php endif; ?>
								<?php if($item['identification_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_identification_number'); ?>: </strong> <?php echo $item['identification_number']; ?><?php if($info['user_device'] != 'm'): ?>,<?php endif; ?> </span><?php endif; ?>
								<?php if($item['vat_number']): ?><span><strong><?php echo Arr::get($cmslabel, 'seller_vat_number'); ?>: </strong> <?php echo $item['vat_number']; ?></span><?php endif; ?>
							</div>
							<?php if(!empty($item['shipping_country'])): ?>
								<div class="seller-item-country">
									<?php echo Arr::get($cmslabel, 'seller_shipping_country'); ?>
									<strong><?php echo $item['shipping_country']; ?></strong>
								</div>
							<?php endif; ?>
						</div>
					<?php if(!empty($item['url']) AND $item['status'] != 'Suspended'): ?></a><?php else: ?></div><?php endif; ?>
				<?php endforeach; ?>
			</div>
		<?php else: ?>
			<div class="wrapper"><?php echo Arr::get($cmslabel, 'no_sellers'); ?></div>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('sidebar'); ?> <?php $this->endblock('sidebar'); ?>