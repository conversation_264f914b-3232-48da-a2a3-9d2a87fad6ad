<?php if ($template == 'detail'): ?>
    <?php if ($rates_status == Kohana::config('app.feedback.rates.item_status.enabled')): ?>
        <span class="cp-rate rates-container add_rate add_rate_<?php echo $content; ?>" data-rates="<?php echo $rates; ?>" data-rates_votes="<?php echo $rates_votes; ?>" data-rates_sum="<?php echo $rates_sum; ?>"></span>
    <?php elseif ($rates_status == Kohana::config('app.feedback.rates.item_status.archive') AND $rates): ?>
        <span class="cp-rate rates-container add_rate add_rate_<?php echo $content; ?>" data-rates="<?php echo $rates; ?>" data-rates_votes="<?php echo $rates_votes; ?>" data-rates_sum="<?php echo $rates_sum; ?>"></span>
    <?php endif; ?>
<?php else: ?>
    <?php if ($rates_status == Kohana::config('app.feedback.rates.item_status.enabled')): ?>
         <span class="cp-rate rates-container add_rate add_rate_<?php echo $content; ?>" data-rates="<?php echo $rates; ?>" data-rates_votes="<?php echo $rates_votes; ?>" data-rates_sum="<?php echo $rates_sum; ?>"></span>
    <?php elseif ($rates_status == Kohana::config('app.feedback.rates.item_status.archive') AND $rates): ?>
        <span class="cp-rate rates-container add_rate add_rate_<?php echo $content; ?>" data-rates="<?php echo $rates; ?>" data-rates_votes="<?php echo $rates_votes; ?>" data-rates_sum="<?php echo $rates_sum; ?>"></span>
    <?php endif; ?>
<?php endif; ?>