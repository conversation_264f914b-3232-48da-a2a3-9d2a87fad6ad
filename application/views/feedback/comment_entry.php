<?php $user_level = ''; ?>
<?php if(!empty($user)): ?>	
		<?php $user_level = $user->level; ?>
<?php endif; ?>

<article class="clear comment<?php if ($item['manager']): ?> comment-manager<?php endif; ?><?php if ($item['parent_id']): ?> comment-child<?php endif; ?>" id="comment-<?php echo $item['id']; ?>" 
    data-feedback_comment_entry_id="<?php echo $item['id']; ?>" 
    data-feedback_comment_entry_datetime="<?php echo $item['datetime_created']; ?>" 
    data-feedback_comment_entry_rate="<?php echo $item['rate']; ?>"
>
    <div class="comment-header">
        <?php if (!empty($item['rate'])): ?>
            <span class="cp-rate comment-rate fz0">
                <?php for ($rate = 1; $rate <=5; $rate++): ?>
                    <span class="icon-star-empty <?php if ($rate <= $item['rate']): ?> icon-star<?php endif ?>"></span>     
                <?php endfor; ?>
            </span>
        <?php endif; ?>
        <span class="comment-username<?php if ($item['manager']): ?> comment-username-manager<?php endif; ?>"><?php echo $item['display_name']; ?></span>
        <?php if (!$item['parent_id'] AND ($user_level == 8 OR $user_level == 9 OR $user_level == 10)): ?>
            <a class="comment-reply" href="javascript:cmsfeedback.replaycomment(<?php echo $item['id']; ?>);"><?php echo Arr::get($cmslabel, 'comment_reply', 'comment_reply'); ?></a>
        <?php endif; ?>
        <span class="comment-date"> <?php echo Date::humanize($item['datetime_created'], 'custom', 'd.m.Y.'); ?></span>
    </div>

    <div class="comment-message" id="comment-message-<?php echo $item['id']; ?>"><?php echo $item['message']; ?></div>
    
    <?php if ($item['can_reviews']): ?>
        <div class="comment-review">
            <span id="review_links_<?php echo $item['id']; ?>" class="review-links review_links">
                <a class="review-btn review_up" href="javascript:cmsfeedback.reviewcomment(<?php echo $item['id']; ?>, +1);">
                    <span class="label">
                        <?php if($info['user_device'] == 'm'): ?>
                            <?php echo Arr::get($cmslabel, 'commentreview_up_mobile', 'Da'); ?>
                        <?php else: ?>
                            <?php echo Arr::get($cmslabel, 'commentreview_up', 'Da'); ?>
                        <?php endif; ?>
                    </span>
                    <span id="review_comment_positive_<?php echo $item['id']; ?>" class="review-comment-qty review_comment review-positive<?php if($item['review_positive'] > 0): ?> active<?php endif; ?>">(<?php echo $item['review_positive']; ?>)</span>
                </a>
                <a class="review-btn review_down" href="javascript:cmsfeedback.reviewcomment(<?php echo $item['id']; ?>, -1);">
                    <span class="label">
                        <?php if($info['user_device'] == 'm'): ?>
                            <?php echo Arr::get($cmslabel, 'commentreview_down_mobile', 'Ne'); ?>
                        <?php else: ?>
                            <?php echo Arr::get($cmslabel, 'commentreview_down', 'Ne'); ?>
                        <?php endif; ?>
                    </span>
                    <span id="review_comment_negative_<?php echo $item['id']; ?>" class="review-comment-qty review_comment review-negative<?php if($item['review_negative'] > 0): ?> active<?php endif; ?>">(<?php echo $item['review_negative']; ?>)</span>
                </a>
            </span>
            <span id="review_comment_success_<?php echo $item['id']; ?>" style="display: none" class="comment-review-success"><?php echo Arr::get($cmslabel, 'commentreview_success'); ?></span>
        </div>
    <?php else: ?>
        <div class="comment-review disabled">
            <span id="review_links_<?php echo $item['id']; ?>" class="review-links review_links">
                <span class="review-btn review_up">
                    <span class="label"><?php echo Arr::get($cmslabel, 'commentreview_up', ''); ?> </span>
                    <span id="review_comment_positive_<?php echo $item['id']; ?>" class="review-comment-qty review_comment review-positive<?php if($item['review_positive'] > 0): ?> active<?php endif; ?>">(<?php echo $item['review_positive']; ?>)</span>
                </span>
               <span class="review-btn review_down">
                    <span class="label"><?php echo Arr::get($cmslabel, 'commentreview_down', ''); ?> </span>
                    <span id="review_comment_negative_<?php echo $item['id']; ?>" class="review-comment-qty review_comment review-negative<?php if($item['review_negative'] > 0): ?> active<?php endif; ?>">(<?php echo $item['review_negative']; ?>)</span>
                </span>
            </span>
        </div>
    <?php endif; ?>
</article>
<span id="comment_delete_success_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_delete_success'); ?></span>