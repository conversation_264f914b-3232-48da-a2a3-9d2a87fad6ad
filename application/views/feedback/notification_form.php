<?php $mode = (isset($mode)) ? $mode : '';  ?>

<div class="cd-unavailable">
	<div class="cd-unavailable-title red">
		<?php if($mode == 'special'): ?>
			<?php echo Arr::get($cmslabel, 'not_available_special', 'Obavijesti me kada postane dostupno'); ?>
		<?php elseif($mode == 'preorder'): ?>
			<?php echo Arr::get($cmslabel, 'not_available_preorder', 'Obavijesti me kada postane dostupno'); ?>
		<?php else: ?>
			<?php echo Arr::get($cmslabel, 'not_available', 'Obavijesti me kada postane dostupno'); ?>
		<?php endif; ?>
	</div>
	<div class="cd-unavailable-note">
		<?php if($mode == 'special'): ?>
			<?php echo Arr::get($cmslabel, 'not_available_note_special'); ?>
		<?php elseif($mode == 'preorder'): ?>
			<?php echo Arr::get($cmslabel, 'not_available_note_preorder'); ?>
		<?php else: ?>
			<?php echo Arr::get($cmslabel, 'not_available_note'); ?>
		<?php endif; ?>
	</div>

	<div class="cd-notify-form">
		<div class="cd-notify-form-wrapper" id="notifyme-<?php echo $form_content; ?>">
			<form action="#notifyme_form" method="post" name="notifyme_form" id="notifyme_add_form_<?php echo $form_content; ?>">
				<input type="hidden" name="id" value="<?php echo $form_content; ?>" />
				<input type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" <?php if ($info['user_id'] AND $info['user_email']): ?>value="<?php echo $info['user_email']; ?>"<?php endif; ?>>
				<button type="submit" class="btn btn-green btn-notify-form"><?php echo Arr::get($cmslabel, 'notifyme'); ?></button>
				<span id="field-error-email" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_email', 'error_email'); ?></span>
			</form>
			<div class="notifyme_success message" style="display: none">
				<?php echo Arr::get($cmslabel, 'notifyme_catalog_ty'); ?>
			</div>
		</div>
	</div>
</div>