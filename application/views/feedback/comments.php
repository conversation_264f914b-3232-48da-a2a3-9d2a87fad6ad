<?php $mode = (isset($mode)) ? $mode : ''; ?>

<?php if (!empty($can_comments)): ?>
	<div class="comments-form" id="comment-0">
		<div id="comment_form">
			<?php $form = Widget_Feedback::comment_form($_POST, $info, 'comment'); ?>
			<?php if ($form['request_login'] AND ! $user): ?>
				<?php echo Arr::get($cmslabel, 'comment_request_login'); ?>
			<?php else: ?>
				<form class="form-comment form-animated-label" action="#comment_form" method="post" name="comment_form" id="comment_add_form_<?php echo $content; ?>">
					<input type="hidden" name="id" value="<?php echo $content; ?>" />
					<input type="hidden" name="comment_id" value="" />
					<input type="hidden" name="parent_id" value="0" />
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>" />
					<?php foreach ($form['customer_fields'] as $field): ?>
						<?php if ($field[0] == ':'): ?>
						<?php else: ?>
							<p class="field comment-field comment-field-<?php echo $field; ?>">
								<?php $label = (Arr::get($cmslabel, 'form_comments_'.$field)) ? Arr::get($cmslabel, 'form_comments_'.$field) : Arr::get($cmslabel, $field); ?>
								<label for="field-<?php echo $field; ?>"><?php echo $label; ?></label>
								<?php echo $form['item']->input($field, 'form'); ?>
								<span id="field-error-<?php echo $field; ?>" class="error" style="display: none"></span>
							</p>
							<?php if($field == 'email'): ?>
								<div class="field comment-field comment-field-rate">
									<div class="label"><?php echo Arr::get($cmslabel, 'your_rate'); ?></div>
									<div class="comment-stars-cnt">
										<?php for ($rate = 1; $rate <= 5; $rate++): ?>
											<span class="comment-rate-item comment-rate-<?php echo $rate; ?> add_comment_rate" data-comment_rate="<?php echo $rate; ?>">
												<input type="radio" id="comment-rate<?php echo $rate; ?>" name="rate" value="<?php echo $rate ?>">
												<label for="comment-rate<?php echo $rate; ?>"><?php echo $rate ?></label>
											</span>
										<?php endfor; ?>
									</div>
                                    <span id="field-error-rate" class="error" style="display: none"></span>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					<?php endforeach; ?>
					<div class="comment-button-container">
						<button class="btn btn-send-comment" type="submit"><?php echo Arr::get($cmslabel, 'send_comment'); ?></button>
						<div class="comment-form-note"><?php echo Arr::get($cmslabel, 'comment_form_note'); ?></div>
					</div>
				</form>
			<?php endif; ?>
			<div class="comment_success comment-success" <?php if($info['user_device'] != 'm'): ?>data-feedback_ignore_scroll="1"<?php endif; ?> style="display: none">
				<?php echo Arr::get($cmslabel, 'comment_success'); ?>
				<a id="comment_add_new" class="" href="javascript:cmsfeedback.replaycomment(0);"><?php echo Arr::get($cmslabel, 'comment_add_new'); ?></a>
			</div>
		</div>
	</div>
<?php endif; ?>

<div class="comments-list comments_data comments-container" id="comment-list">
	<?php if (!empty($items)): ?>
		<div id="comment-list-items">
			<div class="comment-list-cnt" data-feedback_comment_list="1">
				<?php foreach ($items as $item): ?>
					<?php echo View::factory('feedback/comment_entry', ['item' => $item]); ?>
				<?php endforeach; ?>
			</div>
		</div>
		<input type="hidden" name="comments-list-lastcheck" id="lastcheck" value="<?php echo time(); ?>" />
		<input type="hidden" name="comments-list-page" id="comments-list-page" value="1" />
		<a class="btn btn-white btn-comment-more" id="comment-list-more" href="javascript:cmsfeedback.getonlycomments('<?php echo $content; ?>', 'append', '<?php //echo $sort; ?>')" style="display: none"><span><?php echo Arr::get($cmslabel, 'comment_load_more'); ?></span></a>
	<?php endif; ?>
</div>
