export default defineEventHandler(event => {
	const query = getQuery(event);
	const cjevent = query.cjevent || '';

	if (cjevent) {
		const host = event.node.req.headers.host || '';
		const parts = host.split('.');
		const domain = parts.length > 2 ? `.${parts.slice(-2).join('.')}` : `.${host}`;

		setCookie(event, 'cje', cjevent, {
			maxAge: 60 * 60 * 24 * 395, // 13 months duration in seconds
			path: '/', // Set cookie for the entire site
			domain: domain, // Set to the top-level domain
			secure: true, // Ensure the cookie is only sent over HTTPS
			sameSite: 'None', // Allow cross-site cookie usage
			httpOnly: false, // <PERSON><PERSON> must be accessible by JavaScript
		});
	}
});
