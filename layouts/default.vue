<template>
	<Body :class="[{'fixed-header': fixedHeader}, {'page-overlay': overlay}]" />
	<div class="page-wrapper">
		<ClientOnly>
			<BaseThemeUiPageLoadingIndicator bar-color="#002D73" />
		</ClientOnly>

		<CmsHeader v-if="!isCheckout" />
		<CmsCheckoutHeader v-else />

		<slot/>

		<CmsFooter v-if="!isCheckout" />

		<ClientOnly>
			<LazyCmsFlyout v-if="Object.keys(modal.activeModals()).includes('flyout')" />
			<LazyCmsModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" v-slot="{item}">
				<h1 v-if="item?.title" v-html="item.title"></h1>
				<div v-if="item?.content" v-html="item.content"></div>
			</LazyCmsModal>
			<LazyCmsOnTop :class="{'active': showToTopButton}" />
			<LazyCatalogAgeVerificationModal v-if="isAgeModalActive" />
			<LazyWebshopAddToCartModal hydrate-on-visible />
		</ClientOnly>
	</div>
</template>

<script setup>
	const route = useRoute();
	const modal = useModal();
	const {isAgeModalActive} = useAgeVerification();

	const isCheckout = computed(() => {
		if(!route.meta.action) return false;
		return (route.meta.controller == 'webshop' && ['login', 'customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? true : false;
	});	

	//rwd
	const {onMediaQuery, onScroll, onClickOutside, appendTo} = useDom();
	//const {matches: mobileSmallBreakpoint} = onMediaQuery({query: '(max-width: 760px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 980px)'});
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1100px)'});

	const fixedHeader = ref(false);
	const showToTopButton = ref(false);
	const overlay = ref(false);

	// Close overlay when clicked outside of defined elements
	onClickOutside(['.aw', '.nav-item-categories', '.c-container', '.header-nav-item.special'], event => {
		overlay.value = false;
	})	
	
	onScroll({
		callback: ({direction, y}) => {
			fixedHeader.value = (y > 132) ? true : false;
			showToTopButton.value = (y > 250) ? true : false;
		}
	})

	// Close overlay on route change
	watch(
		() => route.fullPath, 
		() => overlay.value = false
	)

	// Reload page when viewport is resized above mobile breakpoint
	onMediaQuery({
		query: '(max-width: 980px)',
		enter: () => {
			//appendTo('.c-categories', '.page-wrapper');
			appendTo('#cd-specs', '#after-seller');
		},
		leave: () => {
			window.location.reload();
		}
	});	

	provide('rwd', {mobileBreakpoint});
	provide('layout', {overlay, fixedHeader});
</script>

<style lang="less" scoped>
	.page-overlay{
		.page-wrapper:before{.pseudo(auto,auto); background: var(--black); position: fixed; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.4; z-index: 111;}
		@media (max-width: @m){
			overflow-y: hidden; overflow-x: hidden;
			.page-wrapper{
				overflow: hidden;
				&:before{content: none;}
			}
		}
	}
</style>