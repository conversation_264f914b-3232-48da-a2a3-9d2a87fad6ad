export default defineNuxtPlugin(nuxtApp => {
	const routes = useApiRoutes().get();
	addRouteMiddleware((to, from) => {
		const path = to.path;

		// New urls do not contain '-katalog' in the end. Check routes for new urls without sufix and redirect
		if (path.startsWith('/katalog/') && path.endsWith('-katalog/')) {
			const newCatalogPath = path.replace('-katalog/', '/');
			if (routes.find(r => r.path == newCatalogPath)) {
				return navigateTo(newCatalogPath, {replace: true, redirectCode: 301});
			}
			if (!routes.find(r => r.path == path) && to.meta.template == 'Error404') {
				return navigateTo({
					path: '/katalog-neobstaja/',
					query: {
						missing_url: to.params.slug[1],
					},
					replace: true,
					redirectCode: 301,
				});
			}
		}

		// If catalog url is not found, check if it exists without '-katalog' sufix
		if (path.startsWith('/katalog-neobstaja/') && to.query.missing_url) {
			const newCatalogPath = '/katalog/' + to.query.missing_url.replace('-katalog', '') + '/';
			if (routes.find(r => r.path == newCatalogPath)) {
				return navigateTo(newCatalogPath, {replace: true, redirectCode: 301});
			}
		}
	});
});
