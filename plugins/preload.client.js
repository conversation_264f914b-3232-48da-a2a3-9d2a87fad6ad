export default defineNuxtPlugin(async nuxtApp => {
	await prefetchComponents([
		'CatalogDetail',
		'StaticcontentDefault',
		'StaticcontentLayoutsblank',
		'StaticcontentLayoutsLandingButtonBlue',
		'StaticcontentLayoutsLandingButtonWhite',
		'StaticcontentLayoutsLandingForm',
		'StaticcontentLayoutsLandingImgCenterBlack',
		'StaticcontentLayoutsLandingImgCenterWhite',
		'StaticcontentLayoutsLandingImgLeftBlack',
		'StaticcontentLayoutsLandingImgLeftWhite',
		'StaticcontentLayoutsLandingImgRightBlack',
		'StaticcontentLayoutsLandingImgRightWhite',
		'StaticcontentLayoutsLandingItemPromo3BtnBlue',
		'StaticcontentLayoutsLandingItemPromo3BtnWhite',
		'StaticcontentLayoutsLandingItemPromo4BtnBlue',
		'StaticcontentLayoutsLandingItemPromo4BtnWhite',
		'StaticcontentLayoutsLandingItemPromo5BtnBlue',
		'StaticcontentLayoutsLandingItemPromo5BtnWhite',
		'StaticcontentLayoutsLandingItemPromo6BtnBlue',
		'StaticcontentLayoutsLandingItemPromo6BtnWhite',
		'StaticcontentLayoutsLandingMenu',
		'StaticcontentLayoutsLandingProduct',
		'StaticcontentLayoutsLandingProductsSlider',
		'StaticcontentLayoutsLandingProductsSliderPromo',
		'StaticcontentLayoutsLandingPromoBig',
		'StaticcontentLayoutsLandingPromoMedium',
		'StaticcontentLayoutsLandingPromoRowLeft',
		'StaticcontentLayoutsLandingPromoRowRight',
		'StaticcontentLayoutsLandingPromoSmall',
		'StaticcontentLayoutsLandingPublish',
		'StaticcontentLayoutsLandingShortDesc',
		'StaticcontentLayoutsLandingTimer',
		'StaticcontentLayoutsLandingTimerBackgroundColor',
		'StaticcontentLayoutsLandingTitle',
		'StaticcontentLayoutsLandingVideo',
		'StaticcontentLayoutsLandingWidget',
		'StaticcontentLayoutsLandingWidgetBraun',
		'StaticcontentLayoutsLandingWidgetBraun2',
		'StaticcontentLayoutsLandingWidgetContent',
		'StaticcontentLayoutsLandingWidgetMiele',
		'StaticcontentLayoutsLandingWidgetSpinWheel',
	]);
});
