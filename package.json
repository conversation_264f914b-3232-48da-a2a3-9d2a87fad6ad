{"private": true, "scripts": {"dev": "nuxt dev", "build": "node ..\\nuxt-base\\buildversion.js && nuxt build", "build-mac": "node ../nuxt-base/buildversion.js; nuxt build", "builddev": "nuxt build --mode=development", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "^2.6.2", "nuxt": "3.17.6"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@panzoom/panzoom": "^4.6.0", "@sentry/nuxt": "^9.35.0", "@vueform/slider": "^2.1.10", "less": "4.3.0", "less-loader": "12.3.0", "nuxt-vitalizer": "^0.10.0", "photoswipe": "^5.4.4", "swiper": "^11.2.10", "vee-validate": "4.15.1"}}