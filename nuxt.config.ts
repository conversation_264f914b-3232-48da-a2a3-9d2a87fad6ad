import {config} from './hapi.config';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig(() => {
	const redisConfig = {
		mp: {
			host: '***********',
			port: 6380,
			base: 'mp_bigbang',
			ttl: 0,
		},
		sdhr: {
			host: '***********',
			port: 6380,
			base: 'sdhr_bigbang',
			ttl: 0,
		},
		beta2: {
			host: 'redis-bigbangbeta',
			port: 6379,
			base: 'beta2_bigbang',
			ttl: 0,
		},
	};

	const modules = ['nuxt-vitalizer'];
	
	let sentryConfig = {};
	/*

	// production sentry
	if (config.default === 'prod' && process.env.NODE_ENV == 'production') {
		modules.push('@sentry/nuxt/module');
		sentryConfig = {
			sourceMapsUploadOptions: {
				org: 'marker-doo',
				project: 'bigbang',
				authToken: '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',
			},
		};
	}
	*/

	return {	
		extends: ['../nuxt-base'],
		css: ['~/assets/style.css'],
		vite: {
			css: {
				preprocessorOptions: {
					less: {
						additionalData: `@import "~/assets/_vars.less"; @import "~/assets/_mixins.less";`,
					},
				},
			},
			define: {
				__GENERATE_THUMBS__: JSON.stringify(true),
				__SELLERS__: JSON.stringify(true),
				__STRUCTURED_DATA__: JSON.stringify(true),
				__WEBSHOP_VIEW_ORDER__: JSON.stringify(false),
				__LOCATIONS__: JSON.stringify(true),
				__CATEGORY_WITHOUT_BASE_URL__: JSON.stringify(true),
			},
		},
		vue: {
			compilerOptions: {
				isCustomElement: tag => ['bb-3dviewer'].includes(tag),
			},
		},	
		app: {
			head: {
				meta: ['mp', 'beta'].includes(config.default) ? [{name: 'robots', content: 'noindex, nofollow'}] : [],
				script: [
					{
						src: 'https://d2wzl9lnvjz3bh.cloudfront.net/frosmo.easy.js',
						async: true,
						body: true,
					},
					{
						src: 'https://d2wzl9lnvjz3bh.cloudfront.net/sites/bigbang_si.js',
						async: true,
						body: true,
					},
				],
			},
		},	
		dir: {
			'public': 'media',
		},
		modules,
		sentry: sentryConfig,
		compressPublicAssets: {
			gzip: true,
			brotli: true,
		},
		$production: {
			sourcemap: {
				server: false,
				client: false,
			},
			/*
			routeRules: {
				'*': {
					swr: config[config.default].routeCacheExpire,
				},
			},
			*/
		},
		nitro: {
			minify: true,
			storage: {
				db: {
					driver: 'redis',
					username: 'default',
					password: 'LqBKEDGSBM',
					...redisConfig[config.default],
				},
			},
			devStorage: {
				db: {
					driver: 'fs',
					base: '.app_cache_data',
				},
			},
		},
		devtools: {
			enabled: false,
		},
		experimental: {
			asyncContext: true,
			clientFallback: true,
			emitRouteChunkError: 'automatic',
		},
		compatibilityDate: '2024-09-17',
	}
});
