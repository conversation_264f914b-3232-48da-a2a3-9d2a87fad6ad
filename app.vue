<template>
	<DevOnly>
		<BaseUtilsCompatibility />
	</DevOnly>
	<NuxtLayout>
		<NuxtPage />
		<template v-if="underDevelopment">
			<component :is="underDevelopment" v-if="labels.get('under_development')" />
		</template>
	</NuxtLayout>
</template>

<script setup>
	const config = useAppConfig();
	const info = useInfo();
	const {addScript} = useMeta();
	const labels = useLabels();

	let underDevelopment = null;
	if (__UNDER_DEVELOPMENT__) {
		underDevelopment = resolveComponent('ViewsCmsUnderDevelopment');
	}

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const {gdprCookie} = useGdpr();
	const env = process.env.NODE_ENV;

	onMounted(() => {
		const gtmCode = info.getInfo('gtagmanager_code');
		if (gtmCode && config.google?.gtm?.env.includes(env)) {
			addScript({
				innerHTML: `(function (w, d, s, l, i) { w[l] = w[l] || []; w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'}); var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i
			+ dl; f.parentNode.insertBefore(j, f); })(window, document, 'script', 'dataLayer', '${gtmCode}');`,
				appendTo: 'head',
				key: 'gtm',
				gdpr: config.google?.gtm?.gdpr,
				delay: true,
			});
			addScript({
				innerHTML: `<iframe :src="https://www.googletagmanager.com/ns.html?id=${gtmCode}" height="0" width="0" style="display: none; visibility: hidden"></iframe>`,
				key: 'gtm-noscript',
				noscript: true,
				gdpr: config.google?.gtm?.gdpr,
				delay: true,
			});
		}

		const ga4 = info.getInfo('ganalytics4_code');
		if (ga4 && config.google?.ga4?.env.includes(env)) {
			addScript({
				src: `https://www.googletagmanager.com/gtag/js?id=${ga4}`,
				appendTo: 'head',
				async: true,
				key: 'ga4',
				gdpr: config.google?.ga4?.gdpr,
				delay: true,
			});
			addScript({
				innerHTML: `window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', '${ga4}');`,
				appendTo: 'head',
				key: 'ga4-script',
				gdpr: config.google?.ga4?.gdpr,
				delay: true,
			});
		}

		if (config.facebook?.pixel?.apiKey && config.facebook?.pixel?.env.includes(env)) {
			addScript({
				innerHTML: `!function(f,b,e,v,n,t,s) {if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)}; if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0'; n.queue=[];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)[0];
						s.parentNode.insertBefore(t,s)}(window, document,'script', 'https://connect.facebook.net/en_US/fbevents.js');
						fbq('init', '${config.facebook.pixel.apiKey}');
						fbq('track', 'PageView');`,
				key: 'fbpixel',
				gdpr: config.facebook?.pixel?.gdpr,
				delay: true,
			});
		}

		if (__RECAPTCHA__) {
			const recaptchaKey = info.getInfo('recaptcha_site_key');
			if (recaptchaKey) {
				addScript({
					src: 'https://www.google.com/recaptcha/api.js?render=' + recaptchaKey,
					key: 'recaptcha',
					delay: true,
				});
			}
		}

		// Watch gdpr cookie. If user changed the consent, submit to GTM
		if (gtm) {
			watch(
				() => gdprCookie(),
				data => gtm.gtmTrackGdpr(data),
				{immediate: true}
			);
		}
	});
</script>
