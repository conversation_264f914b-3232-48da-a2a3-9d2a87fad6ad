/**
 * Middleware to redirect URLs
 */
export default defineNuxtRouteMiddleware(to => {
	// Check if path contains any tel pattern: :tel:, :tel, tel:
	const telPattern = /(:tel:|:tel|tel:)/i;
	const match = to.path.match(telPattern);
	if (match) {
		const cleanPath = to.path.substring(0, match.index);
		if (to.path !== cleanPath) {
			return navigateTo(cleanPath, {
				replace: true,
				redirectCode: 301,
			});
		}
	}

	// Check if URL has mode=gallery parameter
	if (to.query.mode === 'gallery') {
		// Create a new path without the query parameters
		const path = to.path;

		// Return redirect to the clean URL
		return navigateTo(path, {
			replace: true,
			redirectCode: 301, // Permanent redirect for SEO
		});
	}
});
