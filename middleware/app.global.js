export default defineNuxtRouteMiddleware(async to => {
	const nuxtApp = useNuxtApp();

	// add trailing slash to url if missing
	if (!to.path.endsWith('/') && to.path !== '/') {
		const {path, query, hash} = to;
		const nextPath = `${path}/`;
		const nextRoute = {path: nextPath, query, hash};
		return navigateTo(nextRoute, {redirectCode: 301});
	}

	// ignored url bot protection
	if (nuxtApp.$isbot) {
		to.meta.layout = 'bot';
		return;
	}

	// reload app if routes are not loaded
	const routes = nuxtApp.$appData.routes;
	if (!routes) {
		throw createError({
			statusCode: 401,
			statusMessage: `Error 401`,
		});
	}

	const config = useAppConfig();
	const page = usePage();
	const publish = usePublish();
	const catalog = useCatalog();
	const {matchRoute, getRoute} = useApiRoutes();
	let template = 'CmsDefault';
	let controller = 'cms';
	let action = 'index';
	let contentType = 'page';
	let languageUrls = [];
	const bodyClass = ref();
	const {getUrlSegments, relative: relativeUrl} = useUrl();
	const routeSegments = getUrlSegments(to.path, {ignoreLang: true});
	const auth = useAuth();
	const isStaff = auth.getUser()?.staff;
	let globalData = {};

	// clear .app_cache_data cache
	if (to.query?.devclearcache) {
		const cacheModules = to.query.devclearcache.split(',');
		if (cacheModules?.length) {
			await $fetch(`/api/nuxtapi/clearCache/`, {
				method: 'POST',
				body: cacheModules,
			});
		}
	}

	// import structured data composable if enabled
	const structuredDataSchema = ref(null);
	let structuredData = null;
	if (__STRUCTURED_DATA__) structuredData = useStructuredData();

	const matchingRoute = matchRoute(routes, to.path); // match current url with api routes to get correct module
	const currentRoute = getRoute(matchingRoute); // get current route object

	// set controller, action and content type for active module
	if (currentRoute?.controller) controller = currentRoute.controller;
	if (currentRoute?.action) action = currentRoute.action;
	if (currentRoute?.content) contentType = currentRoute.content;
	if (currentRoute?.content_type) contentType = currentRoute.content_type;

	// convert template names to match file names
	function setTemplate(payload) {
		if (payload) {
			const t = payload.split(/(?:_|\/|-)/);

			// if last item in array is "index", remove it
			if (t[t.length - 1] == 'index') t.pop();

			const tpl = t.map(el => {
				return el.charAt(0).toUpperCase() + el.slice(1);
			});
			const newTpl = tpl.join('');

			template = newTpl.toLowerCase();
			return newTpl;
		}

		return 'CmsDefault';
	}

	function setBodyClass(payload) {
		if (payload) {
			const bodyClass = payload.split(/(?:_|\/|-)/);

			if (bodyClass.length > 1) {
				const bClass = bodyClass.map(el => el.toLowerCase());
				return bClass.join('-');
			}

			return payload.toLowerCase();
		}

		return '';
	}

	// add language urls to meta data for hreflang
	function setLanguageUrls(urls) {
		if (!config.multilanguage) return [];
		if (!urls) return [];
		const data = Object.entries(urls).map(([key, value]) => {
			return {
				lang: key,
				url: value ? relativeUrl(value) : value,
			};
		});
		return data.filter(d => d.url);
	}

	function checkAccess() {
		if (process.server) {
			template = 'ErrorLoading';
		}
		if (process.client && !isStaff) {
			template = 'Error404';
		}
	}

	// cms pages
	if (controller == 'cms') {
		let p = await page.fetch({slug: to.path});

		// check if this is faq subcategory
		if (!getRoute(to.path) && !p) {
			// if there is no match in routes, try only first slug
			const firstSlug = getUrlSegments(to.path, {ignoreLang: true, limit: 1, addSlashes: true});

			// if there is a first slug match, fetch page and check if it has faq template
			if (getRoute(firstSlug)) {
				const faqPage = await page.fetch({slug: firstSlug});
				const faqTemplate = faqPage?.template ? faqPage.template.split('/').shift() : null;
				if (faqTemplate == 'faq') p = faqPage;
			}
		}

		bodyClass.value = `page-${controller}`;

		// match regular cms page
		if (p) {
			template = setTemplate(p.template);
			if (p.template) {
				bodyClass.value += ` page-` + setBodyClass(p.template);
			}

			languageUrls = setLanguageUrls(p?.languages_urls);
			to.meta.pageId = p.id;
			if (structuredData) structuredDataSchema.value = structuredData.generateStructuredData({data: p, controller, template});

			// Allow only staff users to see unpublished pages
			if (!p.visible) checkAccess();
		} else {
			template = 'Error404';
		}
	}

	// match auth
	function matchAuth() {
		template = routeSegments[1] ? setTemplate('Auth-' + routeSegments[1]) : setTemplate('AuthDefault');
		bodyClass.value = routeSegments[1] ? 'page-auth page-auth-' + routeSegments[1] : `page-auth page-auth-${action}`;

		// if user is trying to confirm signup, set specific template and content type
		if (routeSegments[1] == 'confirm_signup') {
			template = 'AuthSignup';
			contentType = 'confirmSignup';
			return;
		}

		// if user is trying to confirm special signup, set specific template and content type
		if (routeSegments[1] == 'signup_confirm') {
			template = 'AuthSignup';
			contentType = 'confirmSignup';
			return;
		}

		// if forgotten password, set NewPassword template
		if (routeSegments[1] == 'change_password' && routeSegments.length == 4) {
			template = 'AuthNewPassword';
			contentType = 'newPassword';
			return;
		}

		if (!config.keycloak && !getRoute(to.path)) {
			template = 'Error404';
		}
	}

	if (contentType == 'auth') {
		matchAuth();
	}

	// match landing page
	if (controller == 'staticcontent') {
		bodyClass.value = `page-${controller}`;
		template = setTemplate(controller + '-Default');

		// if there is no match in routes for current landing page, show 404 template
		if (!getRoute(to.path)) {
			template = 'Error404';
		}
	}

	// match search results
	if (to.query.search_content || contentType == 'search') {
		template = 'Search';
	}

	// newsletter and sweepstake
	if (['newsletter', 'sweepstake'].includes(controller)) {
		bodyClass.value = `page-${controller}`;
		template = setTemplate(controller + '-' + action);
	}

	// webshop
	if (controller == 'webshop') {
		bodyClass.value = `page-${controller} page-${controller}-${action}`;
		template = getRoute(currentRoute.path) ? 'Webshop' + setTemplate(action) : 'Error404';
		if (action == 'view_order') {
			to.meta.layout = 'blank';
		}
	}

	// publish
	if (controller == 'publish') {
		bodyClass.value = `page-${controller}-${action}`;

		// if category page, fetch current category
		if (action == 'index' && contentType != 'publish_authors') {
			const currentCategory = await publish.fetchCategoryBySlug({slug: to.path}).then(res => (res?.data?.length ? res.data[0] : null));
			template = 'Publish';

			// generate structured data
			if (currentCategory && structuredData) {
				structuredDataSchema.value = structuredData.generateStructuredData({data: currentCategory, contentType, controller, action});
			}

			// if category has custom template, use it and set body class
			if (currentCategory?.template) {
				template = setTemplate('Publish-' + currentCategory.template);

				// If subfolder template does not exist, use default template
				if (!nuxtApp.$appData?.templates?.includes(template)) template = 'Publish';

				bodyClass.value += ` page-${controller}-${action}-${currentCategory.template}`;
			}

			// set category id
			if (currentCategory?.id) globalData.entityId = currentCategory.id;

			// set language urls
			languageUrls = setLanguageUrls(currentCategory?.languages_urls);

			// if there is no match in routes for current category, show 404 template
			if ((!getRoute(to.path) || !currentCategory) && contentType != 'tag') {
				template = 'Error404';
			}
		}

		// if detail page, fetch post and set post template
		if (action == 'detail' && contentType != 'publish_author') {
			const post = await publish.fetchPost({item_slug: to.path});
			template = 'PublishDetail';

			// if redirect url is set, redirect
			if (post?.redirect_url_without_domain) {
				return navigateTo(post.redirect_url_without_domain, {redirectCode: post.redirect_code ? post.redirect_code : 301});
			}

			// if post has category template, use it and set body class
			if (post?.category_template) {
				template = setTemplate('Publish-' + post.category_template + '-Detail');

				// If category template does not exist, use root template
				if (!nuxtApp.$appData?.templates?.includes(template)) template = 'PublishDetail';

				bodyClass.value += ` page-${controller}-${action}-${post.category_template}`;
			}

			// set global data if post data exists
			if (post?.id) {
				globalData.entityId = post.id;
				globalData.feedbackType = controller;
				if (structuredData) structuredDataSchema.value = structuredData.generateStructuredData({data: post, contentType, controller, action});
			}

			// set language urls
			languageUrls = setLanguageUrls(post?.languages_urls);

			// Allow only staff users to see unpublished posts
			const isPostVisible = Number(post?.status) >= 3 && Number(post?.category_visible);
			if (!isPostVisible) checkAccess();

			// if there is no post data or unauthorized, show 404 template
			if (!post?.id || ['error_unauthorized', 'error_empty_data'].includes(post?.label_name)) {
				template = 'Error404';
			}
		}

		// if tag page
		if (_TAGS__ && action == 'index_tag') {
			template = 'Tag';
			bodyClass.value += ` page-${controller}-${action}`;
		}

		// Publish authors
		if (__PUBLISH_AUTHORS__ && ['publish_authors', 'publish_author'].includes(contentType)) {
			template = contentType == 'publish_authors' ? 'PublishAuthors' : 'PublishAuthorDetail';
			bodyClass.value = `page-publish-${action}`;
		}
	}

	// catalog
	if (controller == 'catalog') {
		bodyClass.value = `page-${controller}-${action}`;
		// if route query contains search_q, set content type to search
		if (to.query?.search_q) contentType = 'search';

		if (contentType) bodyClass.value += ` page-${controller}-${contentType}`;
		const isMainLanding = action == 'index' && routeSegments.length <= 1 && __CATALOG_MAIN_LANDING__ && !to.query?.search_q ? true : false;

		// set category template
		if (action == 'index' && !isMainLanding) {
			// fetch current category and set category template if exists. If not, use default template
			let catalogCategory;
			if (contentType == 'category') {
				catalogCategory = await catalog.fetchCategoryBySlug({slug: to.path}).then(res => {
					if (res?.data?.redirect_url_without_domain) return res.data;
					return res?.data?.length ? res.data[0] : null;
				});
			}

			if (catalogCategory?.redirect_url_without_domain) {
				return navigateTo(catalogCategory.redirect_url_without_domain, {redirectCode: catalogCategory.redirect_code || 301});
			}

			const categoryTemplate = catalogCategory?.template ? catalogCategory.template : '';
			if (catalogCategory?.id) {
				globalData.entityId = catalogCategory.id;
				if (structuredData) structuredDataSchema.value = structuredData.generateStructuredData({data: catalogCategory, contentType, controller, action});
			}

			// Set category template. If category template does not exist, use root template
			template = setTemplate('Catalog-' + categoryTemplate + '-' + action);
			if (!nuxtApp.$appData?.templates?.includes(template)) setTemplate('Catalog-' + action);

			// Use landing template if category is landing page and feature is enabled
			if (__CATALOG_CATEGORY_LANDING__ && catalogCategory?.landing_page) {
				template = 'CatalogCategoryLanding';
			}

			// set language urls
			languageUrls = setLanguageUrls(catalogCategory?.languages_urls);

			// if there is no category data, and this is not main landing page, show 404 template
			if (routeSegments.length > 1 && !catalogCategory?.id && contentType == 'category') template = 'Error404';
		}

		// load root landing template only if this component exists and there is no search query
		if (isMainLanding) {
			template = 'CatalogMain';
			bodyClass.value += ' page-catalog-landing';
		}

		// set manufacturer template
		if (action == 'index_manufacturer') {
			template = 'CatalogIndexManufacturer';
		}

		// set compare template
		if (to.query?.special_view == 'compare') {
			template = 'CatalogCompare';
			bodyClass.value = 'page-catalog-index page-compare';
		}

		// set quick order template
		if (config.catalog?.quickOrder && to.query?.special_view == 'order_form') {
			template = 'CatalogQuickOrder';
			bodyClass.value = 'page-catalog-index page-quickorder';
		}

		// set wishlist template
		if (action == 'wishlist') {
			template = 'CatalogWishlist';
			bodyClass.value = 'page-catalog-index page-wishlist';
		}

		// if tag page
		if (_TAGS__ && action == 'index_tag') {
			template = 'Tag';
			bodyClass.value += ` page-${controller}-${action}`;
		}

		// set seller template
		if (__SELLERS__ && action == 'index_seller') {
			contentType = 'seller';

			if (routeSegments.length == 1) {
				template = 'CatalogIndexSeller';
			}
			if (routeSegments.length == 2) {
				template = 'CatalogDetailSeller';
			}
			if (routeSegments.length > 2) {
				template = 'Catalog';
			}
		}

		// set product detail template
		if (action == 'detail') {
			const productFetchOptions = config?.catalog?.fetch ? config.catalog.fetch : {};
			productFetchOptions.item_slug = to.path;
			productFetchOptions.search_q = to.query?.search_q ? to.query.search_q : null;

			const product = await catalog.fetchProduct(productFetchOptions);
			template = 'CatalogDetail';

			// if redirect url is set, redirect
			if (product?.redirect_url_without_domain) {
				return navigateTo(product.redirect_url_without_domain, {redirectCode: product.redirect_code ? product.redirect_code : 301});
			}

			// if product has category template, use it. If not, use default template
			if (product?.category_template) {
				template = setTemplate('Catalog-' + product.category_template + '-Detail');
				if (!nuxtApp.$appData?.templates?.includes(template)) template = 'CatalogDetail';
				if (product.category_template) {
					bodyClass.value += ` page-${controller}-${action}-${product.category_template}`;
				}
			}

			// Set product data. If there is no product data or unauthorized, show 404 template.
			if (product?.id) {
				if (structuredData) structuredDataSchema.value = structuredData.generateStructuredData({data: product, contentType, controller, action});
				languageUrls = setLanguageUrls(product?.languages_urls);
				globalData.entityId = product.id;
				globalData.feedbackType = 'catalogproduct';

				// Allow only staff users to see unpublished product
				const categoryVisible = product?.category_visible == null || +product.category_visible ? true : false;
				let brandVisible = product?.manufacturer_visible == null || +product.manufacturer_visible ? true : false;
				if (product?.ignore_brand_visibility) brandVisible = true;
				const isProductVisible = +product?.visible && categoryVisible && brandVisible;
				if (!isProductVisible) checkAccess();
			} else {
				template = 'Error404';
			}
		}
	}

	// Events
	if (__EVENTS__ && controller == 'event') {
		bodyClass.value = `page-${controller}-${action}`;
		template = setTemplate(controller + '-' + action);

		if (routeSegments.length > 1) {
			bodyClass.value = `page-${controller}-detail`;
			template = 'EventDetail';
		}
	}

	if (__LOCATIONS__ && controller == 'location') {
		bodyClass.value = `page-${controller}-${action}`;
		template = setTemplate(controller + '-' + action);
		if (routeSegments.length > 1) {
			template = 'LocationDetail';
		}
	}

	// set global data and push to nuxtApp object
	globalData.controller = controller;
	globalData.action = action;
	globalData.contentType = contentType;
	globalData.template = template;
	globalData.bodyClass = bodyClass.value;
	globalData.structuredData = structuredDataSchema.value;
	nuxtApp.$appGlobalData = globalData;
	to.meta.template = template;
	to.meta.controller = controller;
	to.meta.action = action;
	to.meta.contentType = contentType;
	to.meta.languageUrls = languageUrls;
	to.meta.entityId = globalData.entityId;

	// preload component on first load to prevent page flickering
	if (process.client && nuxtApp.isHydrating && template) {
		await prefetchComponents(template);
	}

	// set page layout. If there is no layouts set in config for current template, use default layout
	if (config.layouts?.length) {
		const pageLayout = config.layouts.find(item => item.template.includes(template));
		await new Promise(resolve => setTimeout(resolve, 100));
		to.meta.layout = pageLayout ? pageLayout.layout : 'default';
	}

	// set not found pages url status to 404
	if (process.server && template === 'Error404') {
		const event = useRequestEvent();
		setResponseStatus(event, 404);
	}
});
