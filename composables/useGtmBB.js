export function useGtmBB() {
	// referral
	function sendReferral(payload) {
		const data = {
			'event': 'referrer_data',
			'referrer': payload,
		};

		_pushToDataLayer('Referrer data', data);
	}

	//promotions
	function selectPromotion(payload) {
		const data = {
			'event': 'select_promotion',
			'ecommerce': {
				'creative_name': payload.creative_name,
				'creative_slot': payload.creative_slot,
				'promotion_id': payload.promotion_id,
				'promotion_name': payload.promotion_name,
				'items': [],
			},
		};

		_pushToDataLayer('select_promotion', data);
	}

	function viewPromotion(payload) {
		const data = {
			'event': 'view_promotion',
			'ecommerce': {
				'creative_name': payload.creative_name,
				'creative_slot': payload.creative_slot,
				'promotion_id': payload.promotion_id,
				'promotion_name': payload.promotion_name,
				'items': [],
			},
		};

		_pushToDataLayer('view_promotion', data);
	}

	//items item_list_id
	function viewItemList(items, listData) {
		const data = {
			'event': 'view_item_list',
			'ecommerce': {
				'items': _getProductsList(items, listData),
			},
		};

		_pushToDataLayer('view_item_list', data);
	}

	function selectItem(items, listData, index) {
		const data = {
			'event': 'select_item',
			'ecommerce': {
				'items': _getProductsList(items, listData, index),
			},
		};

		_pushToDataLayer('select_item', data);
	}

	//catalog detail
	function viewItem(payload) {
		let categoryVariables = null;

		if (payload.item_categories) {
			const categoryHierarchy = payload.item_categories.split(' > ');
			categoryVariables = categoryHierarchy?.map((category, index) => {
				if (index === 0) {
					return `item_category: ${category.trim()}`;
				} else {
					return `item_category${index + 1}: ${category.trim()}`;
				}
			});
		}

		const data = {
			'event': 'view_item',
			'value': payload.price,
			'ecommerce': {
				'items': [
					{
						'item_id': payload.item_id,
						'item_name': payload.item_name,
						'affiliation': 'www.bigbang.si',
						'currency': 'EUR',
						'index': 0,
						'discount': payload.discount_amount,
						'item_discount_percentage': payload.item_discount_percentage,
						'price': payload.price,
						'quantity': 1,
						'item_brand': payload.item_brand,
						'item_reviews_no': payload.item_reviews_no,
						'item_reviews_rating': payload.item_reviews_rating,
						'item_list_id': '',
						'item_list_name': '',

						...(categoryVariables
							? categoryVariables.reduce((obj, category) => {
									const [key, value] = category.split(': ');
									obj[key] = value;
									return obj;
							  }, {})
							: {}),
					},
				],
			},
		};

		_pushToDataLayer('Page view', data);
	}

	//chekout
	function pageView(payload) {
		const data = {
			'event': 'virtualPageview',
			'pageUrl': payload.url,
			'pageTitle': payload.title,
		};

		_pushToDataLayer('Page view', data);
	}

	function addProduct(payload) {
		let categoryVariables = null;

		if (payload.product.item?.category?.hierarchy) {
			const categoryHierarchy = payload.product.item?.category?.hierarchy.split(' > ');
			categoryVariables = categoryHierarchy?.map((category, index) => {
				if (index === 0) {
					return `item_category: ${category.trim()}`;
				} else {
					return `item_category${index + 1}: ${category.trim()}`;
				}
			});
		}

		let sessionItem = null;
		for (let key in sessionStorage) {
			if (sessionStorage.hasOwnProperty(key)) {
				if (key === payload.product.item.code) {
					sessionItem = JSON.parse(sessionStorage.getItem(key));
					break;
				}
			}
		}

		const data = {
			'event': 'add_to_cart',
			'ecommerce': {
				'items': [
					{
						'affiliation': 'www.bigbang.si',
						'currency': 'EUR',
						'item_name': payload.product.item.title,
						'item_id': payload.product.item.code,
						'price': payload.product.unit_price,
						'quantity': payload.quantity,
						'discount': payload.product.gross_amount - payload.product.unit_price,
						'item_discount_percentage': payload.product.discount_percentage,
						'item_brand': payload.product.item.manufacturer.title,
						'index': sessionItem ? sessionItem.index : '',
						'item_list_id': sessionItem ? sessionItem.list_code : '',
						'item_list_name': sessionItem ? sessionItem.list_title : '',
						...(categoryVariables
							? categoryVariables.reduce((obj, category) => {
									const [key, value] = category.split(': ');
									obj[key] = value;
									return obj;
							  }, {})
							: {}),
					},
				],
			},
		};

		_pushToDataLayer('add_to_cart', data);
	}

	function removeProduct(payload) {
		let categoryVariables = null;

		if (payload.product.item?.category?.hierarchy) {
			const categoryHierarchy = payload.product.item.category.hierarchy.split(' > ');
			categoryVariables = categoryHierarchy?.map((category, index) => {
				if (index === 0) {
					return `item_category: ${category.trim()}`;
				} else {
					return `item_category${index + 1}: ${category.trim()}`;
				}
			});
		}

		let sessionItem = null;
		for (let key in sessionStorage) {
			if (sessionStorage.hasOwnProperty(key)) {
				if (key === payload.product.item.code) {
					sessionItem = JSON.parse(sessionStorage.getItem(key));
					break;
				}
			}
		}

		const data = {
			'event': 'remove_from_cart',
			'ecommerce': {
				'items': [
					{
						'affiliation': 'www.bigbang.si',
						'currency': 'EUR',
						'item_name': payload.product.item.title,
						'item_id': payload.product.item.code,
						'price': payload.product.unit_price,
						'quantity': payload.quantity,
						'discount': payload.product.gross_amount - payload.product.unit_price,
						'item_discount_percentage': payload.product.discount_percentage,
						'item_brand': payload.product.item.manufacturer.title,
						'index': sessionItem ? sessionItem.index : '',
						'item_list_id': sessionItem ? sessionItem.list_code : '',
						'item_list_name': sessionItem ? sessionItem.list_title : '',
						...(categoryVariables
							? categoryVariables.reduce((obj, category) => {
									const [key, value] = category.split(': ');
									obj[key] = value;
									return obj;
							  }, {})
							: {}),
					},
				],
			},
		};

		_pushToDataLayer('remove_from_cart', data);
	}

	function updateProduct(payload) {
		let quantity = payload.quantity - payload.prevQuantity;

		// if quantity is decreased
		if (quantity < 0) {
			payload.quantity = quantity * -1;
			removeProduct(payload);
		} else {
			payload.quantity = quantity;
			addProduct(payload);
		}
	}

	function viewCart(payload) {
		const data = {
			'event': 'view_cart',
			'ecommerce': {
				'items': _getProducts('ga4'),
			},
		};

		_pushToDataLayer('view_cart', data);
	}

	function beginCheckout(payload) {
		const data = {
			'event': 'begin_checkout',
			'ecommerce': {
				'items': _getProducts('ga4'),
			},
		};

		_pushToDataLayer('begin_checkout', data);
	}

	function addShippingInfo(payload) {
		const data = {
			'event': 'add_shipping_info',
			'ecommerce': {
				'shipping_tier': payload.shippingOption,
				'items': _getProducts('ga4'),
			},
		};

		_pushToDataLayer('add_shipping_info', data);
	}

	function addPaymentInfo(payload) {
		const data = {
			'event': 'add_payment_info',
			'ecommerce': {
				'payment_type': payload.paymentOption,
				'items': _getProducts('ga4'),
			},
		};

		_pushToDataLayer('add_payment_info', data);
	}

	function purchase(payload) {
		const data = {
			'event': 'purchase',
			'ecommerce': {
				'currency': 'EUR',
				'value': payload.value.toFixed(2),
				'tax': payload.tax.toFixed(2),
				'shipping': payload.shipping,
				'affiliation': 'www.bigbang.si',
				'transaction_id': payload.transaction_id,
				'coupon': payload.coupon,
				'items': _getProductsOrder('ga4'),
			},
		};

		_pushToDataLayer('purchase', data);
	}

	//get products
	function _getProductsList(items, listData, index) {
		let productsList = [];
		if (items) {
			let indexValue = index ? index : 0;
			items.forEach(product => {
				let categoryVariables = null;

				let discountValue = product.price_custom < product.basic_price_custom ? product.basic_price_custom - product.price_custom : 0;

				if (product.category_hierarchy_full) {
					const categoryHierarchy = product.category_hierarchy_full.split(' > ');
					categoryVariables = categoryHierarchy?.map((category, index) => {
						if (index === 0) {
							return `item_category: ${category.trim()}`;
						} else {
							return `item_category${index + 1}: ${category.trim()}`;
						}
					});
				}

				productsList.push({
					'affiliation': 'www.bigbang.si',
					'currency': 'EUR',
					'item_name': product.title,
					'item_id': product.id,
					'price': product.price_custom,
					'quantity': 1,
					'discount': discountValue.toFixed(2),
					'item_discount_percentage': product.discount_percentage ? product.discount_percentage : 0,
					'item_brand': product.manufacturer_title,
					'index': indexValue,
					'item_list_id': listData?.list_code,
					'item_list_name': listData?.list_title,
					'item_reviews_no': product?.feedback_rate_widget?.rates_votes,
					'item_reviews_rating': product?.feedback_rate_widget?.rates_sum,
					...(categoryVariables
						? categoryVariables.reduce((obj, category) => {
								const [key, value] = category.split(': ');
								obj[key] = value;
								return obj;
						  }, {})
						: {}),
				});

				indexValue++;
			});
		}
		return productsList;
	}
	function _getProducts(value) {
		const {cart} = useWebshop();
		let products = [];
		if (cart?.value?.parcels) {
			cart.value.parcels?.forEach(parcel => {
				if (parcel.items) {
					parcel.items.forEach(product => {
						let categoryVariables = null;

						if (product.item && product.item.category && product.item.category.hierarchy) {
							const categoryHierarchy = product.item.category.hierarchy.split(' > ');
							categoryVariables = categoryHierarchy?.map((category, index) => {
								if (index === 0) {
									return `item_category: ${category.trim()}`;
								} else {
									return `item_category${index + 1}: ${category.trim()}`;
								}
							});
						}

						let sessionItem = null;
						for (let key in sessionStorage) {
							if (sessionStorage.hasOwnProperty(key)) {
								if (key === product.item.code) {
									sessionItem = JSON.parse(sessionStorage.getItem(key));
									break;
								}
							}
						}

						products.push({
							'affiliation': 'www.bigbang.si',
							'currency': 'EUR',
							'item_name': product.item.title,
							'item_id': parseFloat(product.item.code),
							'price': parseFloat(product.unit_price.toFixed(2)),
							'quantity': product.quantity,
							'discount': product.gross_amount - product.unit_price,
							'item_discount_percentage': product.discount_percentage,
							'item_brand': product.item.manufacturer.title,
							'index': sessionItem ? sessionItem.index : '',
							'item_list_id': sessionItem ? sessionItem.list_code : '',
							'item_list_name': sessionItem ? sessionItem.list_title : '',
							...(categoryVariables
								? categoryVariables.reduce((obj, category) => {
										const [key, value] = category.split(': ');
										obj[key] = value;
										return obj;
								  }, {})
								: {}),
						});
					});
				}
			});
		}
		return products;
	}
	function _getProductsOrder(value) {
		const {orderData} = useCheckout();
		let productsOrder = [];
		if (orderData.value && orderData.value.parcels) {
			orderData.value.parcels?.forEach(parcel => {
				if (parcel.items) {
					parcel.items.forEach(product => {
						let categoryVariables = null;

						if (product.item && product.item.category && product.item.category.hierarchy) {
							const categoryHierarchy = product.item.category.hierarchy.split(' > ');
							categoryVariables = categoryHierarchy?.map((category, index) => {
								if (index === 0) {
									return `item_category: ${category.trim()}`;
								} else {
									return `item_category${index + 1}: ${category.trim()}`;
								}
							});
						}

						let sessionItem = null;
						for (let key in sessionStorage) {
							if (sessionStorage.hasOwnProperty(key)) {
								if (key === product.item.code) {
									sessionItem = JSON.parse(sessionStorage.getItem(key));
									break;
								}
							}
						}

						productsOrder.push({
							'affiliation': 'www.bigbang.si',
							'currency': 'EUR',
							'item_name': product.item.title,
							'item_id': product.item.code,
							'price': product.unit_price,
							'quantity': product.quantity,
							'discount': product.gross_amount - product.unit_price,
							'item_discount_percentage': product.discount_percentage * 100,
							'item_brand': product.item.manufacturer.title,
							'coupon': orderData.value.cart.cart_coupon_notes,
							'index': sessionItem ? sessionItem.index : '',
							'item_list_id': sessionItem ? sessionItem.list_code : '',
							'item_list_name': sessionItem ? sessionItem.list_title : '',
							...(categoryVariables
								? categoryVariables.reduce((obj, category) => {
										const [key, value] = category.split(': ');
										obj[key] = value;
										return obj;
								  }, {})
								: {}),
						});
					});
				}
			});
		}
		return productsOrder;
	}

	function waitForWindowAndExecute(action, interval = 1000, timeout = 20000) {
		let elapsedTime = 0;

		function checkWindow() {
			if (typeof window !== 'undefined' && typeof window?.dataLayer !== 'undefined') {
				action();
			} else if (elapsedTime < timeout) {
				elapsedTime += interval;
				setTimeout(checkWindow, interval);
			}
		}

		checkWindow();
	}

	function _pushToDataLayer(event, data) {
		waitForWindowAndExecute(
			() => {
				if (window?.dataLayer) {
					dataLayer.push(data);
					//console.debug(dataLayer);
				}
			},
			1000,
			20000
		); // Interval of 1000ms and timeout of 20000ms (20 seconds)
	}

	function gtmPromoClick(title, slot, index, code, templateValue) {
		let template = '';
		if (templateValue == 'template_hp_promo_big') {
			template = 'big - ';
		} else if (templateValue == 'template_hp_promo_medium') {
			template = 'medium - ';
		}
		selectPromotion({
			creative_name: title || '',
			creative_slot: slot + template + (index + 1) || '',
			promotion_id: code || '',
			promotion_name: title || '',
		});
	}

	return {
		sendReferral,
		selectPromotion,
		viewPromotion,
		viewItemList,
		selectItem,
		viewItem,
		addProduct,
		removeProduct,
		updateProduct,
		pageView,
		purchase,
		viewCart,
		beginCheckout,
		addShippingInfo,
		addPaymentInfo,
		gtmPromoClick,
	};
}