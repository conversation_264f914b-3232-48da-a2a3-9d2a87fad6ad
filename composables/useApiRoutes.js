export function useApiRoutes() {
	const nuxtApp = useNuxtApp();
	const lang = useLang();
	const {getUrlSegments} = useUrl();
	let routes = nuxtApp.$appData?.routes;
	const appConfig = useAppConfig();

	// loop through routes and set global appUrls
	function processRoutes(data) {
		const urls = {
			home: {url: '/'},
			catalog: {url: ''},
			compare: {url: ''},
			wishlist: {url: ''},
			manufacturer: {url: ''},
			search: {url: ''},
			location: {url: ''},
		};
		data.forEach(routeItem => {
			const routeSegments = getUrlSegments(routeItem.path, {ignoreLang: true});

			// set home url
			if (routeItem.controller == 'cms' && (routeItem.template == 'cms/homepage' || routeItem.path == `/${lang.get()}/`)) {
				urls['home'] = {url: routeItem.path == '//' ? '/' : routeItem.path};
			}

			// set catalog url
			if (routeItem.controller == 'catalog' && routeItem.action == 'index' && !routeItem.urls) {
				if (routeSegments.length == 1) {
					urls['catalog'] = {url: routeItem.path};
					urls['catalog_quickorder'] = {url: routeItem.path + '?special_view=order_form'};
				}
			}

			if (routeItem.controller == 'catalog' && routeItem.action == 'index_manufacturer') {
				urls['manufacturer'] = {url: routeItem.path};
			}

			// set compare url
			if (routeItem.controller == 'cms' && routeItem.template == 'catalog/compare') {
				urls['compare'] = {url: routeItem.path};
			}

			// set wishlist url
			if (routeItem.controller == 'catalog' && routeItem.action == 'wishlist') {
				urls['wishlist'] = {url: routeItem.path};
			}

			// set sweepstake url
			if (routeItem.controller == 'sweepstake') {
				urls['sweepstake'] = {url: routeItem.path};
			}

			// set location url
			if (routeItem.controller == 'location') {
				urls['location'] = {url: routeItem.path};
			}

			// set search url
			if (routeItem.controller == 'cms' && routeItem.content_type == 'search') {
				const r = routeItem.path.split('/').filter(el => el !== '');
				if (routeSegments.length == 1) urls['search'] = {url: routeItem.path};
			}

			// set webshop urls
			if (routeItem.controller == 'webshop') {
				urls['webshop_' + routeItem.action] = {url: routeItem.path};
			}

			// set auth urls
			if (routeItem.controller == 'cms' && routeItem.content_type == 'auth') {
				if (routeSegments.length == 1) {
					urls['auth'] = {url: routeItem.path};
					urls['auth_logout'] = {url: routeItem.path + 'logout/'};
				}

				// if routeSegments length is more than 1, remove first element and add to appUrls
				if (routeSegments.length > 1) {
					routeSegments.shift();
					const url = routeSegments.join('_');
					urls['auth_' + url] = {url: routeItem.path};
				}
			}
		});

		nuxtApp.$appData.appUrls = urls;
		return urls;
	}

	function isNumber(str) {
		return /^\d+$/.test(str);
	}

	// convert routes object received from API to simple array
	function remapRoutes(routes) {
		return Object.entries(routes).map(el => {
			return {
				path: el[0],
				...el[1],
			};
		});
	}

	// match current url with api routes
	function matchRoute(routes, currentPath) {
		let path = currentPath;
		if (!path.startsWith('/')) path = '/' + path;
		if (!path.endsWith('/')) path += '/';

		let url = '';
		let regex = '';
		const urlParts = path.replace(/\//g, '').split('-'); // separate url parts by dash
		const last = urlParts[urlParts.length - 1];
		const secondLast = urlParts[urlParts.length - 2];
		const routeSegments = getUrlSegments(path, {ignoreLang: true});
		const firstSegment = routeSegments[0];
		const secondSegment = routeSegments[1];
		const isDefaultLang = lang.isDefaultLang();
		const langCode = isDefaultLang ? '' : `/${lang.get()}`;

		// homepage api route is '//' so we need to update path to match it
		if (path == '/') path = '//';

		// math current route
		if (routes.find(r => r.path == path)) return path;

		// match view order
		if (routeSegments.length == 3 && firstSegment == 'webshop' && ['orders', 'narocila', 'narudzbe'].includes(secondSegment)) {
			return `${langCode}/${firstSegment}/${secondSegment}/`;
		}

		// match tags
		if (routeSegments.length == 2 && ['tagovi', 'tags', 'tagovi-proizvoda', 'product-tags', 'produkt-tags'].includes(firstSegment)) return `${langCode}/${firstSegment}/%SLUG%-%ID%/`;

		// match catalog detail
		if (isNumber(last) && secondLast == 'proizvod') return `${langCode}/%SLUG%-proizvod-%ID%/`;
		if (isNumber(last) && secondLast == 'izdelek') return `${langCode}/%SLUG%-izdelek-%ID%/`;
		if (isNumber(last) && secondLast == 'product') return `${langCode}/%SLUG%-product-%ID%/`;

		// match publish detail
		regex = /\/[a-zA-Z0-9_+-]+\/[a-zA-Z0-9_+-]+-\d+\/$/;
		if (regex.test(path)) {
			return path.replace(/\/([a-zA-Z0-9_+-]+-\d+)\/$/, '/%SLUG%-%ID%/');
		}

		// Match author detail
		if (__PUBLISH_AUTHORS__ && routeSegments.length <= 2 && ['autori', 'authors'].includes(firstSegment)) return `${langCode}/${firstSegment}/%SLUG%/`;

		// match newsletter subscribe
		if (__NEWSLETTER__) {
			regex = /\d+-[a-zA-Z0-9_+-]+\/$/;
			if (routeSegments.length === 3 && firstSegment == 'newsletter' && regex.test(path)) return path.replace(regex, '%ID%-%CODE%/');
		}

		// match brand detail
		if (routeSegments.length === 3) {
			const limitUrlSegments = isDefaultLang ? 2 : 3;
			url = getUrlSegments(path, {limit: limitUrlSegments, append: '%SLUG%', stringify: true, addSlashes: true});
			if (routes.find(r => r.path == url)) return url;
		}

		// match catalog category url format without prefix (/category-url-<ID>/) or with prefix (/proizvodi/category-url-<ID>/)
		if (__CATALOG_SEO_URL__) {
			if (routeSegments.length === 1 && firstSegment.match(/^(.+)-(\d+)$/)) {
				const url = currentPath.replace(/^\/|\/$/g, '');
				const categoryPath = `${langCode}/%CATSLUG%-%ID%/`;
				const manufacturerPath = `${langCode}/%MANSLUG%-%ID%/`;

				const categoryRoute = routes.find(r => r.path === categoryPath);
				const manufacturerRoute = routes.find(r => r.path === manufacturerPath);

				if (categoryRoute?.urls?.includes(url)) return categoryPath;
				if (manufacturerRoute?.urls?.includes(url)) return manufacturerPath;
				return categoryPath;
			}
		} else if (__CATEGORY_WITHOUT_BASE_URL__) {
			// Find if current url is defined in %CATALOG_CATEGORY_SLUG% route urls. If so, return category path.
			url = `${langCode}/%CATALOG_CATEGORY_SLUG%/`;
			const categoryRoutes = routes.find(r => r.path == url);
			const categoryPath = getUrlSegments(path, {ignoreLang: true, stringify: true});
			const categoryUrl = categoryRoutes?.urls?.find(r => r === categoryPath);
			if (categoryUrl) return url;
		} else if (routeSegments.length > 1) {
			url = `${langCode}/${firstSegment}/%URL%/`;
			if (routes.find(r => r.path === url)) return url;
		}

		// Match payment page (eg. KEKS pay)
		if (['payment-create-quick', 'payment-canceled-quick'].includes(secondSegment)) {
			url = getUrlSegments(path, {limit: 2, stringify: true, addSlashes: true});
			return `${langCode}${url}`;
		}

		// Match create order page (view order quick payment link)
		if (routeSegments.length == 4 && getUrlSegments(path, {ignoreLang: true, limit: 3, stringify: true}) == 'webshop/order/payment') {
			return '/webshop/create-order/';
		}

		// if there is no match, try to match only first route param
		if (routeSegments.length > 1) {
			const limitUrlSegments = isDefaultLang ? 1 : 2;
			url = getUrlSegments(path, {limit: limitUrlSegments, stringify: true, addSlashes: true});
			if (routes.find(r => r.path == url)) return url;
		}
	}

	// fetch all routes from api
	async function fetchRoutes() {
		try {
			const res = await useApi('/api/nuxtapi/routes/');
			if (res.data) {
				setRoutes(res.data);
			}
			return res;
		} catch (err) {
			throw createError({
				statusCode: err.statusCode,
				statusMessage: `Error ${err.statusCode}`,
			});
		}
	}

	// filter only routes for current language
	function filterRoutes(routes) {
		const defaultLang = lang.get();
		const webshopSteps = appConfig.webshop?.steps;

		// Create a Set of excluded actions for O(1) lookup
		const excludedActions = new Set();
		if (webshopSteps?.length) {
			if (!webshopSteps.includes('shipping')) excludedActions.add('shipping');
			if (!webshopSteps.includes('payment')) excludedActions.add('payment');
			if (!webshopSteps.includes('reviewOrder')) excludedActions.add('review_order');
		}

		return routes.filter(route => {
			// Filter by language first
			if (route.lang !== defaultLang) return false;

			// Filter webshop routes if needed
			if (excludedActions.size > 0 && route.controller === 'webshop' && excludedActions.has(route.action)) {
				return false;
			}

			return true;
		});
	}

	// get all routes
	function get() {
		return nuxtApp.$appData?.routes ? nuxtApp.$appData.routes : [];
	}

	// get route from appRoutes. If data is provided, return route with data. Otherwise return route url
	function getAppUrl(key, data = 'url') {
		if (!key || !nuxtApp.$appData.appUrls[key]) {
			useLog(`The "${key}" route does not exist. The "home" route is returned instead.`, 'error');
			return nuxtApp.$appData.appUrls['home'][data];
		}

		return nuxtApp.$appData.appUrls[key][data];
	}

	// get all app routes as a single object
	function getAppUrls() {
		let items = {};
		Object.entries(nuxtApp.$appData.appUrls).forEach(item => {
			items[item[0]] = item[1].url;
		});

		return items;
	}

	function setRoutes(data) {
		const routes = remapRoutes(data); // convert routes to simple array
		const filteredRoutes = filterRoutes(routes); // filter only routes for current language
		nuxtApp.$appData.routes = filteredRoutes; // set routes to appData
		processRoutes(filteredRoutes); // extract appUrls from routes
	}

	function getRoute(path) {
		return nuxtApp.$appData?.routes.find(r => r.path == path);
	}

	return {routes, fetchRoutes, get, getAppUrl, getAppUrls, setRoutes, processRoutes, matchRoute, remapRoutes, getRoute};
}
