export function useMeta() {
	const {onGdprUpdate, gdprApproved} = useGdpr();
	const gdprScriptRegistry = [];
	let gdprWatcher = false;
	const delayedScripts = [];
	let delayListenerAttached = false;

	// Handle delayed script execution on user interaction
	function handleDelayedScripts() {
		if (process.server) return;

		// Execute all delayed scripts
		delayedScripts.forEach(script => {
			// Remove delay flag to prevent infinite loop
			const scriptCopy = {...script};
			delete scriptCopy.delay;
			addScriptImmediate(scriptCopy);
		});

		// Clear the delayed scripts array
		delayedScripts.length = 0;

		// Remove event listeners
		const nuxtEl = document.querySelector('#__nuxt');
		if (nuxtEl) {
			nuxtEl.removeEventListener('pointermove', handleDelayedScripts);
			nuxtEl.removeEventListener('wheel', handleDelayedScripts);
			nuxtEl.removeEventListener('touchstart', handleDelayedScripts);
		}

		delayListenerAttached = false;
	}

	// Attach delay event listeners
	function attachDelayListeners() {
		if (process.server || delayListenerAttached) return;

		const nuxtEl = document.querySelector('#__nuxt');
		if (nuxtEl) {
			nuxtEl.addEventListener('pointermove', handleDelayedScripts);
			nuxtEl.addEventListener('wheel', handleDelayedScripts, {passive: true});
			nuxtEl.addEventListener('touchstart', handleDelayedScripts, {passive: true});
			delayListenerAttached = true;
		}
	}

	function installScript(script) {
		return new Promise((resolve, reject) => {
			if (process.server) return resolve();
			if (!script?.key) {
				useLog('Script key is not defined', 'error');
				return reject(new Error('Script key is not defined'));
			}

			// Check if the script already exists
			if (document.querySelector(`script[data-key="${script.key}"]`)) {
				if (script.onInstall) script.onInstall();
				resolve();
				return;
			}

			// Create a new script element
			const scriptElement = document.createElement(script.noscript ? 'noscript' : 'script');
			scriptElement.setAttribute('data-key', script.key);

			if (script.src) {
				scriptElement.src = script.src;
			} else if (script.innerHTML) {
				scriptElement.innerHTML = script.innerHTML;
			}

			if (script.async) scriptElement.async = script.async;
			if (script.defer) scriptElement.defer = script.defer;
			if (script.id) scriptElement.id = script.id;
			if (script.type) scriptElement.type = script.type;
			if (script.integrity) scriptElement.integrity = script.integrity;
			if (script.crossorigin) scriptElement.crossorigin = script.crossorigin;
			if (script.dataAttributes && typeof script.dataAttributes === 'object') {
				Object.entries(script.dataAttributes).forEach(([key, value]) => {
					scriptElement.setAttribute(`data-${key}`, value);
				});
			}

			scriptElement.onload = () => {
				if (script.onInstall) script.onInstall();
				resolve();
			};

			scriptElement.onerror = error => {
				useLog(`Failed to load script: ${script.key}`, 'error');
				reject(error);
			};

			if (script.noscript) {
				document.body.insertBefore(scriptElement, document.body.firstChild);
			} else {
				let targetElement = document.body;
				if (script.appendTo) {
					const customTarget = document.querySelector(script.appendTo);
					if (customTarget) {
						targetElement = customTarget;
					} else {
						useLog(`Invalid appendTo selector: ${script.appendTo}`, 'warn');
					}
				}
				targetElement.appendChild(scriptElement);
			}
		});
	}

	function uninstallScript(script) {
		if (process.server) return;
		if (!script?.key) return useLog('Script key is not defined', 'error');

		// Remove script from DOM if it exists
		const scriptElement = document.querySelector((script.noscript ? 'noscript' : 'script') + `[data-key="${script.key}"]`);
		if (scriptElement) {
			scriptElement.remove();
			if (script.onUninstall) script.onUninstall();
		}

		// Remove script from delayed scripts array if it exists there
		const delayedIndex = delayedScripts.findIndex(delayedScript => delayedScript.key === script.key);
		if (delayedIndex !== -1) {
			delayedScripts.splice(delayedIndex, 1);

			// If no more delayed scripts remain, remove event listeners
			if (delayedScripts.length === 0 && delayListenerAttached) {
				const nuxtEl = document.querySelector('#__nuxt');
				if (nuxtEl) {
					nuxtEl.removeEventListener('pointermove', handleDelayedScripts);
					nuxtEl.removeEventListener('wheel', handleDelayedScripts);
					nuxtEl.removeEventListener('touchstart', handleDelayedScripts);
				}
				delayListenerAttached = false;
			}
		}
	}

	// Add script immediately without delay
	async function addScriptImmediate(script) {
		if (process.server) return Promise.resolve();
		if (!script) return useLog('Script is not defined', 'error');

		// If GDPR is not required, just add the script
		if (!script.gdpr || gdprApproved(script.gdpr)) {
			await installScript(script);
			return;
		}

		gdprScriptRegistry.push(script);
		if (!gdprWatcher) {
			setGdprWatcher();
			gdprWatcher = true;
		}

		return;
	}

	async function addScript(script) {
		if (process.server) return Promise.resolve();
		if (!script) return useLog('Script is not defined', 'error');

		// Check if script should be delayed until user interaction
		if (script.delay) {
			delayedScripts.push(script);
			attachDelayListeners();
			return Promise.resolve();
		}

		// Execute script immediately
		return addScriptImmediate(script);
	}

	function setGdprWatcher() {
		onGdprUpdate(
			'all',
			data => {
				for (const script of gdprScriptRegistry) {
					if (script.gdprReload) {
						return reloadNuxtApp({ttl: 2000});
					}
					if (data.includes(script.gdpr)) {
						installScript(script);
						if (script?.onGrantConsent) script.onGrantConsent();
					} else {
						uninstallScript(script);
						if (script?.onRevokeConsent) script.onRevokeConsent();
					}
				}
			},
			{immediate: false}
		);
	}

	function waitForWindowProperty(property, callback, options = {}) {
		if (process.server) return;
		if (typeof property !== 'string') throw new Error('Property must be a string representing the key on window');
		if (typeof callback !== 'function') throw new Error('Callback must be a function');

		const {
			intervalTime = 1000, // Time between checks (in milliseconds)
			maxAttempts = 5, // Maximum number of checks before stopping
			onFailure = () => {}, // Optional callback for when the limit is reached
		} = options;

		let attempts = 0;
		const interval = setInterval(() => {
			attempts++;
			if (typeof window !== 'undefined' && window[property] !== undefined) {
				clearInterval(interval);
				callback(window[property]);
			} else if (attempts >= maxAttempts) {
				clearInterval(interval);
				onFailure();
			}
		}, intervalTime);
	}

	return {installScript, uninstallScript, addScript, waitForWindowProperty};
}
