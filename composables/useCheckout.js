export function useCheckout() {
	let bankartHold = useState('bankartHold', () => 0);
	let shippingLeanpayError = useState('shippingLeanpayError', () => 0);
	let additionalPaymentRoute = useState('additionalPaymentRoute', () => 0);
	let additionalPayment = useState('additionalPayment', () => null);
	let orderSuccess = useState('orderSuccess', () => 0);
	let orderSubmit = useState('orderSubmit', () => 0);
	let leanpayData = useState('leanpayData', () => {});
	let paymentError = useState('paymentError', () => null);
	let paymentgateway = useState('paymentgateway', () => {});
	let creditCard = useState('creditCard', () => {});
	let formSubmitCount = useState('formSubmitCount', () => 0);
	let checkoutLoading = useState('checkoutLoading', () => false);
	let customerFields = useState('customerFields', () => {});
	let formValid = useState('formValid', () => 1);
	let newPayment = useState('newPayment', () => null);
	let newPaymentOptions = useState('newPaymentOptions', () => null);
	let newPaymentId = useState('newPaymentId', () => 0);
	let newPaymentCard = useState('newPaymentCard', () => 0);
	let newPaymentInstallment = useState('newPaymentInstallment', () => 1);
	let cartValid = useState('cartValid', () => 1);
	let paymentOptionSelected = useState('paymentOptionSelected', () => 1);
	let cardNumberValid = useState('cardNumberValid', () => 1);
	let cvvValid = useState('cvvValid', () => 1);
	let dateValid = useState('dateValid', () => 1);
	let orderData = useState('orderData', () => null);
	let selectedShipping = useState('selectedShipping', () => null);
	let creation = useState('creation', () => {
		if (typeof window !== 'undefined' && window?.localStorage) {
			const storedValue = window.localStorage.getItem('creation');
			return storedValue ? JSON.parse(storedValue) : false;
		}
		return false;
	});

	const updateCreationState = value => {
		if (typeof window !== 'undefined' && window?.localStorage) {
			localStorage.setItem('creation', JSON.stringify(value));
			creation.value = value;

			// Dispatch a storage event to notify other tabs
			window.dispatchEvent(new Event('storage'));
		}
	};

	return {
		bankartHold,
		shippingLeanpayError,
		additionalPaymentRoute,
		additionalPayment,
		orderSuccess,
		orderSubmit,
		leanpayData,
		paymentError,
		paymentgateway,
		creditCard,
		formSubmitCount,
		checkoutLoading,
		customerFields,
		formValid,
		newPayment,
		newPaymentOptions,
		newPaymentId,
		newPaymentCard,
		newPaymentInstallment,
		cartValid,
		paymentOptionSelected,
		cardNumberValid,
		cvvValid,
		dateValid,
		orderData,
		selectedShipping,
		creation,
		updateCreationState,
	};
}
