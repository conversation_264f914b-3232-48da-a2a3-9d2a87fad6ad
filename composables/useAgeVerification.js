export function useAgeVerification() {
	const ageVerificationModal = useState('ageVerificationModal', () => null);
	const ageCookie = useCookie('xxx_verification', {
		maxAge: 60 * 60 * 24 * 30, // 30 days
		secure: process.env.NODE_ENV == 'development' ? false : true,
	});

	const isAgeModalActive = computed(() => {
		return ageVerificationModal.value !== null;
	});

	const isAdult = computed(() => {
		if(ageCookie.value == true) return true;
		return false;
	});

	function openAgeModal(data) {
		ageVerificationModal.value = data;
	}

	function closeAgeModal() {
		ageVerificationModal.value = null;
	}

	function approveAge() {
		ageCookie.value = true;
		if(ageVerificationModal.value?.url_without_domain) navigateTo(ageVerificationModal.value.url_without_domain);
		closeAgeModal();
	}

	function declineAge() {
		ageCookie.value = false;
		closeAgeModal();
	}

	return {
		isAgeModalActive,
		isAdult,
		approveAge,
		declineAge,
		openAgeModal,
		closeAgeModal,
	};
}