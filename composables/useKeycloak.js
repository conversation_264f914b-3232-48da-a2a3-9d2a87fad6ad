import Keycloak from 'keycloak-js';
export function useKeycloak() {
	const config = useAppConfig();
	const origin = config.host == 'https://www.bigbang.si' || config.host == 'https://beta.bigbang.si' ? true : false;
	const apiToken = useToken();
	const auth = useAuth();
	const scriptLoaded = useState('scriptLoaded', () => false);
	const serviceKeycloak = useState('serviceKeycloak', () => {
		return {
			'url': origin ? 'https://auth.bigbang.si/auth' : 'https://devauth.bigbang.si/auth',
			'username': origin ? 'BigBangPROD' : 'BigBangDEV',
			'client_id': origin ? 'webshop' : 'webshop',
			'disabled_routes': ['payment_on_hold', 'thank_you', 'failed_order', 'failed_payment', 'view_additionalpayment', 'additionalpayments_thank_you', 'additionalpayments_disabled', 'additionalpayments_failed_payment', 'shopping_cart'],
		};
	});

	function objectToFormData(obj, formData, parentKey = null) {
		for (const key in obj) {
			if (Object.hasOwnProperty.call(obj, key)) {
				const value = obj[key];
				const newKey = parentKey ? `${parentKey}[${key}]` : `tokenParsed[${key}]`;

				if (Array.isArray(value)) {
					value.forEach(item => {
						formData.append(`${newKey}[]`, item);
					});
				} else if (typeof value === 'object') {
					objectToFormData(value, formData, newKey);
				} else {
					formData.append(newKey, value);
				}
			}
		}
	}

	async function checkKeycloak() {
		let isUser = false;
		if (auth.isLoggedIn()) {
			isUser = true;
		}

		if (serviceKeycloak.value) {
			var keycloak = new Keycloak({
				url: serviceKeycloak.value.url,
				realm: serviceKeycloak.value.username,
				clientId: serviceKeycloak.value.client_id,
			});
			console.debug('CALL keycloak', isUser);

			keycloak
				.init({
					onLoad: 'check-sso',
					silentCheckSsoRedirectUri: window.location.origin + '/sso/silent-check-sso.html',
					checkLoginIframe: true,
					responseMode: 'query',
				})
				.then(function (authenticated) {
					console.debug('keycloak.authenticated', authenticated);

					if (!isUser) {
						if (authenticated) {
							keycloak
								.loadUserProfile()
								.then(function (profile) {
									console.debug('keycloak authenticated, tokenParsed', keycloak.tokenParsed, 'idToken', keycloak.idToken);
									// redirect to auth
									keycloakUserLogin(keycloak);
								})
								.catch(function () {
									console.debug('keycloak - failed to load user profile');
								});
						}
					} else if (isUser) {
						if (authenticated) {
							keycloak
								.loadUserProfile()
								.then(function (profile) {
									console.debug('keycloak authenticated 2, tokenParsed.exp', keycloak.tokenParsed.exp);
								})
								.catch(function () {
									console.debug('keycloak - failed to load user profile');
								});
						} else {
							keycloakUserLogout(keycloak);
						}
					}
				})
				.catch(function () {
					console.debug('keycloak - failed to initialize');
				});

			keycloak.onTokenExpired = () => {
				keycloakUpdateToken(keycloak);
			};

			keycloak.onAuthLogout = () => {
				keycloakUserLogout(keycloak);
			};

			async function keycloakUserLogin(keycloak) {
				console.debug('CALL keycloakUserLogin');
				const formData = new URLSearchParams();
				formData.append('jwtToken', keycloak.idToken);
				objectToFormData(keycloak.tokenParsed, formData);

				const response = await fetch(`/api/auth/login/`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
					},
					body: formData.toString(),
				});

				const data = await response.json();
				console.debug('response_data', data);

				if (data.response === 'ok') {
					await apiToken.generate();
					const isLoggedin = await auth.fetchIsLogin();
					console.debug('isLoggedin', isLoggedin);
					if (isLoggedin?.user_id) {
						await auth.fetchUser();
					}
				}
			}

			async function keycloakUserLogout(keycloak) {
				console.debug('CALL keycloakUserLogout');
				const response = await fetch(`/api/auth/logout/?response=json`, {
					method: 'POST',
					cache: 'no-cache',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
					},
				});

				const data = await response.json();
				console.debug('keycloakUserLogout response data', data);

				if (data.response === 'ok') {
					await apiToken.generate({tokenForceLogout: true});
					auth.resetUserData();
				}
			}

			function keycloakUpdateToken(keycloak) {
				console.log('token expired, called update');

				//Update the token when will last less than 50 seconds
				keycloak
					.updateToken(50)
					.then(function (refreshed) {
						if (refreshed) {
							console.log('refreshed ' + new Date());
						} else {
							console.log('not refreshed ' + new Date());
						}
					})
					.catch(function () {
						console.debug('keycloak - failed to updateToken');
					});
			}
		}
	}

	return {
		scriptLoaded,
		objectToFormData,
		checkKeycloak,
	};
}
