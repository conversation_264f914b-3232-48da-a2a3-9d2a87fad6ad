export function useChat() {
	let chatWidget = useState('chatWidget', () => false);
	let chatActive = useState('chatActive', () => false);
	let chatSpecial = useState('chatSpecial', () => false);
	let chatLoading = useState('chatLoading', () => false);
	let chatScrollTimer = useState('chatScrollTimer', () => null);

	return {
		chatWidget,
		chatActive,
		chatSpecial,
		chatLoading,
		chatScrollTimer,
	};
}
