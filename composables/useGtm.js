export function useGtm() {
	const config = useAppConfig();
	const trackingConfig = config.google?.tracking;
	const {gdprApproved} = useGdpr();
	const currency = useCurrency();
	const info = useInfo();

	function getEventName(event) {
		if (trackingConfig?.[event]?.name) return trackingConfig?.[event]?.name; // Check if event name is available in tracking config
		if (event === 'gdprConsents') return 'consent_choice';
		if (event === 'pageView') return 'page_view';
		if (event === 'viewItem') return 'view_item';
		if (event === 'viewItemList') return 'view_item_list';
		if (event === 'selectItem') return 'select_item';
		if (event === 'viewCart') return 'view_cart';
		if (event === 'removeProduct') return 'remove_from_cart';
		if (event === 'addProduct') return 'add_to_cart';
		if (event === 'login') return 'login';
		if (event === 'beginCheckout') return 'begin_checkout';
		if (event === 'addShippingInfo') return 'add_shipping_info';
		if (event === 'addPaymentInfo') return 'add_payment_info';
		if (event === 'purchase') return 'purchase';
		if (event === 'refund') return 'refund';
		if (event === 'addToWishlist') return 'add_to_wishlist';
		if (event === 'viewPromotion') return 'view_promotion';
		if (event === 'selectPromotion') return 'select_promotion';
		return event;
	}

	function gtmTrack(event, payload, options = {}) {
		if (process.server) return;

		// Exit if event is not defined
		if (!event || (!options?.custom && !trackingConfig?.events?.includes(event))) return;

		// Base event data
		let data = {
			event: getEventName(event),
		};

		// User login data
		if (event == 'login') {
			data.user_id = payload.user_id;
		}

		// Cms Page data
		if (event === 'pageView') {
			data.page_title = payload.title;
			data.page_path = payload.url;
		}

		if (['addToWishlist', 'addProduct', 'removeProduct'].includes(event)) {
			const extraData = {...payload};
			delete extraData.items;
			data.ecommerce = {
				currency: currency.getCurrency('code'),
				value: formatTotalPrice(payload),
				items: mapProducts(payload.items, extraData),
			};
		}

		if (['viewItemList'].includes(event)) {
			const extraData = {...payload};
			delete extraData.items;
			data.ecommerce = {};
			if (payload?.item_list_id) data.ecommerce.item_list_id = payload.item_list_id;
			if (payload?.item_list_name) data.ecommerce.item_list_name = payload.item_list_name;
			if (payload?.items) data.ecommerce.items = mapProducts(payload.items, extraData);
		}

		if (['selectItem'].includes(event)) {
			const extraData = {...payload};
			delete extraData.items;
			const selectItemListId = payload?.item_list_id || payload.items.category_code || '';
			const selectItemListName = payload?.item_list_name || payload.items.category_title || '';
			data.ecommerce = {
				item_list_id: selectItemListId,
				item_list_name: selectItemListName,
				items: mapProducts(payload.items, extraData),
			};

			// Save select item list data to session storage so we can use it in viewItem event
			if (payload?.item_list_id || payload?.item_list_name) {
				sessionStorage.setItem('gtm_select_item', JSON.stringify({item_list_id: selectItemListId, item_list_name: selectItemListName}));
			} else {
				sessionStorage.removeItem('gtm_select_item');
			}
		}

		if (['viewItem'].includes(event)) {
			const extraData = {...payload};
			delete extraData.items;
			if (sessionStorage.getItem('gtm_select_item')) {
				const selectItemSessionData = JSON.parse(sessionStorage.getItem('gtm_select_item'));
				extraData.item_list_id = selectItemSessionData.item_list_id;
				extraData.item_list_name = selectItemSessionData.item_list_name;
			}
			const viewItemProduct = mapProducts(payload.items, extraData);
			data.ecommerce = {
				currency: currency.getCurrency('code'),
				value: formatPrice(payload.items.price),
				items: viewItemProduct,
			};
		}

		if (['viewPromotion', 'selectPromotion'].includes(event)) {
			const extraData = {...payload};
			delete extraData.items;
			data.ecommerce = {};
			if (payload.promotion_id) data.ecommerce.promotion_id = payload.promotion_id;
			if (payload.promotion_name) data.ecommerce.promotion_name = payload.promotion_name;
			if (payload.creative_name) data.ecommerce.creative_name = payload.creative_name;
			if (payload.creative_slot) data.ecommerce.creative_slot = payload.creative_slot;
			if (payload?.items) data.ecommerce.items = mapProducts(payload.items, extraData);
		}

		if (['viewCart', 'beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'purchase', 'refund'].includes(event)) {
			const cartData = payload.cart;
			const cartItems = getCartProducts(cartData);
			if (!cartItems?.length) return;
			data.ecommerce = {};
			if (cartData) {
				data.ecommerce.currency = currency.getCurrency('code');
				data.ecommerce.value = formatPrice(cartData.total.total_items_total);
			}
			const extraData = {};

			// Add coupon data
			if (['beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'refund'].includes(event)) {
				const coupon = cartData.total?.extraitems?.find(el => el.type === 'coupon');
				data.ecommerce.coupon = coupon?.code || '';
				extraData.coupon = coupon?.code || '';
			}

			// Add shipping data
			if (event == 'addShippingInfo') {
				data.ecommerce.shipping_tier = payload.shipping_tier || '';
			}

			// Add payment data
			if (event == 'addPaymentInfo') {
				data.ecommerce.payment_type = payload.payment_type || '';
			}

			// Purchase data
			if (['purchase'].includes(event)) {
				data.ecommerce.affiliation = trackingConfig.affiliation || removeProtocol(info.getInfo('site_url')) || '';
				data.ecommerce.shipping = formatPrice(cartData.total.total_extra_shipping);
				data.ecommerce.tax = formatPrice(cartData.total.total_tax);
				data.ecommerce.transaction_id = payload.transaction_id || '';
			}

			if (event == 'refund') {
				data.ecommerce.affiliation = trackingConfig.affiliation || removeProtocol(info.getInfo('site_url')) || '';
				data.ecommerce.shipping = formatPrice(cartData.total?.extra_items?.find(el => el.type === 'shipping')?.total || 0);
				data.ecommerce.tax = formatPrice(cartData.total.total_tax);
				data.ecommerce.value = formatPrice(cartData.total?.total_basic || 0);
				data.ecommerce.transaction_id = payload.transaction_id || '';
			}

			data.ecommerce.items = mapProducts(cartItems, extraData);
		}

		// Map data if mapData hook is defined
		if (trackingConfig[event]?.hooks?.mapData) {
			data = trackingConfig[event].hooks.mapData(data);
		}

		// Custom event data
		if (options?.custom) {
			data = {...data, ...payload};
		}

		pushToDataLayer(event, data);
	}

	function getCartProducts(cart) {
		let cartItems = [];
		if (cart.parcels?.length) {
			cart.parcels.forEach(parcel => {
				cartItems.push(...parcel.items);
			});
		}
		return cartItems;
	}

	function mapProducts(products, extraData = {}) {
		if (!products) {
			useLog('Products not provided', 'error');
			return [];
		}

		// Convert single product to array
		if (!Array.isArray(products)) products = [products];

		return products.map((product, index) => {
			let price = 0;
			let discount = 0;
			let variant = '';

			const categories = {};
			if (product.category_parents?.length) {
				product.category_parents.forEach((category, index) => {
					categories[`item_category${index + 2}`] = category.title;
				});
			}

			// Regular product data
			if (product.discount_percent_custom && product.basic_price_custom) {
				discount = product.basic_price_custom - product.price_custom;
			}
			if (product.price_custom) price = product.price_custom;
			if (product.variation?.attributes) variant = product.variation.attributes;

			// Cart data
			if (product.item) {
				if (product.discount_amount) discount = product.discount_amount;
				if (product.unit_price) price = product.unit_price;
				if (product.item?.variation?.attributes) variant = product.item.variation.attributes;
			}

			const productData = {
				affiliation: trackingConfig.affiliation || removeProtocol(info.getInfo('site_url')) || '',
				currency: currency.getCurrency('code'),
				discount: formatPrice(discount),
				item_id: product.item?.id || product.id || '',
				item_code: product.item?.code || product.code || '',
				item_name: product.item?.title || product.title || '',
				index: index,
				item_brand: product.item?.manufacturer?.title || product.manufacturer_title || '',
				item_list_name: product.category_title || '',
				item_list_id: product.category_code || '',
				item_category: product.item?.category?.title || product.category_title || '',
				item_variant: stringifyAttributes(variant),
				price: formatPrice(price),
				quantity: product.quantity || 1,
				...categories,
				...extraData,
			};

			// Custom product mapping hook if defined
			if (trackingConfig?.hooks?.mapProduct) {
				return trackingConfig?.hooks?.mapProduct(product, productData);
			}

			return productData;
		});
	}

	function removeProtocol(url) {
		return url.replace(/^https?:\/\//, '');
	}

	function formatTotalPrice(payload) {
		let price = 0;
		let quantity = 1;

		if (Array.isArray(payload.items)) {
			return payload.items.reduce((total, item) => {
				price = item.price_custom || item.unit_price;
				quantity = payload.quantity || item.quantity;
				return formatPrice(total + price * quantity);
			}, 0);
		}

		price = payload.items.price_custom || payload.items.unit_price;
		quantity = payload.quantity || payload.items.quantity;
		return formatPrice(price * quantity);
	}

	function formatPrice(price) {
		let p = parseFloat(price).toFixed(2);
		return parseFloat(p);
	}

	function stringifyAttributes(attributes) {
		if (!attributes) return '';
		return Object.values(attributes)
			.map(attr => {
				return `${attr.attribute_title}:${attr.title}`;
			})
			.join(',');
	}

	function gtmTrackGdpr(cookie) {
		if (!cookie) return;
		const gdpr = cookie.split('|');
		const analytics = gdpr?.includes('analytics') ? 'granted' : 'denied';
		const marketing = gdpr?.includes('marketing') ? 'granted' : 'denied';
		const eventName = getEventName('gdprConsents');
		const data = {
			'event': eventName,
			'analytics': analytics,
			'marketing': marketing,
			'personalization': marketing,
			'ad_storage': marketing,
			'analytics_storage': analytics,
			'ad_user_data': marketing,
			'ad_personalization': marketing,
		};
		pushToDataLayer(eventName, data, {
			ignoreGdpr: true,
			ignoreEnabled: true,
		});
	}

	// Wait for window to load
	function waitForWindowAndExecute(action, interval = 1000, timeout = 20000) {
		let elapsedTime = 0;

		function checkWindow() {
			if (typeof window !== 'undefined' && typeof window?.dataLayer !== 'undefined' && window?.google_tag_manager?.dataLayer?.gtmDom === true && window?.google_tag_manager?.dataLayer?.gtmLoad === true) {
				action();
			} else if (elapsedTime < timeout) {
				elapsedTime += interval;
				setTimeout(checkWindow, interval);
			}
		}

		checkWindow();
	}

	// Push data to GTM dataLayer
	function pushToDataLayer(event, data, options = {}) {
		if (process.server) return;

		// For custom event, check if gdpr is approved (if defined). For other events, use tracking config
		if (options?.gdpr) {
			if (!gdprApproved(options?.gdpr)) return;
		} else {
			if (trackingConfig.gdpr && !gdprApproved(trackingConfig.gdpr) && !options?.ignoreGdpr) return;
		}
		waitForWindowAndExecute(
			() => {
				if (window?.dataLayer) {
					if (data.ecommerce) dataLayer.push({ecommerce: null});
					dataLayer.push(data);
					if (trackingConfig?.debug) console.log(event, data, dataLayer);
				}
			},
			1000,
			20000
		); // Interval of 1000ms and timeout of 20000ms (20 seconds)
	}

	return {gtmTrackGdpr, gtmTrack};
}
