export function useProductData(item) {
	const auth = useAuth();

	// Make item reactive by converting it to a computed ref if it's not already reactive
	const reactiveItem = computed(() => {
		// If item is already a ref, return its value, otherwise return the item directly
		return unref(item);
	});

	// Dva slikovna badgea koji se prikazuju na iznad slike
	const displayedBadges = computed(() => {
		return reactiveItem.value?.badges_special_1 ? reactiveItem.value?.badges_special_1?.slice(0, 2) : [];
	});

	const textBadges = computed(() => {
		if(!reactiveItem.value?.badges?.length) return [];
		const badges = reactiveItem.value?.badges?.filter(attr => attr.label_title) || [];
		return badges.length > 3 ? badges.slice(0, 3) : badges;
	});

	// Filtered badges with image
	const imageBadges = computed(() => {
		if(!reactiveItem.value?.badges?.length) return [];
		const badges = reactiveItem.value?.badges?.filter(attr => attr.badge_image && !attr.label_title) || [];
		return badges.length > 3 ? badges.slice(0, 3) : badges;
	});

	//badge discount
	const priceSaved = computed(() => {
		const currentItem = reactiveItem.value;
		if (currentItem?.selected_price == 'recommended' && (currentItem?.discount_percent_custom > 0 || currentItem?.price_custom < currentItem?.basic_price_custom)) {
			return currentItem.price_custom - currentItem.loyalty_price_custom;
		} else {
			return currentItem?.basic_price_custom - currentItem?.price_custom;
		}
	});

	//energy image
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['bf000029', 'bf001083', 'bf004128', 'bf004129'];
		return reactiveItem.value?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//conf items label
	const priceFrom = computed(() => {
		const currentItem = reactiveItem.value;
		if (['advanced', 'configurable'].includes(currentItem?.type) && currentItem?.basic_price_custom > currentItem?.price_custom) {
			return true;
		}
		return false;
	});

	//installments
	const installmentPrice = computed(() => {
		const currentItem = reactiveItem.value;
		if (currentItem?.installments_calculation?.regular) {
			const values = Object.values(currentItem.installments_calculation.regular);
			return Math.min(...values);
		}
	});
	const maxInstallments = computed(() => {
		const currentItem = reactiveItem.value;
		if (currentItem?.installments_calculation?.regular) {
			const values = Object.keys(currentItem.installments_calculation.regular);
			return Math.max(...values);
		}
	});

	const isLoyalty = computed(() => {
		const user = auth.getUser();
		return user?.loyalty_code ? true : false;
	});

	// If type of price is set to "Recommended"
	const isRecommendedPrice = computed(() => {
		const currentItem = reactiveItem.value;
		if (currentItem?.selected_price == 'recommended' && (Number(currentItem?.discount_percent_custom) > 0 || Number(currentItem?.price_custom) < Number(currentItem?.basic_price_custom)) && ((!currentItem?.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && currentItem?.loyalty_price_custom && currentItem?.price_custom < currentItem?.loyalty_price_custom))) return true;
		return false;
	});

	const isDiscountPrice = computed(() => {
		const currentItem = reactiveItem.value;
		if ((Number(currentItem?.discount_percent_custom) > 0 || Number(currentItem?.price_custom) < Number(currentItem?.basic_price_custom)) && ((!currentItem?.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && currentItem?.loyalty_price_custom && currentItem?.price_custom < currentItem?.loyalty_price_custom))) return true;
		return false;
	});

	// If type of price is set to "Promotion 2"
	const isPromoPrice = computed(() => {
		const currentItem = reactiveItem.value;
		if (['promotion', 'promotion2'].includes(currentItem?.selected_price) && ((!currentItem?.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && currentItem?.loyalty_price_custom && currentItem?.price_custom < currentItem?.loyalty_price_custom))) return true;
		return false;
	});

	const isLoyaltyPrice = computed(() => {
		const currentItem = reactiveItem.value;
		if (isLoyalty.value && currentItem?.loyalty_price_custom && currentItem?.loyalty_price_custom < currentItem?.basic_price_custom) return true;
		return false;
	});

	const freeShipping = computed(() => {
		const currentItem = reactiveItem.value;
		if (currentItem?.shipping_options?.find(option => option.shipping_price <= 0)) return true;
		return false;
	});

	return {
		displayedBadges,
		imageBadges,
		textBadges,
		priceSaved,
		energyAttr,
		priceFrom,
		installmentPrice,
		maxInstallments,
		isLoyalty,
		isRecommendedPrice,
		isDiscountPrice,
		isPromoPrice,
		isLoyaltyPrice,
		freeShipping
	};
}