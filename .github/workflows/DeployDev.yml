name: Deploy application - sdhr.bigbangmarkerdev.info
on:
  push:
    branches: [dev]
jobs:
  deploy:
    runs-on:
      - runner-001-process
    steps:
      - name: Fetch updated code
        uses: appleboy/ssh-action@v0.1.2
        with:
          host: ${{secrets.SSH_HOST}}
          username: ${{ secrets.SSH_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd /var/www/vhosts/sdhr.bigbangmarkerdev.info
            git checkout dev
            git pull